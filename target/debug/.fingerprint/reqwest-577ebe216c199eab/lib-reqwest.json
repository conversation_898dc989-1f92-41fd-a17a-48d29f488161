{"rustc": 15497389221046826682, "features": "[\"__tls\", \"blocking\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 10900257523021023328, "path": 14436355454594184297, "deps": [[40386456601120721, "percent_encoding", false, 1020218275733132389], [95042085696191081, "ipnet", false, 7773664891254256336], [385810070298638530, "log", false, 10262631209879756345], [411067296443658118, "serde", false, 17890964873241453217], [418947936956741439, "h2", false, 12863321110076963112], [784494742817713399, "tower_service", false, 2078837084231824499], [985115344064483054, "system_configuration", false, 796823120631165864], [1791586607557829099, "serde_json", false, 16540127129777041383], [1811549171721445101, "futures_channel", false, 6746801422087057112], [1906322745568073236, "pin_project_lite", false, 2731696186362346539], [2011830238986063773, "tokio", false, 15929535694821616559], [2517136641825875337, "sync_wrapper", false, 5445691186000838222], [3150220818285335163, "url", false, 15363338077949687400], [4920660634395069245, "hyper_util", false, 6518204466807383863], [5070769681332304831, "once_cell", false, 8061058769325867197], [5695049318159433696, "tower", false, 4033425566730839721], [7620660491849607393, "futures_core", false, 17079716078506851908], [9010263965687315507, "http", false, 10142170707560199720], [10229185211513642314, "mime", false, 15600519187442577407], [10629569228670356391, "futures_util", false, 1094490178943170957], [11957360342995674422, "hyper", false, 5056543972538587309], [12186126227181294540, "tokio_native_tls", false, 10070548931883029947], [13077212702700853852, "base64", false, 5385314962464731783], [14084095096285906100, "http_body", false, 17032765307807311302], [14564311161534545801, "encoding_rs", false, 18391720196261146913], [15032952994102373905, "rustls_pemfile", false, 7758339194535839761], [16066129441945555748, "bytes", false, 758322508367181352], [16542808166767769916, "serde_urlencoded", false, 13475726894923544040], [16785601910559813697, "native_tls_crate", false, 14562033187884404648], [16900715236047033623, "http_body_util", false, 15685780164810793187], [18071510856783138481, "mime_guess", false, 12107096644611143127], [18273243456331255970, "hyper_tls", false, 4305038466073077709]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-577ebe216c199eab/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}