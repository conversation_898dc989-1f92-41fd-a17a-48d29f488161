{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14855836249232253746, "build_script_build", false, 2323352257891900697]], "local": [{"RerunIfChanged": {"output": "debug/build/lzma-sys-0ddc3d622dba0d08/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "LZMA_API_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_NO_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "LIBLZMA_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_ALL_DYNAMIC", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_PATH", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_LIBDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_PKG_CONFIG_SYSROOT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "PKG_CONFIG_SYSROOT_DIR", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}