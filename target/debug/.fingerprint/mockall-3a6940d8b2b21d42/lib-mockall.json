{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"nightly\"]", "target": 16297729212658632601, "profile": 8276155916380437441, "path": 6535723324991939796, "deps": [[2828590642173593838, "cfg_if", false, 3410313713778708879], [3016941897346161952, "downcast", false, 6926781297600932343], [7886665781035375288, "fragile", false, 600365637094547473], [9001202093189684693, "mockall_derive", false, 10711392806018436525], [12516616738327129663, "predicates_tree", false, 902134548413265453], [15863765456528386755, "predicates", false, 9117397196443506835]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/mockall-3a6940d8b2b21d42/dep-lib-mockall", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}