{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":855,"byte_end":867,"line_start":23,"line_end":23,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":855,"byte_end":867,"line_start":23,"line_end":23,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"expected names are: `docsrs`, `feature`, and `test` and 31 more","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unexpected_cfgs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:23:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: expected names are: `docsrs`, `feature`, and `test` and 31 more\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unexpected_cfgs)]` on by default\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1348,"byte_end":1360,"line_start":40,"line_end":40,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1348,"byte_end":1360,"line_start":40,"line_end":40,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:40:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1536,"byte_end":1548,"line_start":49,"line_end":49,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1536,"byte_end":1548,"line_start":49,"line_end":49,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:49:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1745,"byte_end":1757,"line_start":58,"line_end":58,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1745,"byte_end":1757,"line_start":58,"line_end":58,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:58:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m58\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1933,"byte_end":1945,"line_start":67,"line_end":67,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":1933,"byte_end":1945,"line_start":67,"line_end":67,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"#[frb(sync)]","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:67:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":2267,"byte_end":2279,"line_start":82,"line_end":82,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":2267,"byte_end":2279,"line_start":82,"line_end":82,"column_start":5,"column_end":17,"is_primary":false,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:82:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[frb(sync)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":2955,"byte_end":2967,"line_start":113,"line_end":113,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":2955,"byte_end":2967,"line_start":113,"line_end":113,"column_start":5,"column_end":17,"is_primary":false,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:113:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[frb(sync)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unexpected `cfg` condition name: `frb_expand`","code":{"code":"unexpected_cfgs","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":3630,"byte_end":3642,"line_start":144,"line_end":144,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"uplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs","byte_start":3630,"byte_end":3642,"line_start":144,"line_end":144,"column_start":5,"column_end":17,"is_primary":false,"text":[{"text":"    #[frb(sync)]","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[frb]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-1949cf8c6b5b557f/flutter_rust_bridge_macros-2.5.0/src/lib.rs","byte_start":650,"byte_end":718,"line_start":17,"line_end":17,"column_start":1,"column_end":69,"is_primary":false,"text":[{"text":"pub fn frb(attribute: TokenStream, item: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try referring to `frb` crate for guidance on how handle this unexpected cfg","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unexpected `cfg` condition name: `frb_expand`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/features/flutter/dart.rs:144:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    #[frb(sync)]\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: using a cfg inside a attribute macro will use the cfgs from the destination crate and not the ones from the defining crate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: try referring to `frb` crate for guidance on how handle this unexpected cfg\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the attribute macro `frb` may come from an old version of the `flutter_rust_bridge_macros` crate, try updating your dependency with `cargo update -p flutter_rust_bridge_macros`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: see <https://doc.rust-lang.org/nightly/rustc/check-cfg/cargo-specifics.html> for more information about checking conditional configuration\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this warning originates in the attribute macro `frb` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `pf`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs","byte_start":2826,"byte_end":2828,"line_start":78,"line_end":78,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    pf: impl PlatformFunction + 'static,","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs","byte_start":2826,"byte_end":2828,"line_start":78,"line_end":78,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    pf: impl PlatformFunction + 'static,","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":"_pf","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `pf`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs:78:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pf: impl PlatformFunction + 'static,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_pf`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ps`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs","byte_start":3031,"byte_end":3033,"line_start":86,"line_end":86,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    ps: impl PlatformSupplier + 'static,","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs","byte_start":3031,"byte_end":3033,"line_start":86,"line_end":86,"column_start":5,"column_end":7,"is_primary":true,"text":[{"text":"    ps: impl PlatformSupplier + 'static,","highlight_start":5,"highlight_end":7}],"label":null,"suggested_replacement":"_ps","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ps`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/dispatch/cross_platform.rs:86:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ps: impl PlatformSupplier + 'static,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ps`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `i`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":677,"byte_end":678,"line_start":23,"line_end":23,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"        for (i, frame) in bt.frames().iter().enumerate() {","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":677,"byte_end":678,"line_start":23,"line_end":23,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"        for (i, frame) in bt.frames().iter().enumerate() {","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"_i","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `i`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/panic/hook.rs:23:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for (i, frame) in bt.frames().iter().enumerate() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_i`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"trait `PanicWrapper` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":58,"byte_end":70,"line_start":4,"line_end":4,"column_start":11,"column_end":23,"is_primary":true,"text":[{"text":"pub trait PanicWrapper: Send + Sync {","highlight_start":11,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: trait `PanicWrapper` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/panic/hook.rs:4:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub trait PanicWrapper: Send + Sync {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"static `PANIC_WRAPPER` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":172,"byte_end":185,"line_start":8,"line_end":8,"column_start":8,"column_end":21,"is_primary":true,"text":[{"text":"static PANIC_WRAPPER: OnceCell<Box<dyn PanicWrapper>> = OnceCell::new();","highlight_start":8,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: static `PANIC_WRAPPER` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/panic/hook.rs:8:8\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstatic PANIC_WRAPPER: OnceCell<Box<dyn PanicWrapper>> = OnceCell::new();\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `hook_panic` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":246,"byte_end":256,"line_start":10,"line_end":10,"column_start":8,"column_end":18,"is_primary":true,"text":[{"text":"pub fn hook_panic(wrapper: Option<Box<dyn PanicWrapper>>) {","highlight_start":8,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `hook_panic` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/panic/hook.rs:10:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn hook_panic(wrapper: Option<Box<dyn PanicWrapper>>) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `make_panic_test` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"uplus_main_rust/rust_main_uplus/src/panic/hook.rs","byte_start":1267,"byte_end":1282,"line_start":42,"line_end":42,"column_start":8,"column_end":23,"is_primary":true,"text":[{"text":"pub fn make_panic_test() {","highlight_start":8,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `make_panic_test` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0muplus_main_rust/rust_main_uplus/src/panic/hook.rs:42:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn make_panic_test() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"15 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 15 warnings emitted\u001b[0m\n\n"}
