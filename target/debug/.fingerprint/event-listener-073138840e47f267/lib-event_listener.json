{"rustc": 15497389221046826682, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 3883922691551601380, "path": 17343197970161871151, "deps": [[189982446159473706, "parking", false, 3113634032024402410], [1906322745568073236, "pin_project_lite", false, 2731696186362346539], [12100481297174703255, "concurrent_queue", false, 8717310885447846640]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/event-listener-073138840e47f267/dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}