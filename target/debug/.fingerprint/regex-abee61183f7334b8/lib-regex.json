{"rustc": 15497389221046826682, "features": "[\"std\", \"unicode-bool\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 3033921117576893, "path": 5258966399381635768, "deps": [[555019317135488525, "regex_automata", false, 14493729139579348274], [9408802513701742484, "regex_syntax", false, 2159414886849593191]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-abee61183f7334b8/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}