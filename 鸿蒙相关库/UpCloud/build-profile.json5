{
  "app": {
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "release"
      }
    ],
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.cer",
          "storePassword": "0000001A99668C0E5A60CD58E2F9E66A539D190577F730CBF5BDA2FD54FC4CBEE090AC6692596F5C1940",
          "keyAlias": "debugKey",
          "keyPassword": "0000001A294582F293A11AB5D5C768DC06D75723F28B8CA2F8D0A9ED10B7BC0FEB0C6DA2D5A9A2BFAAF2",
          "profile": "/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p12"
        }
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    },
    {
      "name": "upcloud",
      "srcPath": "./upcloud",
    }
  ]
}