[2024-06-21T08:47:51.608] [DEBUG] debug-file - hvigor start cli arguments:{
  prop: [ 'product=default' ],
  sync: true,
  parallel: true,
  incremental: true,
  daemon: true,
  _: [],
  analyze: 'normal'
}
[2024-06-21T08:47:51.642] [DEBUG] debug-file - session manager: set active socket. socketId=U1Z96rGGjBG-7o4AAAAB
[2024-06-21T08:47:51.936] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2024-06-21T08:47:51.945] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2024-06-21T08:47:51.949] [DEBUG] debug-file - Cache service initialization finished in 5 ms 
[2024-06-21T08:47:51.955] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-21T08:47:52.732] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-21T08:47:52.732] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-21T08:47:52.836] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2024-06-21T08:47:52.836] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2024-06-21T08:47:52.836] [DEBUG] debug-file - Product 'default' build option: {}
[2024-06-21T08:47:52.837] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2024-06-21T08:47:52.838] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true
} in this build.
[2024-06-21T08:47:52.848] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5
[2024-06-21T08:47:52.853] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2024-06-21T08:47:52.857] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native
[2024-06-21T08:47:52.860] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2024-06-21T08:47:52.865] [DEBUG] debug-file - Sdk init in 10 ms 
[2024-06-21T08:47:52.877] [DEBUG] debug-file - Project task initialization takes 11 ms 
[2024-06-21T08:47:52.877] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-21T08:47:52.877] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-21T08:47:52.877] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-21T08:47:52.880] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-21T08:47:52.884] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-21T08:47:52.884] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-21T08:47:52.889] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2024-06-21T08:47:52.889] [DEBUG] debug-file - Target 'default' config: {}
[2024-06-21T08:47:52.890] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2024-06-21T08:47:52.890] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
}
[2024-06-21T08:47:52.890] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "name": "default"
}
[2024-06-21T08:47:52.890] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2024-06-21T08:47:52.890] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-21T08:47:52.893] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2024-06-21T08:47:52.893] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-21T08:47:52.893] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-21T08:47:52.893] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-21T08:47:52.896] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-21T08:47:52.899] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-21T08:47:52.900] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-21T08:47:52.919] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=upcloud, buildMode=debug
[2024-06-21T08:47:52.920] [DEBUG] debug-file - Target 'default' config: {}
[2024-06-21T08:47:52.920] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2024-06-21T08:47:52.920] [DEBUG] debug-file - Module 'upcloud' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
}
[2024-06-21T08:47:52.920] [DEBUG] debug-file - Module 'upcloud' target 'ohosTest' build option: {
  "debuggable": true,
  "name": "default"
}
[2024-06-21T08:47:52.920] [DEBUG] debug-file - End initialize module-target build option map, moduleName=upcloud
[2024-06-21T08:47:52.920] [DEBUG] debug-file - Module 'upcloud' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-21T08:47:52.922] [DEBUG] debug-file - Module upcloud task initialization takes 1 ms 
[2024-06-21T08:47:52.922] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-21T08:47:52.922] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-21T08:47:52.922] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-21T08:47:52.959] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 37 ms 
[2024-06-21T08:47:52.959] [DEBUG] debug-file - project has submodules:entry,upcloud
[2024-06-21T08:47:52.959] [DEBUG] debug-file - module:upcloud no need to execute packageHap
[2024-06-21T08:47:52.968] [DEBUG] debug-file - Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.0/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-21T08:47:52.968] [DEBUG] debug-file - Module UpCloud's total dependency: 5
[2024-06-21T08:47:52.969] [DEBUG] debug-file - Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.0/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-21T08:47:52.969] [DEBUG] debug-file - Module entry's total dependency: 5
[2024-06-21T08:47:52.973] [DEBUG] debug-file - Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.0/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-21T08:47:52.973] [DEBUG] debug-file - Module upcloud's total dependency: 5
[2024-06-21T08:47:52.974] [DEBUG] debug-file - Configuration phase cost:1 s 23 ms 
[2024-06-21T08:47:52.976] [DEBUG] debug-file - Configuration task cost before running: 1 s 38 ms 
[2024-06-21T08:47:52.976] [DEBUG] debug-file - Executing task :entry:init
[2024-06-21T08:47:52.976] [DEBUG] debug-file - entry : init cost memory 0.0152587890625
[2024-06-21T08:47:52.976] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 39 ms 
[2024-06-21T08:47:52.976] [INFO] debug-file - Finished :entry:init... after 1 ms 
[2024-06-21T08:47:52.976] [DEBUG] debug-file - Executing task :upcloud:init
[2024-06-21T08:47:52.977] [DEBUG] debug-file - upcloud : init cost memory 0.011749267578125
[2024-06-21T08:47:52.977] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 39 ms 
[2024-06-21T08:47:52.977] [INFO] debug-file - Finished :upcloud:init... after 1 ms 
[2024-06-21T08:47:52.977] [DEBUG] debug-file - Executing task ::init
[2024-06-21T08:47:52.977] [DEBUG] debug-file - UpCloud : init cost memory 0.0122833251953125
[2024-06-21T08:47:52.977] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 39 ms 
[2024-06-21T08:47:52.977] [INFO] debug-file - Finished ::init... after 1 ms 
[2024-06-21T08:47:52.980] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-21T08:47:52.983] [DEBUG] debug-file - Module 'entry' target 'ohosTest' using build option: {
  "debuggable": true,
  "name": "default"
} in this build.
[2024-06-21T08:47:52.986] [DEBUG] debug-file - Module 'upcloud' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-21T08:47:52.989] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2024-06-21T08:47:52.989] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2024-06-21T08:47:52.990] [DEBUG] debug-file - Create  resident worker with id: 0.
[2024-06-21T08:47:52.991] [DEBUG] debug-file - session manager: send message to worker process.
[2024-06-21T08:47:52.991] [DEBUG] debug-file - session manager: send message to worker process.
[2024-06-21T08:47:52.991] [DEBUG] debug-file - Create  resident worker with id: 1.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Cleanup worker 0.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Worker 0 has been cleaned up.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Current idle worker size: 1.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Current resident worker size: 2.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Cleanup worker 1.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Current idle worker size: 0.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - Current resident worker size: 2.
[2024-06-21T08:47:52.992] [DEBUG] debug-file - hvigor build process will be closed.
[2024-06-21T08:47:53.206] [DEBUG] debug-file - worker[0] exits with exit code 0.
[2024-06-21T08:47:53.207] [DEBUG] debug-file - worker[1] exits with exit code 0.
[2024-06-21T08:47:53.207] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2024-06-21T08:47:53.207] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
