[2024-06-25T11:10:29.799] [DEBUG] debug-file - session manager: set active socket. socketId=JyQH3_rtaqOOkpiEAAAB
[2024-06-25T11:10:29.359] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2024-06-25T11:10:29.385] [DEBUG] debug-file - env: daemon=true
[2024-06-25T11:10:29.360] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.0',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2024-06-25T11:10:30.105] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2024-06-25T11:10:30.115] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2024-06-25T11:10:30.121] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2024-06-25T11:10:30.129] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-25T11:10:30.888] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-25T11:10:30.888] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-25T11:10:30.992] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2024-06-25T11:10:30.992] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2024-06-25T11:10:30.992] [DEBUG] debug-file - Product 'default' build option: {}
[2024-06-25T11:10:30.992] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2024-06-25T11:10:30.993] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true
} in this build.
[2024-06-25T11:10:31.004] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5
[2024-06-25T11:10:31.016] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native
[2024-06-25T11:10:31.020] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2024-06-25T11:10:31.024] [DEBUG] debug-file - Sdk init in 11 ms 
[2024-06-25T11:10:31.036] [DEBUG] debug-file - Project task initialization takes 11 ms 
[2024-06-25T11:10:31.036] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-25T11:10:31.036] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-25T11:10:31.036] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts
[2024-06-25T11:10:31.039] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-25T11:10:31.043] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-25T11:10:31.043] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=entry, buildMode=debug
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Target 'default' config: {}
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Module 'entry' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
}
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Module 'entry' target 'ohosTest' build option: {
  "debuggable": true,
  "name": "default"
}
[2024-06-25T11:10:31.049] [DEBUG] debug-file - End initialize module-target build option map, moduleName=entry
[2024-06-25T11:10:31.049] [DEBUG] debug-file - Module 'entry' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-25T11:10:31.052] [DEBUG] debug-file - Module entry task initialization takes 2 ms 
[2024-06-25T11:10:31.052] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-25T11:10:31.052] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-25T11:10:31.053] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts
[2024-06-25T11:10:31.055] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-25T11:10:31.059] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2024-06-25T11:10:31.059] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2024-06-25T11:10:31.077] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=upcloud, buildMode=debug
[2024-06-25T11:10:31.077] [DEBUG] debug-file - Target 'default' config: {}
[2024-06-25T11:10:31.077] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2024-06-25T11:10:31.077] [DEBUG] debug-file - Module 'upcloud' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
}
[2024-06-25T11:10:31.077] [DEBUG] debug-file - Module 'upcloud' target 'ohosTest' build option: {
  "debuggable": true,
  "name": "default"
}
[2024-06-25T11:10:31.077] [DEBUG] debug-file - End initialize module-target build option map, moduleName=upcloud
[2024-06-25T11:10:31.078] [DEBUG] debug-file - Module 'upcloud' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "name": "debug"
} in this build.
[2024-06-25T11:10:31.079] [DEBUG] debug-file - Module upcloud task initialization takes 1 ms 
[2024-06-25T11:10:31.079] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2024-06-25T11:10:31.079] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-25T11:10:31.080] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts
[2024-06-25T11:10:31.119] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 36 ms 
[2024-06-25T11:10:31.120] [DEBUG] debug-file - project has submodules:entry,upcloud
[2024-06-25T11:10:31.120] [DEBUG] debug-file - module:upcloud no need to execute packageHap
[2024-06-25T11:10:31.128] [DEBUG] debug-file - Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-25T11:10:31.128] [DEBUG] debug-file - Module UpCloud's total dependency: 5
[2024-06-25T11:10:31.129] [DEBUG] debug-file - Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-25T11:10:31.129] [DEBUG] debug-file - Module entry's total dependency: 5
[2024-06-25T11:10:31.132] [DEBUG] debug-file - Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js
[2024-06-25T11:10:31.133] [DEBUG] debug-file - Module upcloud's total dependency: 5
[2024-06-25T11:10:31.134] [DEBUG] debug-file - Configuration phase cost:1 s 10 ms 
[2024-06-25T11:10:31.135] [DEBUG] debug-file - Configuration task cost before running: 1 s 28 ms 
[2024-06-25T11:10:31.141] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.141] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.148] [DEBUG] debug-file - Executing task :entry:default@PreBuild
[2024-06-25T11:10:31.150] [DEBUG] debug-file - entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5' has been changed.
[2024-06-25T11:10:31.150] [DEBUG] debug-file - Incremental task entry:default@PreBuild pre-execution cost: 1 ms .
[2024-06-25T11:10:31.150] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2024-06-25T11:10:31.150] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2024-06-25T11:10:31.151] [DEBUG] debug-file - Create  resident worker with id: 0.
[2024-06-25T11:10:31.152] [DEBUG] debug-file - Create  resident worker with id: 1.
[2024-06-25T11:10:31.213] [DEBUG] debug-file - current product is not Atomic service.
[2024-06-25T11:10:31.213] [DEBUG] debug-file - Use tool [darwin: JAVA_HOME, CLASSPATH]
 [
  {
    JAVA_HOME: '/Applications/DevEco-Studio.app/Contents/jbr/Contents/Home'
  },
  { CLASSPATH: undefined }
]
[2024-06-25T11:10:31.213] [DEBUG] debug-file - Use tool [darwin: NODE_HOME]
 [
  {
    NODE_HOME: '/Applications/DevEco-Studio.app/Contents/tools/node/bin'
  }
]
[2024-06-25T11:10:31.214] [DEBUG] debug-file - entry : default@PreBuild cost memory 2.9893951416015625
[2024-06-25T11:10:31.214] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 107 ms 
[2024-06-25T11:10:31.215] [INFO] debug-file - Finished :entry:default@PreBuild... after 66 ms 
[2024-06-25T11:10:31.216] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.216] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.216] [DEBUG] debug-file - Executing task :entry:default@GenerateMetadata
[2024-06-25T11:10:31.217] [DEBUG] debug-file - entry:default@GenerateMetadata is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5' has been changed.
[2024-06-25T11:10:31.217] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .
[2024-06-25T11:10:31.218] [DEBUG] debug-file - entry : default@GenerateMetadata cost memory 0.15052032470703125
[2024-06-25T11:10:31.219] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 112 ms 
[2024-06-25T11:10:31.219] [INFO] debug-file - Finished :entry:default@GenerateMetadata... after 3 ms 
[2024-06-25T11:10:31.220] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.220] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.221] [DEBUG] debug-file - Executing task :entry:default@ConfigureCmake
[2024-06-25T11:10:31.221] [DEBUG] debug-file - entry : default@ConfigureCmake cost memory 0.0131378173828125
[2024-06-25T11:10:31.221] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 114 ms 
[2024-06-25T11:10:31.221] [INFO] debug-file - Finished :entry:default@ConfigureCmake... after 1 ms 
[2024-06-25T11:10:31.222] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.222] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.224] [DEBUG] debug-file - Executing task :entry:default@MergeProfile
[2024-06-25T11:10:31.224] [DEBUG] debug-file - entry:default@MergeProfile is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5' has been changed.
[2024-06-25T11:10:31.224] [DEBUG] debug-file - Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .
[2024-06-25T11:10:31.224] [DEBUG] debug-file - Change app api release type with 'Canary4'
[2024-06-25T11:10:31.225] [DEBUG] debug-file - Change app compile API version with '********'
[2024-06-25T11:10:31.225] [DEBUG] debug-file - Change app target API version with '50000012'
[2024-06-25T11:10:31.225] [DEBUG] debug-file - Change app minimum API version with '50000012'
[2024-06-25T11:10:31.225] [DEBUG] debug-file - Use cli appEnvironment
[2024-06-25T11:10:31.231] [DEBUG] debug-file - entry : default@MergeProfile cost memory 0.26984405517578125
[2024-06-25T11:10:31.231] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 124 ms 
[2024-06-25T11:10:31.231] [INFO] debug-file - Finished :entry:default@MergeProfile... after 8 ms 
[2024-06-25T11:10:31.232] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.232] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.233] [DEBUG] debug-file - Executing task :entry:default@CreateBuildProfile
[2024-06-25T11:10:31.234] [DEBUG] debug-file - entry:default@CreateBuildProfile is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5' has been changed.
[2024-06-25T11:10:31.234] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .
[2024-06-25T11:10:31.234] [DEBUG] debug-file - entry : default@CreateBuildProfile cost memory 0.107757568359375
[2024-06-25T11:10:31.235] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 128 ms 
[2024-06-25T11:10:31.235] [INFO] debug-file - Finished :entry:default@CreateBuildProfile... after 2 ms 
[2024-06-25T11:10:31.236] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.236] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.236] [DEBUG] debug-file - Executing task :entry:default@PreCheckSyscap
[2024-06-25T11:10:31.236] [DEBUG] debug-file - entry : default@PreCheckSyscap cost memory 0.01342010498046875
[2024-06-25T11:10:31.236] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 130 ms 
[2024-06-25T11:10:31.237] [INFO] debug-file - Finished :entry:default@PreCheckSyscap... after 1 ms 
[2024-06-25T11:10:31.237] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.237] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.238] [DEBUG] debug-file - Executing task :entry:default@ProcessIntegratedHsp
[2024-06-25T11:10:31.239] [DEBUG] debug-file - entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.
[2024-06-25T11:10:31.239] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .
[2024-06-25T11:10:31.239] [DEBUG] debug-file - entry : default@ProcessIntegratedHsp cost memory 0.08824920654296875
[2024-06-25T11:10:31.239] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 132 ms 
[2024-06-25T11:10:31.239] [INFO] debug-file - Finished :entry:default@ProcessIntegratedHsp... after 2 ms 
[2024-06-25T11:10:31.240] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.240] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.241] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithCmake
[2024-06-25T11:10:31.241] [DEBUG] debug-file - entry : default@BuildNativeWithCmake cost memory 0.01434326171875
[2024-06-25T11:10:31.241] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 134 ms 
[2024-06-25T11:10:31.241] [INFO] debug-file - Finished :entry:default@BuildNativeWithCmake... after 1 ms 
[2024-06-25T11:10:31.242] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.242] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.242] [DEBUG] debug-file - Executing task :entry:default@MakePackInfo
[2024-06-25T11:10:31.243] [DEBUG] debug-file - entry:default@MakePackInfo is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5' has been changed.
[2024-06-25T11:10:31.243] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .
[2024-06-25T11:10:31.244] [DEBUG] debug-file - Module Pack Info:  {
  summary: {
    app: {
      bundleName: 'com.haier.uhome.upcloud',
      bundleType: 'app',
      version: [Object]
    },
    modules: [ [Object] ]
  },
  packages: [
    {
      deviceType: [Array],
      moduleType: 'entry',
      deliveryWithInstall: true,
      name: 'entry-default'
    }
  ]
}
[2024-06-25T11:10:31.244] [DEBUG] debug-file - entry : default@MakePackInfo cost memory 0.1568145751953125
[2024-06-25T11:10:31.244] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 138 ms 
[2024-06-25T11:10:31.245] [INFO] debug-file - Finished :entry:default@MakePackInfo... after 2 ms 
[2024-06-25T11:10:31.246] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.246] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.247] [DEBUG] debug-file - Executing task :entry:default@ProcessProfile
[2024-06-25T11:10:31.247] [DEBUG] debug-file - entry:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/merge_profile/default/module.json' has been changed.
[2024-06-25T11:10:31.247] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .
[2024-06-25T11:10:31.248] [DEBUG] debug-file - [
  '/Applications/DevEco-Studio.app/Contents/tools/node/bin/node',
  '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/ets/build-tools/ets-loader/bin/ark/ts2abc.js',
  '--target-api-version',
  '12'
]
[2024-06-25T11:10:31.300] [DEBUG] debug-file - ********
[2024-06-25T11:10:31.303] [DEBUG] debug-file - entry : default@ProcessProfile cost memory 0.23760223388671875
[2024-06-25T11:10:31.303] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 196 ms 
[2024-06-25T11:10:31.303] [INFO] debug-file - Finished :entry:default@ProcessProfile... after 57 ms 
[2024-06-25T11:10:31.306] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.306] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.307] [DEBUG] debug-file - Executing task :entry:default@SyscapTransform
[2024-06-25T11:10:31.321] [DEBUG] debug-file - entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.
[2024-06-25T11:10:31.322] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform pre-execution cost: 15 ms .
[2024-06-25T11:10:31.322] [DEBUG] debug-file - entry : default@SyscapTransform cost memory 0.084808349609375
[2024-06-25T11:10:31.322] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 215 ms 
[2024-06-25T11:10:31.322] [INFO] debug-file - Finished :entry:default@SyscapTransform... after 16 ms 
[2024-06-25T11:10:31.323] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.323] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.323] [DEBUG] debug-file - Executing task :entry:default@BuildNativeWithNinja
[2024-06-25T11:10:31.324] [DEBUG] debug-file - entry : default@BuildNativeWithNinja cost memory 0.03060150146484375
[2024-06-25T11:10:31.324] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 217 ms 
[2024-06-25T11:10:31.324] [INFO] debug-file - Finished :entry:default@BuildNativeWithNinja... after 1 ms 
[2024-06-25T11:10:31.325] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.325] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.328] [DEBUG] debug-file - restool module names: entry,upcloud; moduleName=entry, taskName=default@ProcessResource
[2024-06-25T11:10:31.330] [DEBUG] debug-file - Executing task :entry:default@ProcessResource
[2024-06-25T11:10:31.331] [DEBUG] debug-file - Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .
[2024-06-25T11:10:31.331] [DEBUG] debug-file - entry : default@ProcessResource cost memory 0.102386474609375
[2024-06-25T11:10:31.332] [INFO] debug-file - UP-TO-DATE :entry:default@ProcessResource...  
[2024-06-25T11:10:31.333] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.333] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.337] [DEBUG] debug-file - Executing task :entry:default@ProcessLibs
[2024-06-25T11:10:31.338] [DEBUG] debug-file - entry:default@ProcessLibs is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5' has been changed.
[2024-06-25T11:10:31.338] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .
[2024-06-25T11:10:31.339] [DEBUG] debug-file - default@ProcessLibs work[0] is submitted.
[2024-06-25T11:10:31.340] [DEBUG] debug-file - default@ProcessLibs work[0] is pushed to ready queue.
[2024-06-25T11:10:31.340] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.340] [DEBUG] debug-file - Create  resident worker with id: 6.
[2024-06-25T11:10:31.340] [DEBUG] debug-file - default@ProcessLibs work[0] has been dispatched to worker[6].
[2024-06-25T11:10:31.340] [DEBUG] debug-file - default@ProcessLibs work[0] is dispatched.
[2024-06-25T11:10:31.341] [DEBUG] debug-file - entry : default@ProcessLibs cost memory 0.6436538696289062
[2024-06-25T11:10:31.706] [DEBUG] debug-file - worker[6] has one work done.
[2024-06-25T11:10:31.706] [DEBUG] debug-file - default@ProcessLibs work[0] done.
[2024-06-25T11:10:31.707] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.707] [INFO] debug-file - Finished :entry:default@ProcessLibs... after 370 ms 
[2024-06-25T11:10:31.708] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.708] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.709] [DEBUG] debug-file - Executing task :entry:default@DoNativeStrip
[2024-06-25T11:10:31.710] [DEBUG] debug-file - Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .
[2024-06-25T11:10:31.710] [DEBUG] debug-file - entry : default@DoNativeStrip cost memory 0.04639434814453125
[2024-06-25T11:10:31.710] [INFO] debug-file - UP-TO-DATE :entry:default@DoNativeStrip...  
[2024-06-25T11:10:31.711] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.711] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.714] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgContextInfo
[2024-06-25T11:10:31.715] [DEBUG] debug-file - entry : default@GeneratePkgContextInfo cost memory 0.031341552734375
[2024-06-25T11:10:31.715] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 608 ms 
[2024-06-25T11:10:31.715] [INFO] debug-file - Finished :entry:default@GeneratePkgContextInfo... after 1 ms 
[2024-06-25T11:10:31.715] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.716] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.716] [DEBUG] debug-file - Executing task :entry:default@CacheNativeLibs
[2024-06-25T11:10:31.717] [DEBUG] debug-file - Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .
[2024-06-25T11:10:31.717] [DEBUG] debug-file - entry : default@CacheNativeLibs cost memory 0.0537872314453125
[2024-06-25T11:10:31.717] [INFO] debug-file - UP-TO-DATE :entry:default@CacheNativeLibs...  
[2024-06-25T11:10:31.718] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.718] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.718] [DEBUG] debug-file - Executing task :entry:default@ProcessRouterMap
[2024-06-25T11:10:31.721] [DEBUG] debug-file - entry:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/oh-package.json5' has been changed.
[2024-06-25T11:10:31.721] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .
[2024-06-25T11:10:31.722] [DEBUG] debug-file - entry : default@ProcessRouterMap cost memory 0.45525360107421875
[2024-06-25T11:10:31.722] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 615 ms 
[2024-06-25T11:10:31.723] [INFO] debug-file - Finished :entry:default@ProcessRouterMap... after 4 ms 
[2024-06-25T11:10:31.724] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.724] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.724] [DEBUG] debug-file - restool module names: entry,upcloud; moduleName=entry, taskName=default@CompileResource
[2024-06-25T11:10:31.726] [DEBUG] debug-file - Executing task :entry:default@CompileResource
[2024-06-25T11:10:31.727] [DEBUG] debug-file - entry:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/resources' has been changed.
[2024-06-25T11:10:31.727] [DEBUG] debug-file - Incremental task entry:default@CompileResource pre-execution cost: 2 ms .
[2024-06-25T11:10:31.728] [DEBUG] debug-file - Use tool [/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/restool]
 [
  '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/restool',
  '-l',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resConfig.json'
]
[2024-06-25T11:10:31.729] [DEBUG] debug-file - current process  memoryUsage: {
  rss: 326615040,
  heapTotal: 138952704,
  heapUsed: 111951952,
  external: 1459397,
  arrayBuffers: 149599
} os memoryUsage :15.892181396484375
[2024-06-25T11:10:31.766] [DEBUG] debug-file - Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/base/element/string.json
but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/resources/base/element/string.json
Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/en_US/element/string.json
but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/resources/en_US/element/string.json
Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/zh_CN/element/string.json
but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/resources/zh_CN/element/string.json

[2024-06-25T11:10:31.767] [DEBUG] debug-file - Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/base/element/string.json
but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/resources/base/element/string.json
Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/en_US/element/string.json
but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/resources/en_US/element/string.json
Warning: 'page_show' conflict, first declared.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources/zh_CN/element/string.json

[2024-06-25T11:10:31.767] [DEBUG] debug-file - but declared again.
at /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/resources/zh_CN/element/string.json

[2024-06-25T11:10:31.769] [DEBUG] debug-file - Warning: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/resources/base/media/layered_image.json is not png format

[2024-06-25T11:10:31.769] [DEBUG] debug-file - Info: restool resources compile success.

[2024-06-25T11:10:31.771] [DEBUG] debug-file - entry : default@CompileResource cost memory 0.7418365478515625
[2024-06-25T11:10:31.771] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 664 ms 
[2024-06-25T11:10:31.771] [INFO] debug-file - Finished :entry:default@CompileResource... after 46 ms 
[2024-06-25T11:10:31.772] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.772] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.774] [DEBUG] debug-file - Executing task :entry:default@GenerateLoaderJson
[2024-06-25T11:10:31.779] [DEBUG] debug-file - entry:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/mock/mock-config.json5' has been changed.
[2024-06-25T11:10:31.779] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson pre-execution cost: 3 ms .
[2024-06-25T11:10:31.781] [DEBUG] debug-file - entry : default@GenerateLoaderJson cost memory 0.8412246704101562
[2024-06-25T11:10:31.781] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 674 ms 
[2024-06-25T11:10:31.790] [INFO] debug-file - Finished :entry:default@GenerateLoaderJson... after 8 ms 
[2024-06-25T11:10:31.791] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.791] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.794] [DEBUG] debug-file - Executing task :entry:default@CompileArkTS
[2024-06-25T11:10:31.797] [DEBUG] debug-file - entry:default@CompileArkTS is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default' has been changed.
[2024-06-25T11:10:31.797] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS pre-execution cost: 2 ms .
[2024-06-25T11:10:31.801] [DEBUG] debug-file - build config:
[2024-06-25T11:10:31.801] [DEBUG] debug-file - {
  moduleType: 'entry',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/local.properties',
  isPreview: false,
  isOhosTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile',
  etsLoaderPath: '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/ets/build-tools/ets-loader',
  modulePath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud',
  compileSdkVersion: 12,
  compatibleSdkVersion: 12,
  bundleName: 'com.haier.uhome.upcloud',
  etsLoaderVersion: '********',
  etsLoaderReleaseType: 'Canary4',
  aotCompileMode: 'type',
  apPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/modules.ap',
  entryModuleName: 'entry',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'entry',
  allModuleNameHash: 'c5bcc0957e71911e41d0d74ae356c6fd',
  externalApiPaths: [
    '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/hms/ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  buildGeneratedProfilePath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default',
  bundleType: 'app',
  arkTSVersion: undefined,
  apiVersion: 12,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: undefined,
  buildDir: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build',
  deviceTypes: [ 'phone', 'tablet', '2in1' ],
  moduleDependencies: { dependency: {} },
  projectDependencies: {
    '@ohos/hamock': '1.0.0',
    '@ohos/httpclient': '^2.0.1-rc.7',
    '@ohos/hypium': '1.0.18',
    '@ohos/retrofit': '^2.0.1-rc.0',
    dependency: {}
  },
  useNormalizedOHMUrl: false,
  pkgContextInfo: undefined,
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: {
    requestPermissions: [ [Object], [Object] ],
    definePermissions: undefined
  },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: false,
  aceModuleJsonPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json',
  appResource: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt',
  rawFileResource: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile',
  resourceTableHash: 'cdb5c08842b88666c87081531eb24111',
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:12:********:Canary4',
  aceModuleRoot: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/supervisual',
  aceBuildJson: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/loader.json',
  cachePath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/cache/default/default@CompileArkTS/esmodule/debug',
  aceModuleBuild: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets',
  supportChunks: true,
  pkgNameToPkgBriefInfo: {
    '@ohos/retrofit': {
      pkgRoot: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit',
      pkgName: '@ohos/retrofit',
      sourceRoots: undefined
    },
    '@ohos/httpclient': {
      pkgRoot: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient',
      pkgName: '@ohos/httpclient',
      sourceRoots: undefined
    },
    '@ohos/crypto-js': {
      pkgRoot: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js',
      pkgName: '@ohos/crypto-js',
      sourceRoots: undefined
    },
    entry: {
      pkgRoot: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'entry'
    }
  },
  projectModel: {
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main': {
      moduleName: 'entry',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/src/ohosTest/ets': {
      moduleName: 'entry_test',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/ohosTest': {
      moduleName: 'entry_test',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry': {
      moduleName: 'entry',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/main': {
      moduleName: 'upcloud',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/build/default/intermediates/src/ohosTest/ets': {
      moduleName: 'upcloud',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest': {
      moduleName: 'upcloud',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud': {
      moduleName: 'upcloud',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud'
    },
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud': {
      moduleName: 'UpCloud',
      modulePkgPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud'
    }
  },
  pkgJsonFileHash: '6cb20063dd52db7424d2efc34873efc0',
  allModulePaths: [
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry',
    '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud'
  ],
  routerMap: {},
  obfuscationOptions: undefined,
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/mock/mock-config.json5',
    mockConfigKey2ModuleInfo: {}
  },
  otherPaths: {
    'entry/*': [ '../*', '../../../build/default/generated/profile/default/*' ]
  },
  collectImportersConfig: {
    relativeDir: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets',
    buildConfigJsonPath: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/config/buildConfig.json',
    isReload: false
  }
}
[2024-06-25T11:10:31.802] [DEBUG] debug-file - Compile arkts with external api path: /Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/hms/ets
[2024-06-25T11:10:31.802] [DEBUG] debug-file - default@CompileArkTS work[1] is submitted.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - default@CompileArkTS work[1] is pushed to ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[3] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[2] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.802] [DEBUG] debug-file - A work dispatched to worker[1] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[1] has been dispatched to worker[0].
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[1] is dispatched.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - CopyResources startTime: 9657438996166
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[2] is submitted.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[2] is pushed to ready queue.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[2] has been dispatched to worker[6].
[2024-06-25T11:10:31.803] [DEBUG] debug-file - default@CompileArkTS work[2] is dispatched.
[2024-06-25T11:10:31.803] [DEBUG] debug-file - entry : default@CompileArkTS cost memory 1.338531494140625
[2024-06-25T11:10:31.804] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:31.804] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:31.806] [DEBUG] debug-file - Executing task :entry:default@BuildJS
[2024-06-25T11:10:31.807] [DEBUG] debug-file - entry : default@BuildJS cost memory 0.10791778564453125
[2024-06-25T11:10:31.807] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 700 ms 
[2024-06-25T11:10:31.807] [INFO] debug-file - Finished :entry:default@BuildJS... after 2 ms 
[2024-06-25T11:10:31.812] [DEBUG] debug-file - worker[6] has one work done.
[2024-06-25T11:10:31.812] [DEBUG] debug-file - CopyResources is end, endTime: 9657448139041
[2024-06-25T11:10:31.812] [DEBUG] debug-file - default@CompileArkTS work[2] done.
[2024-06-25T11:10:31.812] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2024-06-25T11:10:35.454] [DEBUG] debug-file - worker[0] has one work done.
[2024-06-25T11:10:35.457] [DEBUG] debug-file - default@CompileArkTS work[1] done.
[2024-06-25T11:10:35.457] [DEBUG] debug-file - A work dispatched to worker[0] failed because unable to get work from ready queue.
[2024-06-25T11:10:35.458] [INFO] debug-file - Finished :entry:default@CompileArkTS... after 3 s 663 ms 
[2024-06-25T11:10:35.459] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:35.459] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:35.460] [DEBUG] debug-file - Executing task :entry:default@GeneratePkgModuleJson
[2024-06-25T11:10:35.461] [DEBUG] debug-file - entry:default@GeneratePkgModuleJson is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json' has been changed.
[2024-06-25T11:10:35.461] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .
[2024-06-25T11:10:35.461] [DEBUG] debug-file - entry : default@GeneratePkgModuleJson cost memory 0.06391143798828125
[2024-06-25T11:10:35.461] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 354 ms 
[2024-06-25T11:10:35.461] [INFO] debug-file - Finished :entry:default@GeneratePkgModuleJson... after 1 ms 
[2024-06-25T11:10:35.463] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:35.463] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:35.466] [DEBUG] debug-file - Executing task :entry:default@PackageHap
[2024-06-25T11:10:35.468] [DEBUG] debug-file - entry:default@PackageHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json' has been changed.
[2024-06-25T11:10:35.468] [DEBUG] debug-file - Incremental task entry:default@PackageHap pre-execution cost: 2 ms .
[2024-06-25T11:10:35.470] [DEBUG] debug-file - Use tool [/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/lib/app_packing_tool.jar]
 [
  'java',
  '-Dfile.encoding=utf-8',
  '-jar',
  '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/lib/app_packing_tool.jar',
  '--mode',
  'hap',
  '--force',
  'true',
  '--lib-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default',
  '--json-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/package/default/module.json',
  '--resources-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources',
  '--index-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index',
  '--pack-info-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info',
  '--out-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap',
  '--ets-path',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets'
]
[2024-06-25T11:10:35.471] [DEBUG] debug-file - default@PackageHap work[3] is submitted.
[2024-06-25T11:10:35.471] [DEBUG] debug-file - default@PackageHap work[3] is pushed to ready queue.
[2024-06-25T11:10:35.471] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2024-06-25T11:10:35.471] [DEBUG] debug-file - default@PackageHap work[3] has been dispatched to worker[6].
[2024-06-25T11:10:35.471] [DEBUG] debug-file - default@PackageHap work[3] is dispatched.
[2024-06-25T11:10:35.472] [DEBUG] debug-file - entry : default@PackageHap cost memory 0.723358154296875
[2024-06-25T11:10:35.736] [DEBUG] debug-file - worker[6] has one work done.
[2024-06-25T11:10:35.736] [DEBUG] debug-file - default@PackageHap work[3] done.
[2024-06-25T11:10:35.736] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2024-06-25T11:10:35.737] [INFO] debug-file - Finished :entry:default@PackageHap... after 270 ms 
[2024-06-25T11:10:35.739] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2024-06-25T11:10:35.739] [DEBUG] debug-file - jsonObjWithoutParam {"name":"entry","version":"1.0.0","description":"Please describe the basic information.","main":"","author":"","license":"","dependencies":{}} at undefined
[2024-06-25T11:10:35.740] [DEBUG] debug-file - Executing task :entry:default@SignHap
[2024-06-25T11:10:35.742] [DEBUG] debug-file - entry:default@SignHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed.
[2024-06-25T11:10:35.742] [DEBUG] debug-file - Incremental task entry:default@SignHap pre-execution cost: 1 ms .
[2024-06-25T11:10:35.753] [DEBUG] debug-file - Use tool [/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/lib/hap-sign-tool.jar]
 [
  'java',
  '-jar',
  '/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/lib/hap-sign-tool.jar',
  'sign-app',
  '-mode',
  'localSign',
  '-keystoreFile',
  '/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p12',
  '-keystorePwd',
  '******',
  '-keyAlias',
  'debugKey',
  '-keyPwd',
  '******',
  '-signAlg',
  'SHA256withECDSA',
  '-profileFile',
  '/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p7b',
  '-appCertFile',
  '/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.cer',
  '-inFile',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap',
  '-outFile',
  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap'
]
[2024-06-25T11:10:36.350] [DEBUG] debug-file - java daemon socket received message:{"code":0,"message":"verify profile success"}
[2024-06-25T11:10:36.354] [DEBUG] debug-file - java daemon socket close code:1000 reason:close by user
[2024-06-25T11:10:36.396] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - certificate in profile: 青岛海尔科技有限公司(1301408258556180289)\\

[2024-06-25T11:10:36.413] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Start to sign code.

[2024-06-25T11:10:36.422] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - /Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p12 is exist. Try to load it with given passwd

[2024-06-25T11:10:36.513] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Create a sign info successfully.

[2024-06-25T11:10:36.518] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - No native libs.

[2024-06-25T11:10:36.518] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Sign successfully.

[2024-06-25T11:10:36.523] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Add sign data in sign info list success.

[2024-06-25T11:10:36.525] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Generate signing block success, begin write it to output file

[2024-06-25T11:10:36.526] [DEBUG] debug-file - on stdout: 06-25 11:10:36 INFO  - Sign Hap success!

[2024-06-25T11:10:36.527] [DEBUG] debug-file - java daemon socket received message:{"code":0,"message":"sign app success"}
[2024-06-25T11:10:36.528] [DEBUG] debug-file - entry : default@SignHap cost memory 1.097625732421875
[2024-06-25T11:10:36.528] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 421 ms 
[2024-06-25T11:10:36.528] [INFO] debug-file - Finished :entry:default@SignHap... after 788 ms 
[2024-06-25T11:10:36.530] [DEBUG] debug-file - Executing task :entry:assembleHap
[2024-06-25T11:10:36.530] [DEBUG] debug-file - entry : assembleHap cost memory 0.01153564453125
[2024-06-25T11:10:36.530] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 423 ms 
[2024-06-25T11:10:36.530] [INFO] debug-file - Finished :entry:assembleHap... after 1 ms 
[2024-06-25T11:10:36.532] [DEBUG] debug-file - BUILD SUCCESSFUL in 6 s 425 ms 
[2024-06-25T11:10:36.532] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5 cache from map.
[2024-06-25T11:10:36.532] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5 cache by regenerate.
[2024-06-25T11:10:36.532] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.532] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigor/hvigor-config.json5 cache by regenerate.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@PreBuild input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/oh-package.json5 cache by regenerate.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Incremental task entry:default@PreBuild post-execution cost:1 ms .
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@GenerateMetadata input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5 cache from map.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@GenerateMetadata output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/hap_metadata/default/output_metadata.json cache.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Incremental task entry:default@GenerateMetadata post-execution cost:1 ms .
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5 cache from map.
[2024-06-25T11:10:36.533] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5 cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/module.json cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/module.json cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@MergeProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/module.json cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@MergeProfile output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/merge_profile/default/module.json cache.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Incremental task entry:default@MergeProfile post-execution cost:1 ms .
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5 cache from map.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@CreateBuildProfile output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json cache.
[2024-06-25T11:10:36.534] [DEBUG] debug-file - Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@MakePackInfo input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/app.json5 cache from map.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@MakePackInfo input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5 cache by regenerate.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@MakePackInfo input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@MakePackInfo input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/resources/base/profile/backup_config.json cache by regenerate.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@MakePackInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info cache.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Incremental task entry:default@MakePackInfo post-execution cost:1 ms .
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@ProcessProfile input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/merge_profile/default/module.json cache from map.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@ProcessProfile output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/process_profile/default/module.json cache.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Incremental task entry:default@ProcessProfile post-execution cost:1 ms .
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/toolchains/syscap_tool cache by regenerate.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/HarmonyOS-NEXT-DB1/openharmony/ets/api/device-define cache by regenerate.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@SyscapTransform output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc cache.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Incremental task entry:default@SyscapTransform post-execution cost:1 ms .
[2024-06-25T11:10:36.535] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.
[2024-06-25T11:10:36.535] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/libs cache by regenerate.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/libs cache by regenerate.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/libs cache by regenerate.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5 cache from map.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@ProcessLibs input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build-profile.json5 cache by regenerate.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@ProcessLibs output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/libs/default cache.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Incremental task entry:default@ProcessLibs post-execution cost:1 ms .
[2024-06-25T11:10:36.536] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.
[2024-06-25T11:10:36.536] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2024-06-25T11:10:36.536] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.
[2024-06-25T11:10:36.537] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/oh-package.json5 cache from map.
[2024-06-25T11:10:36.537] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh-package.json5 cache by regenerate.
[2024-06-25T11:10:36.537] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/module.json5 cache by regenerate.
[2024-06-25T11:10:36.538] [DEBUG] debug-file - Update task entry:default@ProcessRouterMap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/loader.json cache.
[2024-06-25T11:10:36.538] [DEBUG] debug-file - Incremental task entry:default@ProcessRouterMap post-execution cost:2 ms .
[2024-06-25T11:10:36.538] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/resources cache from map.
[2024-06-25T11:10:36.538] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/resources cache by regenerate.
[2024-06-25T11:10:36.539] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/resources cache by regenerate.
[2024-06-25T11:10:36.541] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/resources cache by regenerate.
[2024-06-25T11:10:36.542] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/AppScope/resources cache by regenerate.
[2024-06-25T11:10:36.542] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/process_profile/default/module.json cache by regenerate.
[2024-06-25T11:10:36.542] [DEBUG] debug-file - Update task entry:default@CompileResource input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resConfig.json cache by regenerate.
[2024-06-25T11:10:36.542] [DEBUG] debug-file - Update task entry:default@CompileResource output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default cache.
[2024-06-25T11:10:36.543] [DEBUG] debug-file - Update task entry:default@CompileResource output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/r/default/ResourceTable.h cache.
[2024-06-25T11:10:36.543] [DEBUG] debug-file - Update task entry:default@CompileResource output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/r/default cache.
[2024-06-25T11:10:36.544] [DEBUG] debug-file - Incremental task entry:default@CompileResource post-execution cost:6 ms .
[2024-06-25T11:10:36.545] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/mock/mock-config.json5 cache from map.
[2024-06-25T11:10:36.545] [DEBUG] debug-file - Update task entry:default@GenerateLoaderJson output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/loader.json cache.
[2024-06-25T11:10:36.545] [DEBUG] debug-file - Incremental task entry:default@GenerateLoaderJson post-execution cost:2 ms .
[2024-06-25T11:10:36.546] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache from map.
[2024-06-25T11:10:36.546] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/index.ts cache by regenerate.
[2024-06-25T11:10:36.546] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.
[2024-06-25T11:10:36.546] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.
[2024-06-25T11:10:36.546] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/src/main/ets cache by regenerate.
[2024-06-25T11:10:36.547] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/ets cache by regenerate.
[2024-06-25T11:10:36.551] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.
[2024-06-25T11:10:36.551] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.
[2024-06-25T11:10:36.551] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.
[2024-06-25T11:10:36.551] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.
[2024-06-25T11:10:36.552] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets cache by regenerate.
[2024-06-25T11:10:36.552] [DEBUG] debug-file - Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.
[2024-06-25T11:10:36.552] [DEBUG] debug-file - Update task entry:default@CompileArkTS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Incremental task entry:default@CompileArkTS post-execution cost:8 ms .
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+retrofit@2.0.1-rc.0/oh_modules/@ohos/retrofit/index.ts cache by regenerate.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/js cache by regenerate.
[2024-06-25T11:10:36.553] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@BuildJS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/js cache.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Incremental task entry:default@BuildJS post-execution cost:2 ms .
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@GeneratePkgModuleJson input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache from map.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@GeneratePkgModuleJson output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/package/default/module.json cache.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2024-06-25T11:10:36.554] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache from map.
[2024-06-25T11:10:36.555] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources cache by regenerate.
[2024-06-25T11:10:36.555] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index cache by regenerate.
[2024-06-25T11:10:36.555] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info cache by regenerate.
[2024-06-25T11:10:36.555] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache by regenerate.
[2024-06-25T11:10:36.555] [DEBUG] debug-file - Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/mapping/sourceMaps.map cache.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Incremental task entry:default@PackageHap post-execution cost:2 ms .
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.cer cache by regenerate.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p7b cache by regenerate.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_UpCloud_LNyNwRmSzA_gVuIL4fhcVDGOIljUqsFhYKrJbBw_lfo=.p12 cache by regenerate.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@SignHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache from map.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Update task entry:default@SignHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap cache.
[2024-06-25T11:10:36.556] [DEBUG] debug-file - Incremental task entry:default@SignHap post-execution cost:1 ms .
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Cleanup worker 1.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Current idle worker size: 2.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Current resident worker size: 3.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Cleanup worker 6.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Worker 6 has been cleaned up.
[2024-06-25T11:10:36.566] [DEBUG] debug-file - Current idle worker size: 1.
[2024-06-25T11:10:36.567] [DEBUG] debug-file - Current resident worker size: 3.
[2024-06-25T11:10:36.567] [DEBUG] debug-file - hvigor build process will be closed.
[2024-06-25T11:10:36.567] [DEBUG] debug-file - java daemon socket close code:1000 reason:close by user
[2024-06-25T11:10:36.568] [DEBUG] debug-file - worker[1] exits with exit code 1.
[2024-06-25T11:10:36.569] [DEBUG] debug-file - session manager: send message to worker process.
[2024-06-25T11:10:36.570] [DEBUG] debug-file - session manager: send message to worker process.
[2024-06-25T11:10:36.570] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2024-06-25T11:10:36.570] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2024-06-25T11:10:36.570] [DEBUG] debug-file - worker[6] exits with exit code 1.
