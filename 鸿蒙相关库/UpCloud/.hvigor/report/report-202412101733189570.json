{"version": "2.0", "ppid": 17236, "events": [{"head": {"id": "6b96b798-f050-478a-8c1f-1970cccfa631", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451713541958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dab6b13-17ed-4266-8c8a-57b9de1fae8c", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451716955208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc81daad-6677-46b3-a7ec-e15dc7b74a55", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451718227875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fe3f6aa-bad0-4d48-8311-d186a01aa5dc", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451718428958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93cdebab-f9a3-4b70-a63a-963b0cb36917", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468477525458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3082c19b-e166-4c85-8b9b-4e330229bcca", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468482106375, "endTime": 32468808834125}, "additional": {"children": ["9284f043-d70f-4396-94bf-ed3e0968a23a", "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "a5ccfe98-d0e0-4631-95bc-cf50eee94bbc", "c4a964f7-6539-4ea4-b0a0-245b9cb89e78", "dafe6f01-ee43-4a64-ac8f-d4a66abb145b", "b1d9fe09-dd72-4b8a-9cf7-4b948d87108c", "6c023806-298f-412d-a1c4-28500c92e9ba"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9284f043-d70f-4396-94bf-ed3e0968a23a", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468482115083, "endTime": 32468496206833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "0a27e0c6-e159-41db-a660-30d3dbae446f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468496222000, "endTime": 32468807865250}, "additional": {"children": ["10f564c9-4a57-41db-b199-9a424341900f", "1cfc2311-9e09-43aa-99da-1befbccd32b2", "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "589feb37-6058-410c-b3ab-c9591aa29a48", "d3d98738-1537-479c-94c6-5fdf38f9885b", "b13a8059-37b2-44a2-8c6f-f997eb979d39", "d976ce08-fc31-4e19-8d2d-5ab5ab2f1c78", "e1514ffa-7272-4a4e-ae5f-ca48b639df55", "a64e32e3-c068-4219-be9a-ef5307ed9ad1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5ccfe98-d0e0-4631-95bc-cf50eee94bbc", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468807883041, "endTime": 32468808827583}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "7d646103-300e-4a78-831d-0b944d7125f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c4a964f7-6539-4ea4-b0a0-245b9cb89e78", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468808829791, "endTime": 32468808830375}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "08ce4e28-2b56-4b65-afd0-fc9d46ac37e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dafe6f01-ee43-4a64-ac8f-d4a66abb145b", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468483726083, "endTime": 32468483762750}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "cb2e6101-cef8-4d6b-89a0-e0fa187d56b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb2e6101-cef8-4d6b-89a0-e0fa187d56b4", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468483726083, "endTime": 32468483762750}, "additional": {"logType": "info", "children": [], "durationId": "dafe6f01-ee43-4a64-ac8f-d4a66abb145b", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "b1d9fe09-dd72-4b8a-9cf7-4b948d87108c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468488428500, "endTime": 32468488442291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "2f6081ca-e558-4c87-9425-505f8e202e34"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2f6081ca-e558-4c87-9425-505f8e202e34", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468488428500, "endTime": 32468488442291}, "additional": {"logType": "info", "children": [], "durationId": "b1d9fe09-dd72-4b8a-9cf7-4b948d87108c", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "a5376903-198f-4354-a490-3b275e2b56a5", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468488581166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97572bd7-429e-426f-8d98-4daaf5db3c3a", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468496114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a27e0c6-e159-41db-a660-30d3dbae446f", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468482115083, "endTime": 32468496206833}, "additional": {"logType": "info", "children": [], "durationId": "9284f043-d70f-4396-94bf-ed3e0968a23a", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "10f564c9-4a57-41db-b199-9a424341900f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468502993041, "endTime": 32468503000375}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "696c847a-9611-40a3-b9c2-0156c8a18b3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1cfc2311-9e09-43aa-99da-1befbccd32b2", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468503053416, "endTime": 32468506308958}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "eb62597c-6025-4d19-93b9-9965a3e85b5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468506315416, "endTime": 32468679791708}, "additional": {"children": ["1f515399-589f-4d47-9cdb-69005f94aea2", "6f46912f-3455-4446-ad82-1118b3798645", "7a31c8fc-8908-43fb-a033-7c8d87dfc820"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "a9df93b6-1f8b-47c8-b496-5dfd94570f9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "589feb37-6058-410c-b3ab-c9591aa29a48", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679801333, "endTime": 32468734090708}, "additional": {"children": ["a1a060a7-4eec-4c2d-b716-754e83355cf1", "3ec328dc-3ac1-48b6-9c43-411fecce4727"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "a1ae773d-22d2-4623-90e9-eb8e67e2e93c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3d98738-1537-479c-94c6-5fdf38f9885b", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468734136541, "endTime": 32468778969250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "ea55ea9d-c322-4b45-99de-4f40ab17e580"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b13a8059-37b2-44a2-8c6f-f997eb979d39", "name": "exec before all nodes", "description": "Execute before all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468501551958, "endTime": 32468807869250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d976ce08-fc31-4e19-8d2d-5ab5ab2f1c78", "name": "exec after all nodes", "description": "Execute after all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468501852500, "endTime": 32468807869833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1514ffa-7272-4a4e-ae5f-ca48b639df55", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468807802458, "endTime": 32468807854666}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "4932bd4f-c669-427a-b67b-6de0a520a45e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "696c847a-9611-40a3-b9c2-0156c8a18b3f", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468502993041, "endTime": 32468503000375}, "additional": {"logType": "info", "children": [], "durationId": "10f564c9-4a57-41db-b199-9a424341900f", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "eb62597c-6025-4d19-93b9-9965a3e85b5d", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468503053416, "endTime": 32468506308958}, "additional": {"logType": "info", "children": [], "durationId": "1cfc2311-9e09-43aa-99da-1befbccd32b2", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "1f515399-589f-4d47-9cdb-69005f94aea2", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468507802875, "endTime": 32468507817333}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "logId": "8c9390d3-816a-4b7d-bd11-87db2920bd81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c9390d3-816a-4b7d-bd11-87db2920bd81", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468507802875, "endTime": 32468507817333}, "additional": {"logType": "info", "children": [], "durationId": "1f515399-589f-4d47-9cdb-69005f94aea2", "parent": "a9df93b6-1f8b-47c8-b496-5dfd94570f9f"}}, {"head": {"id": "6f46912f-3455-4446-ad82-1118b3798645", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468510555333, "endTime": 32468679066375}, "additional": {"children": ["db9608a8-865a-4eaa-a49c-4cce38e539c9", "4b440caf-6ba3-4b57-ade2-3867eb75697b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "logId": "f87e1943-ce73-4007-80eb-7fdd7d282c5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db9608a8-865a-4eaa-a49c-4cce38e539c9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468510556208, "endTime": 32468529657750}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f46912f-3455-4446-ad82-1118b3798645", "logId": "6be3ff80-f8a6-45e2-8ac2-bfe3aaa76a68"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b440caf-6ba3-4b57-ade2-3867eb75697b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468529669625, "endTime": 32468679059375}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6f46912f-3455-4446-ad82-1118b3798645", "logId": "86da2fb3-5caf-433c-a37f-0a77ed53946b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13078b84-e38b-4e3c-be4f-375b25d4775d", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468510559166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dae45ba-4403-4e7b-ac36-1b54246d5045", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468529570916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6be3ff80-f8a6-45e2-8ac2-bfe3aaa76a68", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468510556208, "endTime": 32468529657750}, "additional": {"logType": "info", "children": [], "durationId": "db9608a8-865a-4eaa-a49c-4cce38e539c9", "parent": "f87e1943-ce73-4007-80eb-7fdd7d282c5c"}}, {"head": {"id": "a85c101f-8e3e-448b-839b-516f646127b0", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468529759916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1efbae8f-6249-456c-a8ed-2525fdca4686", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468558396708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f1a48de-69ea-4dad-bda6-473211e15320", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468558521250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9833abb6-8fbb-4bb6-8a37-066ce44e9e00", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468558578458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64cecfda-f11b-4e65-be59-b1ef6a175fa2", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468558640916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29031b10-3f3f-4cb8-ac91-c18896c73489", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468567083083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edb5f6d1-077a-4fb5-8ab7-f5818b3a47f5", "name": "not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468574653625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1512844d-6d68-420b-969b-ab4f998c6b07", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468577152625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a294d2d1-b4c6-4ffa-9d73-895a72c6b6eb", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468590061666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd41edd-a770-459b-9edc-e8da0c10d8c4", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468620947541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16acfbbd-2505-426b-bf82-c15d8cc2e8e4", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468621067875}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 33}, "markType": "other"}}, {"head": {"id": "63b81b98-5734-4c8d-a156-001ec6282bf2", "name": "caseSensitive<PERSON><PERSON>ck<PERSON>ff", "description": "caseSensitive<PERSON><PERSON><PERSON> check is off", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468621101000}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 33}, "markType": "other"}}, {"head": {"id": "05d32e22-8f9c-48f9-80c9-5c0ac62ddd80", "name": "Project task initialization takes 55 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468678895000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25269a43-d9bb-4a8d-bb83-ddf20b1a976c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468678982291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7115da4-3ad6-4ea5-8b9e-f8504e335d72", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679008708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7290359-5568-4fa0-9544-0db7eab52114", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679028166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86da2fb3-5caf-433c-a37f-0a77ed53946b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468529669625, "endTime": 32468679059375}, "additional": {"logType": "info", "children": [], "durationId": "4b440caf-6ba3-4b57-ade2-3867eb75697b", "parent": "f87e1943-ce73-4007-80eb-7fdd7d282c5c"}}, {"head": {"id": "f87e1943-ce73-4007-80eb-7fdd7d282c5c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468510555333, "endTime": 32468679066375}, "additional": {"logType": "info", "children": ["6be3ff80-f8a6-45e2-8ac2-bfe3aaa76a68", "86da2fb3-5caf-433c-a37f-0a77ed53946b"], "durationId": "6f46912f-3455-4446-ad82-1118b3798645", "parent": "a9df93b6-1f8b-47c8-b496-5dfd94570f9f"}}, {"head": {"id": "7a31c8fc-8908-43fb-a033-7c8d87dfc820", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679765625, "endTime": 32468679781583}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "logId": "bf94c3bd-91a7-498d-8887-0e400c7ca8b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf94c3bd-91a7-498d-8887-0e400c7ca8b5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679765625, "endTime": 32468679781583}, "additional": {"logType": "info", "children": [], "durationId": "7a31c8fc-8908-43fb-a033-7c8d87dfc820", "parent": "a9df93b6-1f8b-47c8-b496-5dfd94570f9f"}}, {"head": {"id": "a9df93b6-1f8b-47c8-b496-5dfd94570f9f", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468506315416, "endTime": 32468679791708}, "additional": {"logType": "info", "children": ["8c9390d3-816a-4b7d-bd11-87db2920bd81", "f87e1943-ce73-4007-80eb-7fdd7d282c5c", "bf94c3bd-91a7-498d-8887-0e400c7ca8b5"], "durationId": "a32a6f35-cd9d-4fb3-a0df-a5bcd86474f5", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "a1a060a7-4eec-4c2d-b716-754e83355cf1", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468680197458, "endTime": 32468714848708}, "additional": {"children": ["3a80a414-8a27-4d75-a2f5-0b0dd972de5d", "5e3f820f-21c1-4f77-8575-4bcf274a059f", "ecc4ae61-e71f-4515-a554-6e9b2c51e5b3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "589feb37-6058-410c-b3ab-c9591aa29a48", "logId": "ea61df8c-4f08-4521-ac5a-71f8585bfe1c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a80a414-8a27-4d75-a2f5-0b0dd972de5d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468686994083, "endTime": 32468687012416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1a060a7-4eec-4c2d-b716-754e83355cf1", "logId": "0d0973dd-6a67-4666-b7cc-27d2422beb46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d0973dd-6a67-4666-b7cc-27d2422beb46", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468686994083, "endTime": 32468687012416}, "additional": {"logType": "info", "children": [], "durationId": "3a80a414-8a27-4d75-a2f5-0b0dd972de5d", "parent": "ea61df8c-4f08-4521-ac5a-71f8585bfe1c"}}, {"head": {"id": "5e3f820f-21c1-4f77-8575-4bcf274a059f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468688193041, "endTime": 32468713070208}, "additional": {"children": ["5b891c15-fdb2-4c98-8394-78bed4b2a38e", "53750ff7-205d-4f9d-9866-99fe466e33ec"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1a060a7-4eec-4c2d-b716-754e83355cf1", "logId": "e4f80563-87b8-49b8-a20a-802b4220eb9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b891c15-fdb2-4c98-8394-78bed4b2a38e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468688194291, "endTime": 32468691494250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5e3f820f-21c1-4f77-8575-4bcf274a059f", "logId": "78173c4c-99c2-4321-8f61-a076f9e76043"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53750ff7-205d-4f9d-9866-99fe466e33ec", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468691508625, "endTime": 32468713059291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5e3f820f-21c1-4f77-8575-4bcf274a059f", "logId": "e881a967-7078-458f-b090-52649e3b2faa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3606b1d2-f964-40c6-baef-4d67e6d4db4d", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468688199541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c82f5a3d-c342-408b-bba6-89f6b64203a7", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468691408916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78173c4c-99c2-4321-8f61-a076f9e76043", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468688194291, "endTime": 32468691494250}, "additional": {"logType": "info", "children": [], "durationId": "5b891c15-fdb2-4c98-8394-78bed4b2a38e", "parent": "e4f80563-87b8-49b8-a20a-802b4220eb9f"}}, {"head": {"id": "43d801fa-4270-4f50-b93a-b7b4fdfa04de", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468691515125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbcdd09a-3096-4f67-807e-5fc78cbeaf80", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704217666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e764de74-f67c-4d46-a000-7386435dc3d4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704306041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe9f5dcb-3727-402d-b47c-9f37c865f3a5", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704400666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02060478-9941-4a34-9824-76fe5dccca27", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704446333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "690cd5fb-7daf-4d9d-9f4a-f910e70a6504", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704466875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99311528-4475-4169-b5bb-88127e762126", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704483583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ac8fac-c954-450a-9ce2-de80d08fb44c", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468704507083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f50dc957-3b6a-45ce-bb5f-d42645c2b48e", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468712878583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b74ff14c-7fdc-4de9-a7c7-c531753e39d0", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468712995958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f730fa6-25ac-43e6-88f4-314b8eb10596", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468713021125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f790c5a-0838-4a80-bcd1-1c0f22728123", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468713039500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e881a967-7078-458f-b090-52649e3b2faa", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468691508625, "endTime": 32468713059291}, "additional": {"logType": "info", "children": [], "durationId": "53750ff7-205d-4f9d-9866-99fe466e33ec", "parent": "e4f80563-87b8-49b8-a20a-802b4220eb9f"}}, {"head": {"id": "e4f80563-87b8-49b8-a20a-802b4220eb9f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468688193041, "endTime": 32468713070208}, "additional": {"logType": "info", "children": ["78173c4c-99c2-4321-8f61-a076f9e76043", "e881a967-7078-458f-b090-52649e3b2faa"], "durationId": "5e3f820f-21c1-4f77-8575-4bcf274a059f", "parent": "ea61df8c-4f08-4521-ac5a-71f8585bfe1c"}}, {"head": {"id": "ecc4ae61-e71f-4515-a554-6e9b2c51e5b3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468714822458, "endTime": 32468714835958}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a1a060a7-4eec-4c2d-b716-754e83355cf1", "logId": "c73bd96d-4d7a-4227-b753-16518cb3a9f6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c73bd96d-4d7a-4227-b753-16518cb3a9f6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468714822458, "endTime": 32468714835958}, "additional": {"logType": "info", "children": [], "durationId": "ecc4ae61-e71f-4515-a554-6e9b2c51e5b3", "parent": "ea61df8c-4f08-4521-ac5a-71f8585bfe1c"}}, {"head": {"id": "ea61df8c-4f08-4521-ac5a-71f8585bfe1c", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468680197458, "endTime": 32468714848708}, "additional": {"logType": "info", "children": ["0d0973dd-6a67-4666-b7cc-27d2422beb46", "e4f80563-87b8-49b8-a20a-802b4220eb9f", "c73bd96d-4d7a-4227-b753-16518cb3a9f6"], "durationId": "a1a060a7-4eec-4c2d-b716-754e83355cf1", "parent": "a1ae773d-22d2-4623-90e9-eb8e67e2e93c"}}, {"head": {"id": "3ec328dc-3ac1-48b6-9c43-411fecce4727", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468715256916, "endTime": 32468734083291}, "additional": {"children": ["6e36965d-b5a3-4973-be18-d183aaab5029", "4301d960-bd0a-4cd2-924a-2ff33854ae28", "eece1f42-c1b1-4bfa-b9f2-f609181f0d5c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "589feb37-6058-410c-b3ab-c9591aa29a48", "logId": "66e72949-4939-484e-a7c1-b247edcc4280"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e36965d-b5a3-4973-be18-d183aaab5029", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468717862375, "endTime": 32468717878083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ec328dc-3ac1-48b6-9c43-411fecce4727", "logId": "dd8d7ab9-a4c6-4a7e-9705-16d66081ef85"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd8d7ab9-a4c6-4a7e-9705-16d66081ef85", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468717862375, "endTime": 32468717878083}, "additional": {"logType": "info", "children": [], "durationId": "6e36965d-b5a3-4973-be18-d183aaab5029", "parent": "66e72949-4939-484e-a7c1-b247edcc4280"}}, {"head": {"id": "4301d960-bd0a-4cd2-924a-2ff33854ae28", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468721080083, "endTime": 32468733049041}, "additional": {"children": ["22227e81-a7d3-414f-bfe9-0482b6acaaa1", "f4cbda97-faa5-4c1e-9e00-8e71a890032b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ec328dc-3ac1-48b6-9c43-411fecce4727", "logId": "55e901cf-1842-4628-ac22-3892e0ada7fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22227e81-a7d3-414f-bfe9-0482b6acaaa1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468721081333, "endTime": 32468723548208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4301d960-bd0a-4cd2-924a-2ff33854ae28", "logId": "dc1bcf7b-21cc-46a1-98dd-259d6c71a8ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4cbda97-faa5-4c1e-9e00-8e71a890032b", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468723556166, "endTime": 32468733040291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4301d960-bd0a-4cd2-924a-2ff33854ae28", "logId": "42774e01-a95f-47a2-840f-aefd6cd6ac3b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "583dd974-5854-48e6-b640-db3cf6798ebc", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468721085000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a147441-7735-40d0-8756-790487792c88", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468723504083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc1bcf7b-21cc-46a1-98dd-259d6c71a8ef", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468721081333, "endTime": 32468723548208}, "additional": {"logType": "info", "children": [], "durationId": "22227e81-a7d3-414f-bfe9-0482b6acaaa1", "parent": "55e901cf-1842-4628-ac22-3892e0ada7fb"}}, {"head": {"id": "90858287-da4a-4a63-b90d-02ac760f9fbf", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468723559750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbeb271b-a6b7-44c6-9e32-d66dc866e9b5", "name": "Start initialize module-target build option map, moduleName=upcloud, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729257291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a700f6-5701-45ac-87a3-be7cfac5a081", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729363583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef1721e0-dc4a-4167-8916-a4e039821dd9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729459625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f99f810-2bc3-41a9-b71f-6d4532dad60b", "name": "Module 'upcloud' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729503291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28250a69-20df-4b72-ac4c-7f07cb4faa4a", "name": "Module 'upcloud' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729523583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52ed0b0a-2f79-49cc-bdcd-e8d5a6ba20aa", "name": "End initialize module-target build option map, moduleName=upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729540250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1d58e6f-f5d3-42e2-b92c-77ce01643aae", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468729561750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73aefb6a-5ee2-4787-bd66-c62875788a0c", "name": "Module upcloud task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468732788583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0bdc527-606e-478c-b019-475535fddc9b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468732957125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61be2847-7e63-4a94-b494-e83c744ee68e", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468732982458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "592f6439-4222-4cad-a807-ef87c878cb32", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468733019583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42774e01-a95f-47a2-840f-aefd6cd6ac3b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468723556166, "endTime": 32468733040291}, "additional": {"logType": "info", "children": [], "durationId": "f4cbda97-faa5-4c1e-9e00-8e71a890032b", "parent": "55e901cf-1842-4628-ac22-3892e0ada7fb"}}, {"head": {"id": "55e901cf-1842-4628-ac22-3892e0ada7fb", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468721080083, "endTime": 32468733049041}, "additional": {"logType": "info", "children": ["dc1bcf7b-21cc-46a1-98dd-259d6c71a8ef", "42774e01-a95f-47a2-840f-aefd6cd6ac3b"], "durationId": "4301d960-bd0a-4cd2-924a-2ff33854ae28", "parent": "66e72949-4939-484e-a7c1-b247edcc4280"}}, {"head": {"id": "eece1f42-c1b1-4bfa-b9f2-f609181f0d5c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468734059291, "endTime": 32468734072291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3ec328dc-3ac1-48b6-9c43-411fecce4727", "logId": "9c1ac836-f2e5-45d2-a03e-9b993a38963f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c1ac836-f2e5-45d2-a03e-9b993a38963f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468734059291, "endTime": 32468734072291}, "additional": {"logType": "info", "children": [], "durationId": "eece1f42-c1b1-4bfa-b9f2-f609181f0d5c", "parent": "66e72949-4939-484e-a7c1-b247edcc4280"}}, {"head": {"id": "66e72949-4939-484e-a7c1-b247edcc4280", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468715256916, "endTime": 32468734083291}, "additional": {"logType": "info", "children": ["dd8d7ab9-a4c6-4a7e-9705-16d66081ef85", "55e901cf-1842-4628-ac22-3892e0ada7fb", "9c1ac836-f2e5-45d2-a03e-9b993a38963f"], "durationId": "3ec328dc-3ac1-48b6-9c43-411fecce4727", "parent": "a1ae773d-22d2-4623-90e9-eb8e67e2e93c"}}, {"head": {"id": "a1ae773d-22d2-4623-90e9-eb8e67e2e93c", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468679801333, "endTime": 32468734090708}, "additional": {"logType": "info", "children": ["ea61df8c-4f08-4521-ac5a-71f8585bfe1c", "66e72949-4939-484e-a7c1-b247edcc4280"], "durationId": "589feb37-6058-410c-b3ab-c9591aa29a48", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "bfdee5be-bcef-457a-b01a-923939606fa3", "name": "watch files: [\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1846 more items\n]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468746718458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0eee80-8cae-434b-8db0-4386e7eaf045", "name": "hvigorfile, resolve hvigorfile dependencies in 45 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468778876583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea55ea9d-c322-4b45-99de-4f40ab17e580", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468734136541, "endTime": 32468778969250}, "additional": {"logType": "info", "children": [], "durationId": "d3d98738-1537-479c-94c6-5fdf38f9885b", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "a64e32e3-c068-4219-be9a-ef5307ed9ad1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468779958625, "endTime": 32468780278666}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "logId": "c71b28a7-9312-4405-931f-4aba2b598d83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c0e0d52e-55e4-44ec-a88f-ae490599558a", "name": "project has submodules:entry,upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468780060666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f2f26e-25ee-49d6-81b8-a943ec0c5664", "name": "module:upcloud no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468780250375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c71b28a7-9312-4405-931f-4aba2b598d83", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468779958625, "endTime": 32468780278666}, "additional": {"logType": "info", "children": [], "durationId": "a64e32e3-c068-4219-be9a-ef5307ed9ad1", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "a4098255-bde4-4029-a9c2-78661868e67c", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468782169041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc86edfa-e2d3-48ef-941b-faa29bf3b7ae", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468786723875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "661708ca-15f8-45cb-ae8e-3e79b37b43e9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468790008458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "fb48ff8d-b375-4db7-adaf-135f654eca2d", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468790334416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "578816bf-cfb0-4d1a-9fa6-e43a9a2c6082", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468790587708}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d37dc67b-78c1-482a-927a-cc6b9bdbb1a3", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468790836375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "207937f0-d46e-4bd8-9c66-06c34db63d2e", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468791083041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1c2b7a76-a911-442f-9567-b8bc936df04c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468791328375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cb435bfa-52af-4279-ac83-51bffbe613ff", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468791586583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "9911b414-bf38-4554-b7a1-6ee4ae33ca66", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468791834583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b44f050e-a6dc-48e5-8db2-2e30dbc8bef9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468792075541}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "46b082ff-2363-401a-8151-abbaca40c6e1", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468792313583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "6f147d62-b2e9-4b0d-815d-f47b61c87b78", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468792557208}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1b634af1-8451-4f27-bbab-1689254f163c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468792808291}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "aea4bbb7-ffbf-45bb-a960-fe6f7de27f8a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468793050458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "89e39279-1a00-45ce-84c5-677850f354bf", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468793301083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "6082266d-b1bd-46f4-a095-bfa90c2b45cf", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468793643208}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5fd1d0c3-f563-4f43-9475-adc333bdb7ce", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468794506541}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "f9b1631c-8a87-48ae-b2f3-55a256cd845f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468794783041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8894a39c-e20f-4c9f-a5c7-31d7fbf39b5f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468795040250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d30ed2c6-bd87-40cb-9290-e14f5599e75e", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468795604458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "50cc9f13-1278-4e06-886a-492544955ecc", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468795999666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2fd0da12-dadb-42d8-a21a-77235d1039a8", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468796293833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d9656809-e066-417b-a717-38760f2ca760", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468796652833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "aa50464c-f2aa-4114-af81-068c92437f7d", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468796941000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2788621e-411a-4b4c-bbde-c1176aa8d556", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468797200708}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "65e366ad-6418-4a3d-8430-3254863a05c0", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468797458875}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1ab1cb7b-7529-46be-9ca6-fb1044e87cec", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468797710833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "f90ef463-52ee-4f02-8bac-3ab6294335eb", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468798042000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "efd59832-32a9-49ac-a56b-9717ee336c29", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468798310541}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "61191ec7-420d-4183-a943-f8c69d1651bd", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468798565416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "9de067df-fc95-4099-b5ae-04a47602ad24", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468798821416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "01467e5a-8aa2-4710-ae55-30fafa0089a9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468799119375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "78cc9d79-2687-496e-8613-091b2e208b03", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468799379166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0a27e688-e93c-47db-a875-e3f4fe1d494f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468799635250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "36ad4709-34fe-484e-8fa4-f4f273e37a0f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468800017750}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "738dba95-8766-4367-91d6-6661b584baf0", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468800359791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5db5df41-5435-4c8f-8fff-cef9f4573378", "name": "Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468800430625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c64bc8f3-d17d-4a2a-b316-f2dccf48725d", "name": "Module UpCloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468800458250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e34c7b36-31ac-430a-8d95-0bf218c07c18", "name": "Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468802068375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e51ccc22-e885-4e26-97ea-3ec67378d61c", "name": "Module entry's total dependency: 9", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468802118583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1803f1be-a41c-4ef3-aa7b-6a5139e500e3", "name": "Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468806033416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0facbef-5414-41fa-aac8-060bd6ac54ff", "name": "Module upcloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468806105500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e58db24-8982-4a1a-a8d9-504003cc32e1", "name": "Configuration phase cost:305 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468807817500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4932bd4f-c669-427a-b67b-6de0a520a45e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468807802458, "endTime": 32468807854666}, "additional": {"logType": "info", "children": [], "durationId": "e1514ffa-7272-4a4e-ae5f-ca48b639df55", "parent": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211"}}, {"head": {"id": "f0f292e6-3e4c-4e2d-a49f-95fe1558e211", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468496222000, "endTime": 32468807865250}, "additional": {"logType": "info", "children": ["696c847a-9611-40a3-b9c2-0156c8a18b3f", "eb62597c-6025-4d19-93b9-9965a3e85b5d", "a9df93b6-1f8b-47c8-b496-5dfd94570f9f", "a1ae773d-22d2-4623-90e9-eb8e67e2e93c", "ea55ea9d-c322-4b45-99de-4f40ab17e580", "4932bd4f-c669-427a-b67b-6de0a520a45e", "c71b28a7-9312-4405-931f-4aba2b598d83"], "durationId": "35fc2eb2-2818-4514-804d-1abcd7c2f43a", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "6c023806-298f-412d-a1c4-28500c92e9ba", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468808811291, "endTime": 32468808821875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3082c19b-e166-4c85-8b9b-4e330229bcca", "logId": "7bcc6b18-72dc-409d-878f-8760d55565be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7bcc6b18-72dc-409d-878f-8760d55565be", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468808811291, "endTime": 32468808821875}, "additional": {"logType": "info", "children": [], "durationId": "6c023806-298f-412d-a1c4-28500c92e9ba", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "7d646103-300e-4a78-831d-0b944d7125f6", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468807883041, "endTime": 32468808827583}, "additional": {"logType": "info", "children": [], "durationId": "a5ccfe98-d0e0-4631-95bc-cf50eee94bbc", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "08ce4e28-2b56-4b65-afd0-fc9d46ac37e6", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468808829791, "endTime": 32468808830375}, "additional": {"logType": "info", "children": [], "durationId": "c4a964f7-6539-4ea4-b0a0-245b9cb89e78", "parent": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2"}}, {"head": {"id": "45596e0d-e9bb-4dc7-8984-8acb54c7afb2", "name": "init", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468482106375, "endTime": 32468808834125}, "additional": {"logType": "info", "children": ["0a27e0c6-e159-41db-a660-30d3dbae446f", "f0f292e6-3e4c-4e2d-a49f-95fe1558e211", "7d646103-300e-4a78-831d-0b944d7125f6", "08ce4e28-2b56-4b65-afd0-fc9d46ac37e6", "cb2e6101-cef8-4d6b-89a0-e0fa187d56b4", "2f6081ca-e558-4c87-9425-505f8e202e34", "7bcc6b18-72dc-409d-878f-8760d55565be"], "durationId": "3082c19b-e166-4c85-8b9b-4e330229bcca"}}, {"head": {"id": "ac978128-398b-4b6f-aa08-e06566455fd0", "name": "Configuration task cost before running: 329 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468809208083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa41607a-df8f-4479-abe7-18060e036080", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468817909208, "endTime": 32468827612083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "abe25b59-621a-4c06-b1c4-00479e568b79", "logId": "e6166272-9dac-495a-87ff-8faf4782f023"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abe25b59-621a-4c06-b1c4-00479e568b79", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468810226666}, "additional": {"logType": "detail", "children": [], "durationId": "fa41607a-df8f-4479-abe7-18060e036080"}}, {"head": {"id": "0f3533a0-7562-4279-8bb2-1f2db9614a47", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468810557375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4be7baad-315d-4aec-afe5-3d1da9d77242", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468810614291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50eedfe0-1b03-4847-acfa-ced3f56f6026", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468817929125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f296d90e-e16d-423c-88ab-047c52a95c6f", "name": "Incremental task entry:default@PreBuild pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468827502625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf123c5-971f-4c3c-8ed7-044549f78bf0", "name": "entry : default@PreBuild cost memory 0.2259368896484375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468827580708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6166272-9dac-495a-87ff-8faf4782f023", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468817909208, "endTime": 32468827612083}, "additional": {"logType": "info", "children": [], "durationId": "fa41607a-df8f-4479-abe7-18060e036080"}}, {"head": {"id": "818be7c4-c563-4d26-aa61-c322b4bf1afe", "name": "upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468842523916, "endTime": 32468845595541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Verification", "taskRunReasons": [], "detailId": "86e03e1b-1d38-4af1-b2dd-f8175b2a2547", "logId": "e97eca51-86a5-4fb6-b964-2cdb009c804a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86e03e1b-1d38-4af1-b2dd-f8175b2a2547", "name": "create upcloud:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468837858416}, "additional": {"logType": "detail", "children": [], "durationId": "818be7c4-c563-4d26-aa61-c322b4bf1afe"}}, {"head": {"id": "ca6668ea-e36c-41bd-97eb-3c3aeadac441", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468839808166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f176c7-b590-448a-a574-d5a159792243", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468839887750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd75de16-34e0-4c79-9350-d7737a032966", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468839908166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee532a28-f077-4107-a166-5842a3d95d1f", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468840040083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4cd2c37-b37b-4c93-bd4b-b7e7db06aa6f", "name": "Executing task :upcloud:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468842528375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fae80071-8e50-4dcf-840b-7d5483926a8e", "name": "Incremental task upcloud:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468845496750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30dad148-9c0e-4d44-8614-6a6cf1d7b8b7", "name": "upcloud : default@PreBuild cost memory 0.1712799072265625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468845566708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e97eca51-86a5-4fb6-b964-2cdb009c804a", "name": "UP-TO-DATE :upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468842523916, "endTime": 32468845595541}, "additional": {"logType": "info", "children": [], "durationId": "818be7c4-c563-4d26-aa61-c322b4bf1afe"}}, {"head": {"id": "45f27ee1-9b73-4588-acd1-b3775345d46b", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468855728375, "endTime": 32468857477250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "1d5934cb-9bab-41eb-b20d-5df65310e192", "logId": "1c0b99b6-5fd6-4db2-ab0c-6c04a540403c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1d5934cb-9bab-41eb-b20d-5df65310e192", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468854788416}, "additional": {"logType": "detail", "children": [], "durationId": "45f27ee1-9b73-4588-acd1-b3775345d46b"}}, {"head": {"id": "d5d71861-8c09-448f-80b2-b6ad77f95cb3", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468855018458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e40b5d4-6e46-45db-a0cf-6df95a9a2469", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468855084625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e627df4-0748-4056-88b4-2c1c5e4a265c", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468855734875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "303a2136-9819-496a-895d-c60fbe71dfcc", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468857121166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d6ebb6-9588-48b2-9f8b-d5718c199265", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468857413291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac4ba09-eee4-4f6c-85fe-0244acf666ad", "name": "entry : default@GenerateMetadata cost memory 0.08209228515625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468857449750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c0b99b6-5fd6-4db2-ab0c-6c04a540403c", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468855728375, "endTime": 32468857477250}, "additional": {"logType": "info", "children": [], "durationId": "45f27ee1-9b73-4588-acd1-b3775345d46b"}}, {"head": {"id": "f9be8613-7c48-4f3c-b0da-fd5ef0a4a265", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858930416, "endTime": 32468859112916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ad1cc339-d609-47c1-8d06-f9e20ed1fea3", "logId": "4cdbace7-2960-4e3e-ae64-aceef573d7aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad1cc339-d609-47c1-8d06-f9e20ed1fea3", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858218500}, "additional": {"logType": "detail", "children": [], "durationId": "f9be8613-7c48-4f3c-b0da-fd5ef0a4a265"}}, {"head": {"id": "03cde088-55d2-4e5c-b9dc-c6aa0334117f", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858392791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c454a96-be07-45eb-a78f-e81eae908ce9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858419833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e87785-650d-4fe5-8dda-9b6eede8bad0", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858934916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0464cd26-fdde-4d91-973a-d1c12fd766ca", "name": "entry : default@PreCheckSyscap cost memory 0.0128936767578125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468859061041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7309bbb2-626a-4ac4-9846-d4d18cfeee65", "name": "runTaskFromQueue task cost before running: 379 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468859092583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cdbace7-2960-4e3e-ae64-aceef573d7aa", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468858930416, "endTime": 32468859112916, "totalTime": 153583}, "additional": {"logType": "info", "children": [], "durationId": "f9be8613-7c48-4f3c-b0da-fd5ef0a4a265"}}, {"head": {"id": "40f1f06d-577c-4631-871d-628ee5f7615b", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866468750, "endTime": 32468867019708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "22abdc27-7fc5-48f6-a8d9-4773ee422198", "logId": "0f04c17a-e03e-4953-bd65-df8e8137b2e5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22abdc27-7fc5-48f6-a8d9-4773ee422198", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468859926916}, "additional": {"logType": "detail", "children": [], "durationId": "40f1f06d-577c-4631-871d-628ee5f7615b"}}, {"head": {"id": "c13dbd84-0d22-4dca-8981-1b8cae065507", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468860158916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6868d21c-c55b-470f-8c29-5561ee444f42", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468860191375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd01e53e-c3af-4f5f-93a8-b64baee41510", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866485750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43436d9b-f7d8-4d87-9942-60aad8d51a1c", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866696833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e0c46ab-a87a-4fb6-9dc6-9b32b849507a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.05541229248046875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866865583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa4ef236-0028-40b0-920d-a5346e3c6ebe", "name": "runTaskFromQueue task cost before running: 387 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866938291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f04c17a-e03e-4953-bd65-df8e8137b2e5", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468866468750, "endTime": 32468867019708, "totalTime": 426916}, "additional": {"logType": "info", "children": [], "durationId": "40f1f06d-577c-4631-871d-628ee5f7615b"}}, {"head": {"id": "d44f1282-9800-4555-a261-33564107f900", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468869484750, "endTime": 32468870237708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "7d4cc355-89c2-4bd2-88c3-3736302068d1", "logId": "bdf7439f-f942-4dc9-a42c-f08f57872e58"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d4cc355-89c2-4bd2-88c3-3736302068d1", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468867982125}, "additional": {"logType": "detail", "children": [], "durationId": "d44f1282-9800-4555-a261-33564107f900"}}, {"head": {"id": "8e16bca8-e8b1-4e60-ba39-fb43758fcaf3", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468868292791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27baac3e-ed5a-4ed7-a0de-b0f1eee28d03", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468868318708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf58057-07d4-417d-b648-9f8a542dc3ee", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468869488291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea25884e-84e2-4287-9ba2-043fabab0a87", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468870130416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0f421ac-60b0-43d9-a4eb-e291679678a6", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468870158208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d80f5685-2586-4b32-98e6-f92fbc4cb505", "name": "entry : default@ProcessIntegratedHsp cost memory 0.0828399658203125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468870196291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95639fa4-b46c-41e7-bc6b-0635c5625465", "name": "runTaskFromQueue task cost before running: 390 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468870220208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf7439f-f942-4dc9-a42c-f08f57872e58", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468869484750, "endTime": 32468870237708, "totalTime": 731583}, "additional": {"logType": "info", "children": [], "durationId": "d44f1282-9800-4555-a261-33564107f900"}}, {"head": {"id": "31159732-9ede-49f7-8cdb-cf57cefcf45a", "name": "upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871951416, "endTime": 32468872305208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Generate", "taskRunReasons": [], "detailId": "4cd28b43-98cb-4af0-a511-2136ae2dcc73", "logId": "a028bcac-72e6-4656-a587-ccd28ffcba12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4cd28b43-98cb-4af0-a511-2136ae2dcc73", "name": "create upcloud:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871312708}, "additional": {"logType": "detail", "children": [], "durationId": "31159732-9ede-49f7-8cdb-cf57cefcf45a"}}, {"head": {"id": "45f08a9c-4ad1-4723-bb7c-92cd3221fdce", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871499958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf11890d-48e3-45a5-a1dd-a9ed8c6d8e95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871530583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c30ce6e7-65e5-4751-aee2-f94541cffb3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871547666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe4fa82-3184-4438-845a-81beda363fc8", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871564208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48aa504e-2296-4116-9acb-5e48a7c9159e", "name": "Executing task :upcloud:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871954375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c42a92-ba37-4fc9-94c5-d60acbd5482c", "name": "Task 'upcloud:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468872048625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "571d6ecd-67b5-4cf4-9b95-c572c26985ad", "name": "Incremental task upcloud:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468872255875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b464af89-ff37-43d2-80e9-7a2bcbb129da", "name": "upcloud : default@CreateHarBuildProfile cost memory 0.07401275634765625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468872282416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a028bcac-72e6-4656-a587-ccd28ffcba12", "name": "UP-TO-DATE :upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468871951416, "endTime": 32468872305208}, "additional": {"logType": "info", "children": [], "durationId": "31159732-9ede-49f7-8cdb-cf57cefcf45a"}}, {"head": {"id": "d83aa210-b443-472b-9243-76e7b49cab53", "name": "upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468874388500, "endTime": 32468874546708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "1002f176-9056-43f3-a2dd-b5d5662090d6", "logId": "9cd96746-b26e-46cb-9e0f-4a8155e3efe2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1002f176-9056-43f3-a2dd-b5d5662090d6", "name": "create upcloud:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468873055041}, "additional": {"logType": "detail", "children": [], "durationId": "d83aa210-b443-472b-9243-76e7b49cab53"}}, {"head": {"id": "ecd0cb8c-493c-4891-b64e-1728194b48a4", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468873531500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ec8900a-c2dc-4dc6-bacc-5b4abef21fa5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468873554166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8ac08c-7bb6-4bb0-b68c-449faf861e7e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468873569291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff217bcf-3d4d-4a4d-b42e-9bcc4b66e394", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468873585958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475e688a-ac07-46e6-83c1-7582fea65da9", "name": "Executing task :upcloud:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468874397583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c7d5591-47b0-478a-8b19-bc9b5d15502b", "name": "upcloud : default@ConfigureCmake cost memory 0.01369476318359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468874490625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d96815e-46af-42da-8994-0965391db4c7", "name": "runTaskFromQueue task cost before running: 394 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468874527125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cd96746-b26e-46cb-9e0f-4a8155e3efe2", "name": "Finished :upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468874388500, "endTime": 32468874546708, "totalTime": 129791}, "additional": {"logType": "info", "children": [], "durationId": "d83aa210-b443-472b-9243-76e7b49cab53"}}, {"head": {"id": "ebf993d6-020e-487a-9bc1-64d5c9212e64", "name": "upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468886487250, "endTime": 32468887098041}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Config", "taskRunReasons": [], "detailId": "42428499-95eb-42a4-ae43-2755087a00d1", "logId": "a7fe9ec7-bc3e-4bea-ae9f-59b02c230082"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "42428499-95eb-42a4-ae43-2755087a00d1", "name": "create upcloud:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468875231166}, "additional": {"logType": "detail", "children": [], "durationId": "ebf993d6-020e-487a-9bc1-64d5c9212e64"}}, {"head": {"id": "5a178fda-1795-4925-a465-964f24f9a894", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468875602583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c1a9fb8-534a-4dd9-a28c-d20a27c902bc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468875626625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee9edf3c-e795-456e-839c-fb5f2aa61a3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468875640833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fc4b261-3747-4a9b-a378-c55a4382c7e8", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468875656458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4a824f-46f7-4fd4-858c-07f5d7f9fa24", "name": "Executing task :upcloud:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468886497333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a52b485e-312c-47b6-82e8-3096d2a89f04", "name": "Incremental task upcloud:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468887043041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d13ff4df-288c-4ca4-9a7d-5ff767129501", "name": "upcloud : default@MergeProfile cost memory 0.099884033203125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468887074208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7fe9ec7-bc3e-4bea-ae9f-59b02c230082", "name": "UP-TO-DATE :upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468886487250, "endTime": 32468887098041}, "additional": {"logType": "info", "children": [], "durationId": "ebf993d6-020e-487a-9bc1-64d5c9212e64"}}, {"head": {"id": "ac51ff17-b897-4d9d-88e6-e7186b41b85f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888941083, "endTime": 32468889834791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "b07ad2fd-67da-4dcb-bec2-5118e401d4e2", "logId": "f11afe02-3d43-42a7-a59f-7ddd90ff85f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b07ad2fd-67da-4dcb-bec2-5118e401d4e2", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468887998833}, "additional": {"logType": "detail", "children": [], "durationId": "ac51ff17-b897-4d9d-88e6-e7186b41b85f"}}, {"head": {"id": "fb9e4256-e10d-4a95-b59a-d36367a2db1e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888256625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f83318c-e031-45f0-8e12-eb9319ff57b2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888292708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b21430b-3a0a-4815-ba71-1419b5213122", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888945125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b464d23-b64a-43ea-b25b-738a14786a2f", "name": "File: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888983000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84aea57e-3296-4f64-9133-99d87bb7e6dc", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468889191500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48371581-d08a-4b41-967d-b0cc53e46ac8", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468889727083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4b19c68-486a-481b-b733-2d23d5bab8a4", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468889754708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4994ff1-53d9-4f7b-b555-9f0407b6d512", "name": "entry : default@SyscapTransform cost memory 0.09999847412109375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468889790083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33aeb993-d795-4d42-b029-b201ec33a964", "name": "runTaskFromQueue task cost before running: 410 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468889816708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f11afe02-3d43-42a7-a59f-7ddd90ff85f5", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468888941083, "endTime": 32468889834791, "totalTime": 869500}, "additional": {"logType": "info", "children": [], "durationId": "ac51ff17-b897-4d9d-88e6-e7186b41b85f"}}, {"head": {"id": "368ac839-3532-4ef9-9a64-adced54720db", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468893559916, "endTime": 32468894629208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4a24f603-0ace-4c9d-8ed9-18941f0b70f9", "logId": "d368a56c-6765-4fd3-a1f8-e97a8710816d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a24f603-0ace-4c9d-8ed9-18941f0b70f9", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468891569458}, "additional": {"logType": "detail", "children": [], "durationId": "368ac839-3532-4ef9-9a64-adced54720db"}}, {"head": {"id": "eb483361-266a-4716-b52c-1eb30de71580", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468891731291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11230d36-af2c-4bfc-a391-8e015fa8862b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468891759750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9c7253-656e-4738-a975-de4872013769", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468893564291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98838e7c-0972-43f8-adcf-da087fb38c70", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468894572583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f52654a-07f3-4720-850d-84f5ab00d921", "name": "entry : default@ProcessRouterMap cost memory 0.18337249755859375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468894606625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d368a56c-6765-4fd3-a1f8-e97a8710816d", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468893559916, "endTime": 32468894629208}, "additional": {"logType": "info", "children": [], "durationId": "368ac839-3532-4ef9-9a64-adced54720db"}}, {"head": {"id": "101064ac-ad3d-4012-a4e9-9e127a1cee3f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896246250, "endTime": 32468896804666}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "7652adce-ad2d-4909-8340-baad36da04c5", "logId": "abae4f3f-c905-4fa8-9e6f-6e058a374ae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7652adce-ad2d-4909-8340-baad36da04c5", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468895678458}, "additional": {"logType": "detail", "children": [], "durationId": "101064ac-ad3d-4012-a4e9-9e127a1cee3f"}}, {"head": {"id": "8408e651-c4a0-4e80-86e1-4dbe68cd4578", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468895842833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e18bbb5-562f-4cac-87cb-1219ea28d9f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468895866208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7346db-2bf7-4725-b38b-36a958eae55d", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896249500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe93085-32aa-443b-90a6-ab0006850da7", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896534625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cde0b59-ec9d-4108-84b7-e24c80a2eefb", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896749875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd93028b-553c-4fc6-b458-7217d35ed47e", "name": "entry : default@CreateBuildProfile cost memory 0.087158203125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896779500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abae4f3f-c905-4fa8-9e6f-6e058a374ae3", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468896246250, "endTime": 32468896804666}, "additional": {"logType": "info", "children": [], "durationId": "101064ac-ad3d-4012-a4e9-9e127a1cee3f"}}, {"head": {"id": "4a6aca82-368a-42e4-9723-e86e939acfdf", "name": "upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468903349000, "endTime": 32468903493000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "15020113-7c2e-4160-8a2f-f01ac58eeb02", "logId": "5c6508f3-0658-47a7-add1-e94ef0bec4b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15020113-7c2e-4160-8a2f-f01ac58eeb02", "name": "create upcloud:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468902058083}, "additional": {"logType": "detail", "children": [], "durationId": "4a6aca82-368a-42e4-9723-e86e939acfdf"}}, {"head": {"id": "75e491fa-1d78-4388-bee2-73838d8b6273", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468902452708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25b3dcfc-ebcd-4e27-9dfa-94de8e90838f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468902528750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974cf28a-0fe3-4847-b294-c893d7226fb3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468902546625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5119bd8-2ed2-487b-800e-d0df70c68aad", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468902562708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe273009-91e6-4289-9814-7dcf32484f31", "name": "Executing task :upcloud:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468903356791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b6db380-97e5-4452-8a86-a839f017f707", "name": "upcloud : default@BuildNativeWithCmake cost memory 0.01374053955078125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468903431125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77c85ccf-f012-4a4c-9db1-8fd2c8955390", "name": "runTaskFromQueue task cost before running: 423 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468903472583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6508f3-0658-47a7-add1-e94ef0bec4b5", "name": "Finished :upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468903349000, "endTime": 32468903493000, "totalTime": 111917}, "additional": {"logType": "info", "children": [], "durationId": "4a6aca82-368a-42e4-9723-e86e939acfdf"}}, {"head": {"id": "ede9d4ee-139c-48db-b7f2-0b010590b563", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468909878333, "endTime": 32468910791458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "1e5da169-3a48-41c5-b090-026f60bbf0ed", "logId": "c2fa67ff-31b9-4f29-9107-26ac0f3f930f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e5da169-3a48-41c5-b090-026f60bbf0ed", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468904468291}, "additional": {"logType": "detail", "children": [], "durationId": "ede9d4ee-139c-48db-b7f2-0b010590b563"}}, {"head": {"id": "4f26cc1c-472f-46d7-a4ec-40a8768229f7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468905082708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69def549-dc0c-4b23-a04f-a42617f138e2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468905129500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fac8a788-e800-4bce-af4a-0e2bc54915ae", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468909888416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcbb480d-26a9-4480-b613-2a6c25f9c339", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468910704500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "194f6578-dbda-41dc-b80b-91df03613061", "name": "entry : default@MergeProfile cost memory 0.11220550537109375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468910765791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2fa67ff-31b9-4f29-9107-26ac0f3f930f", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468909878333, "endTime": 32468910791458}, "additional": {"logType": "info", "children": [], "durationId": "ede9d4ee-139c-48db-b7f2-0b010590b563"}}, {"head": {"id": "a3d54a82-500c-40af-a30c-ae9fc4fd21b8", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468918770625, "endTime": 32468925861791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a5a4840a-635b-480a-8a55-eff3d4ab6d91", "logId": "3ce59ca6-0392-4936-a7b5-404460c93d02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5a4840a-635b-480a-8a55-eff3d4ab6d91", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468912056916}, "additional": {"logType": "detail", "children": [], "durationId": "a3d54a82-500c-40af-a30c-ae9fc4fd21b8"}}, {"head": {"id": "996f55f4-ad3e-4d91-b8b8-c2a7003bdac8", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468912364333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a909c25-0d3e-43d5-be3e-67f5083b73d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468912433583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4463eacf-faf5-46f0-a43a-f46f9a66de01", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468913128541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2541f104-b321-49af-a0db-bf94721175e5", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468918780583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d6ad9d7-52c6-489e-89c6-47e58616f884", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468919513291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "885b0ac4-282a-45be-b2ae-cafc812d32e3", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468920665458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a0663d-03d1-4922-ad7e-0c157f087687", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468922198666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "287514c4-379f-4dd7-90eb-9a91a501066a", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468923189083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52d08694-5b71-445a-8596-8d8aacd93657", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468924897500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "042dc07f-25bf-428b-8178-41bc373a71c0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468925798333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06322cd7-140d-44da-9c22-1b039bcf9a37", "name": "entry : default@GenerateLoaderJson cost memory 0.8096160888671875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468925834875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce59ca6-0392-4936-a7b5-404460c93d02", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468918770625, "endTime": 32468925861791}, "additional": {"logType": "info", "children": [], "durationId": "a3d54a82-500c-40af-a30c-ae9fc4fd21b8"}}, {"head": {"id": "6e735301-4886-499d-ac79-c858d51b71ea", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468926332500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e72a4d76-3f51-4c21-af9d-4b5b81bae8a1", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468927236333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317cc2b9-3aee-4912-8d8e-15cf7ed4b5f1", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468931478666, "endTime": 32468931586083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1c42d122-f3d2-438c-ba07-a4dd57b037db", "logId": "3cde4e65-59b2-4d20-be23-7f61129ec11a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c42d122-f3d2-438c-ba07-a4dd57b037db", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468930288791}, "additional": {"logType": "detail", "children": [], "durationId": "317cc2b9-3aee-4912-8d8e-15cf7ed4b5f1"}}, {"head": {"id": "9f0b28a7-c194-4419-b50d-859376afdee7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468930443666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c97a1e0c-c142-4dbc-83fa-03d6806eba08", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468930472041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8410a69e-6660-454b-b54d-ddca555c7af8", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468931481625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28f59aad-f181-4d11-8ffa-ccb4f2c94ff3", "name": "entry : default@ConfigureCmake cost memory 0.0125885009765625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468931530750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ac60dc7-0425-4589-bf45-3869092640e3", "name": "runTaskFromQueue task cost before running: 451 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468931565541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cde4e65-59b2-4d20-be23-7f61129ec11a", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468931478666, "endTime": 32468931586083, "totalTime": 74709}, "additional": {"logType": "info", "children": [], "durationId": "317cc2b9-3aee-4912-8d8e-15cf7ed4b5f1"}}, {"head": {"id": "f4314316-8f0e-4b38-9f24-b897f83c08f7", "name": "upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468934039166, "endTime": 32468935107208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "e9006849-9a14-4225-aeda-3616d6de9dff", "logId": "aad3cdfe-17f2-43a3-b2d6-e3e28b31dc6c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e9006849-9a14-4225-aeda-3616d6de9dff", "name": "create upcloud:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468933207333}, "additional": {"logType": "detail", "children": [], "durationId": "f4314316-8f0e-4b38-9f24-b897f83c08f7"}}, {"head": {"id": "7f89913c-391f-47bc-a3b6-cb6de65b9410", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468933569166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a663d0-31ef-47f7-9459-d2d54f86b603", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468933618500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21048d18-f545-4211-bfec-af6bfc44da9e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468933635875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b11a44ed-19bf-4523-b08d-242f1e0fec59", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468933652791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcaa6745-36d6-460f-abed-3191368c936b", "name": "Executing task :upcloud:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468934046333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ecfeb9af-70b9-4573-85a1-c323e946eee5", "name": "upcloud : default@BuildNativeWithNinja cost memory 0.028564453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468934862583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca1c009a-9441-4924-b480-9c367eb43082", "name": "runTaskFromQueue task cost before running: 455 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468935082791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aad3cdfe-17f2-43a3-b2d6-e3e28b31dc6c", "name": "Finished :upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468934039166, "endTime": 32468935107208, "totalTime": 1030167}, "additional": {"logType": "info", "children": [], "durationId": "f4314316-8f0e-4b38-9f24-b897f83c08f7"}}, {"head": {"id": "a9c9c7a0-dd6e-4fff-a88e-63d1b035289a", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936879083, "endTime": 32468940798125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6283cd74-198b-4200-b7e0-d7b556621d6b", "logId": "845d1082-140c-4284-bc0b-93344e69527d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6283cd74-198b-4200-b7e0-d7b556621d6b", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936245541}, "additional": {"logType": "detail", "children": [], "durationId": "a9c9c7a0-dd6e-4fff-a88e-63d1b035289a"}}, {"head": {"id": "fb34efcf-d39c-4d5a-b472-5caaff6232fe", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936487666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f1fbd29-459e-4eb3-b284-47e6d65c172a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936529833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5292b7-5176-4f1b-872c-504b97482063", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936883333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40784868-0fe4-4edd-adf9-e38618cb802a", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468940698375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ee17170-f368-4e8d-a3b4-2ed26a595ecb", "name": "entry : default@MakePackInfo cost memory 0.1263885498046875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468940768375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "845d1082-140c-4284-bc0b-93344e69527d", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468936879083, "endTime": 32468940798125}, "additional": {"logType": "info", "children": [], "durationId": "a9c9c7a0-dd6e-4fff-a88e-63d1b035289a"}}, {"head": {"id": "361c74b2-5f57-4703-8eb5-174855747cae", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468943630500, "endTime": 32468943981333}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "6933a0e9-e4fe-4ea9-ab60-8df32e26a241", "logId": "ffaec9f8-3e58-4caa-b4cd-2ad8a0a0ef79"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6933a0e9-e4fe-4ea9-ab60-8df32e26a241", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468942024041}, "additional": {"logType": "detail", "children": [], "durationId": "361c74b2-5f57-4703-8eb5-174855747cae"}}, {"head": {"id": "d4d21344-e176-4aaa-add9-a545cf61d1bb", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468942214916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c634e487-bdc0-4b07-b7fe-8f58530c8d46", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468942239958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01501299-661e-4296-a2fb-975606deba98", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468943637875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f694bd1-b055-4826-9fcf-fa27d03d0ca8", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468943927500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1335175f-5114-4458-a732-adf0b3aeb4b4", "name": "entry : default@ProcessProfile cost memory 0.047943115234375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468943958166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffaec9f8-3e58-4caa-b4cd-2ad8a0a0ef79", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468943630500, "endTime": 32468943981333}, "additional": {"logType": "info", "children": [], "durationId": "361c74b2-5f57-4703-8eb5-174855747cae"}}, {"head": {"id": "635dcce6-dd7d-41ab-9730-06ce79f42261", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945531166, "endTime": 32468947174541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eae40cbe-d17e-4829-9f26-b0a3b491ca34", "logId": "5a158e53-e75e-4196-b14e-63a2851c22a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eae40cbe-d17e-4829-9f26-b0a3b491ca34", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468944758583}, "additional": {"logType": "detail", "children": [], "durationId": "635dcce6-dd7d-41ab-9730-06ce79f42261"}}, {"head": {"id": "6fa88ec1-84df-4e60-a9cc-f93ef4e4392e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945032166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5402bf7-709c-4c63-9d4f-658eb7522161", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945067375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eafb5b55-9ea7-476d-8571-782371a2b11c", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945534375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45ba8922-fdd3-40dc-9a4d-20086218755a", "name": "entry : default@BuildNativeWithCmake cost memory 0.01364898681640625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945589666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ec8b98-67b6-4cfc-9585-4659380609a8", "name": "runTaskFromQueue task cost before running: 466 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945621250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a158e53-e75e-4196-b14e-63a2851c22a5", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468945531166, "endTime": 32468947174541, "totalTime": 79916}, "additional": {"logType": "info", "children": [], "durationId": "635dcce6-dd7d-41ab-9730-06ce79f42261"}}, {"head": {"id": "927ea2ce-ec46-41d6-bbf1-b60943ebd13a", "name": "upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468950187041, "endTime": 32468951689083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Resources", "taskRunReasons": [], "detailId": "5c6d0997-715f-482d-932f-bb938bc19e76", "logId": "cd664667-4402-44b4-b9f7-ee68d0650ab2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c6d0997-715f-482d-932f-bb938bc19e76", "name": "create upcloud:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468949430458}, "additional": {"logType": "detail", "children": [], "durationId": "927ea2ce-ec46-41d6-bbf1-b60943ebd13a"}}, {"head": {"id": "a5675024-50cc-407c-8aa0-5d40b0e17b77", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468949759291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13d7b15d-d865-469b-9b91-9d4b4a8bdad1", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468949803250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24226bb4-491c-4cc5-a01c-d0da8603e2b9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468949818541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "638472f6-bad3-47f0-b5b1-e711fb964d1b", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468949833958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92938c9d-6057-4988-9e1e-ceed62e081c5", "name": "Executing task :upcloud:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468950193166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85458257-9d06-493d-964a-1a1fdc85856e", "name": "Incremental task upcloud:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468951624958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76a44132-71ec-4960-b29a-7e8bad52db69", "name": "upcloud : default@ProcessLibs cost memory 0.10271453857421875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468951663583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd664667-4402-44b4-b9f7-ee68d0650ab2", "name": "UP-TO-DATE :upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468950187041, "endTime": 32468951689083}, "additional": {"logType": "info", "children": [], "durationId": "927ea2ce-ec46-41d6-bbf1-b60943ebd13a"}}, {"head": {"id": "4bc8c500-d98d-4f63-a806-ccc750e5d430", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468955550833, "endTime": 32468957870208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d5dddc67-d7d7-4a5b-b38e-46e34b474705", "logId": "76cd8e33-70ad-4ada-81e2-ac284039ee60"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5dddc67-d7d7-4a5b-b38e-46e34b474705", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468952388458}, "additional": {"logType": "detail", "children": [], "durationId": "4bc8c500-d98d-4f63-a806-ccc750e5d430"}}, {"head": {"id": "b6d5b7f8-aede-4ba6-a7dd-f181e3c39588", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468952640375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc4e8179-7780-4241-b0c6-b55ef77b518d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468952676208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "899b6c25-f9ec-4c46-acbb-9dc0aeae7198", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468953090416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab01c00-df8d-483e-9f09-1bd449a64714", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468956539750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461efaef-0e78-4fa7-8bf0-e44ba85e5516", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468956855333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f575b75-698a-401b-bf88-e17da71a5c43", "name": "entry : default@ProcessResource cost memory 0.107269287109375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468956890583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76cd8e33-70ad-4ada-81e2-ac284039ee60", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468955550833, "endTime": 32468957870208}, "additional": {"logType": "info", "children": [], "durationId": "4bc8c500-d98d-4f63-a806-ccc750e5d430"}}, {"head": {"id": "200ca8fb-9fea-42a5-aea7-aa382df6e7a8", "name": "upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468960697291, "endTime": 32468961246916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "24aa4077-857c-4593-ac1b-f57d0eaaa646", "logId": "756a6eb7-d0dd-4227-b3d9-4120a53b98ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "24aa4077-857c-4593-ac1b-f57d0eaaa646", "name": "create upcloud:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468959498583}, "additional": {"logType": "detail", "children": [], "durationId": "200ca8fb-9fea-42a5-aea7-aa382df6e7a8"}}, {"head": {"id": "6ac8db44-4909-48a1-a964-f8b4d9f9964c", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468959709500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e8c8fcd-b425-4ba7-a7e4-5c7650b478e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468959752666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7689174e-7db1-4724-a5ff-8c5246b12489", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468959769291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0131e493-cc2d-4af5-9d8d-e80b7035d3cd", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468959785541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c3ed01d-daa3-4460-9e51-eb73717a625d", "name": "Executing task :upcloud:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468960701583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a273c26d-38f2-47cb-826e-7e24e0291a73", "name": "Task 'upcloud:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468960764166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc28688a-381e-4c61-9f1a-31af3a6f24bb", "name": "Incremental task upcloud:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468961180458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9997d698-1eec-49e7-a031-fcc1f122a1ac", "name": "upcloud : default@DoNativeStrip cost memory 0.06481170654296875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468961222041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756a6eb7-d0dd-4227-b3d9-4120a53b98ff", "name": "UP-TO-DATE :upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468960697291, "endTime": 32468961246916}, "additional": {"logType": "info", "children": [], "durationId": "200ca8fb-9fea-42a5-aea7-aa382df6e7a8"}}, {"head": {"id": "ac7666f8-5a06-47cf-9abc-3d2b02c0b4dc", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468966016750, "endTime": 32468977830541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "28c91d5e-81f7-44ab-9d49-f2adef8d2426", "logId": "4267f50e-1efc-441d-9ed9-ecc2ad451cc9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28c91d5e-81f7-44ab-9d49-f2adef8d2426", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468961912666}, "additional": {"logType": "detail", "children": [], "durationId": "ac7666f8-5a06-47cf-9abc-3d2b02c0b4dc"}}, {"head": {"id": "5f8be1e3-2112-4a78-a4dc-e71a15064c76", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468962060250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f034d7-7832-4dc4-83b0-379d336718ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468962087791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f48f4f-4869-4923-bb6d-e8f7006fe573", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468962329166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02211a1f-7bbc-4bbb-81a5-efe2a6b91453", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468966067000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f7f213-7178-4ae9-b227-63e986499964", "name": "Incremental task entry:default@CompileResource pre-execution cost: 11 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468977657791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "043964de-f339-483b-9bb6-a94656825005", "name": "entry : default@CompileResource cost memory 0.8412933349609375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468977782916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4267f50e-1efc-441d-9ed9-ecc2ad451cc9", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468966016750, "endTime": 32468977830541}, "additional": {"logType": "info", "children": [], "durationId": "ac7666f8-5a06-47cf-9abc-3d2b02c0b4dc"}}, {"head": {"id": "ee00ba9d-c4f1-4529-be8b-e7d8adcc3883", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468987127375, "endTime": 32468988028166}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e3219a5d-5a74-40db-a9b5-1ee662ee9612", "logId": "660ed015-6d60-4910-b9e7-6ea25f52aa9b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e3219a5d-5a74-40db-a9b5-1ee662ee9612", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468985306208}, "additional": {"logType": "detail", "children": [], "durationId": "ee00ba9d-c4f1-4529-be8b-e7d8adcc3883"}}, {"head": {"id": "625d78a0-dd66-4cd9-a54a-301cd3117b64", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468985816041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0761b219-72e8-4001-a4c2-e0d2cf128dba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468985910833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea856880-0dec-4e4d-b029-d0dd58872a0c", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468987134791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e38760ef-5d80-4919-a180-4602b4e39069", "name": "entry : default@BuildNativeWithNinja cost memory 0.02843475341796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468987921000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8b39428-fe36-4d9b-b5e5-569d5463b2b8", "name": "runTaskFromQueue task cost before running: 508 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468988005583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "660ed015-6d60-4910-b9e7-6ea25f52aa9b", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468987127375, "endTime": 32468988028166, "totalTime": 863500}, "additional": {"logType": "info", "children": [], "durationId": "ee00ba9d-c4f1-4529-be8b-e7d8adcc3883"}}, {"head": {"id": "8fdc7639-0cb8-4b37-bcdc-091ef5d58894", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468991534250, "endTime": 32470960328958}, "additional": {"children": ["35890bb6-b264-4f6c-947c-404e3ad328c4", "ce6afdf4-585f-4ade-a2e7-092bc6d32e69"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets' has been changed."], "detailId": "fdfd841f-5377-42dc-b847-d017e2d88472", "logId": "c473aaea-291c-4cef-8d77-fff4917481a4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fdfd841f-5377-42dc-b847-d017e2d88472", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468989013083}, "additional": {"logType": "detail", "children": [], "durationId": "8fdc7639-0cb8-4b37-bcdc-091ef5d58894"}}, {"head": {"id": "1f23d41f-f6a3-4f5c-a4f7-1bfebf5bca7c", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468989232708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1b097ec-7836-4a8b-b83f-8c2d8505c939", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468989267375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89cb8bef-a087-47c5-9b23-9f67b01cba3c", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468991539916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a066e39-dd31-4893-bbf0-2afd15756c79", "name": "entry:default@CompileArkTS is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468996312583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3b2d672-e1c6-4083-9d48-b04450b09b09", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468996403208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4e3297f-4a00-417b-869c-ab7e7283be17", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469002974833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b701599b-cdc8-4cc9-ba9f-047462b3d526", "name": "Compile arkts with external api path: /Applications/DevEco-Studio.app/Contents/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469003288041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a8473f1-496e-4776-b06b-d775fd2a6ad7", "name": "default@CompileArkTS work[61] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469003693208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35890bb6-b264-4f6c-947c-404e3ad328c4", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469106791041, "endTime": 32470956439875}, "additional": {"children": ["65c64525-4b2e-44d8-b2da-ddf271245830", "44f58cff-c74a-4d19-9bf2-67332951ac49", "ebe8e58c-f3f6-43f0-a0e8-b186a8f8e202", "314d340f-63a0-42e8-85f4-f35603b16497", "9dd56535-b963-4d88-bdf7-d69e598e8581", "05c1a005-bf0f-4f6c-8697-18f7ef193eed"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8fdc7639-0cb8-4b37-bcdc-091ef5d58894", "logId": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e850c1d-c82d-4016-b51f-4c563d42c65c", "name": "default@CompileArkTS work[61] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004159000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b480dbe-0118-4c61-bf30-aecbd9b226a3", "name": "default@CompileArkTS work[61] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004260375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68fb30ba-5613-474c-86aa-117c8ea83cd4", "name": "CopyResources startTime: 32469004286875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004287750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceb97c55-9a71-4207-bec0-b666e9093968", "name": "default@CompileArkTS work[62] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004309833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce6afdf4-585f-4ade-a2e7-092bc6d32e69", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32469521091333, "endTime": 32469525507791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "8fdc7639-0cb8-4b37-bcdc-091ef5d58894", "logId": "6edfe472-46f9-44a5-b6a6-5ef54da692ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "135d9904-98f2-468b-b76e-22c691484294", "name": "default@CompileArkTS work[62] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004573958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d81bd93a-8a43-4d5b-bb4a-a556f23cfcbb", "name": "default@CompileArkTS work[62] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004594125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88d0b628-b7e6-48bb-9a87-bf2a4711e3b8", "name": "entry : default@CompileArkTS cost memory 1.2976837158203125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469004643958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c1ef1da-0bf6-4c4c-be1f-89eec2feaa8a", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469007657708, "endTime": 32469009388291}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "e2b045cc-f9ee-4ac3-ac0b-e70c77e8b64c", "logId": "7df3904f-5f7a-4f4b-9837-fc603ebd26d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2b045cc-f9ee-4ac3-ac0b-e70c77e8b64c", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469005316375}, "additional": {"logType": "detail", "children": [], "durationId": "4c1ef1da-0bf6-4c4c-be1f-89eec2feaa8a"}}, {"head": {"id": "bc5561bd-7dfc-4fa7-9d9a-f2ccd7422f13", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469005488708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff3d0b0-d43d-4880-a8dd-76405412a467", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469005520416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b15a46d2-d60c-4691-ac4d-a743e20c7556", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469007666666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea301458-c076-4dcd-bf94-1572133d7c19", "name": "entry : default@BuildJS cost memory 0.11231231689453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469009298416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bca247c7-d942-421b-9d4e-0501f43a035e", "name": "runTaskFromQueue task cost before running: 529 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469009363333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7df3904f-5f7a-4f4b-9837-fc603ebd26d4", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469007657708, "endTime": 32469009388291, "totalTime": 1693125}, "additional": {"logType": "info", "children": [], "durationId": "4c1ef1da-0bf6-4c4c-be1f-89eec2feaa8a"}}, {"head": {"id": "8f1dfb4c-3a70-45d2-abc7-7b5f8ada0106", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010511583, "endTime": 32469016000250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "085bc42c-d41e-47ab-9904-e78c5b3af195", "logId": "e9b7b309-921d-4f91-b385-8e57d70ca35d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "085bc42c-d41e-47ab-9904-e78c5b3af195", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010069083}, "additional": {"logType": "detail", "children": [], "durationId": "8f1dfb4c-3a70-45d2-abc7-7b5f8ada0106"}}, {"head": {"id": "5a1bd6e9-4333-4340-a444-a170e0c27fab", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010215625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "095e4b18-037d-4b47-a21f-ec460bca5a96", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010243208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8cdbca3-e382-4679-8430-eeabd11db904", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010514375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed7beeac-106f-465e-9c37-84587e7296d2", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469015787291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "098dfbcb-6d34-4c22-a7e6-666f70313613", "name": "entry : default@ProcessLibs cost memory 0.5331344604492188", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469015880125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9b7b309-921d-4f91-b385-8e57d70ca35d", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469010511583, "endTime": 32469016000250}, "additional": {"logType": "info", "children": [], "durationId": "8f1dfb4c-3a70-45d2-abc7-7b5f8ada0106"}}, {"head": {"id": "2e8fad4e-7fb8-484d-87ec-5837a0cd15d4", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469018828500, "endTime": 32469019321750}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "975e74f6-f1f3-49de-b2f8-1fbf0b592636", "logId": "26de0c1d-9812-47eb-9b14-4bbd5ff05a6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "975e74f6-f1f3-49de-b2f8-1fbf0b592636", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469016794041}, "additional": {"logType": "detail", "children": [], "durationId": "2e8fad4e-7fb8-484d-87ec-5837a0cd15d4"}}, {"head": {"id": "4fdde662-43c3-49f7-a1a2-8f7e825022f0", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469017052375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d564d86-b044-4492-ad54-de789de7a8d6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469017077250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d0212b-a4ea-431c-bd63-8e356520d0bd", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469018831375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75b66abc-ed43-401a-89c0-d3b94b166dc4", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469018875750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc5bb838-1dca-49f5-a0e9-9c0c65f579dd", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469019266375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a702159-f839-441f-aa7f-0513d9324d53", "name": "entry : default@DoNativeStrip cost memory 0.0598297119140625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469019299541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26de0c1d-9812-47eb-9b14-4bbd5ff05a6a", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469018828500, "endTime": 32469019321750}, "additional": {"logType": "info", "children": [], "durationId": "2e8fad4e-7fb8-484d-87ec-5837a0cd15d4"}}, {"head": {"id": "515e833c-19a8-4681-beaf-57afddb87af0", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025031708, "endTime": 32469025621833}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fb5a909e-563d-4b15-b3ed-b7f99b269d4c", "logId": "92e38065-b000-4224-a904-855ab94c9fd3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb5a909e-563d-4b15-b3ed-b7f99b269d4c", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469023119666}, "additional": {"logType": "detail", "children": [], "durationId": "515e833c-19a8-4681-beaf-57afddb87af0"}}, {"head": {"id": "0706428a-da54-49ad-8d5e-d5ccbc5bb51b", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469023336250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cb275f7-4087-40a1-a5a7-9c163c3889ec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469023361166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a63caca-641f-4e33-b503-36884fd0985a", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025036541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fcc0486b-71dc-4441-b3af-8927f7e9de3c", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025134125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb3d1c58-79b2-41c1-90e9-91de8740e1ef", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025558208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3c9af1b-e8d7-4402-a6e9-f42de864295a", "name": "entry : default@CacheNativeLibs cost memory 0.0670623779296875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025595250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92e38065-b000-4224-a904-855ab94c9fd3", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469025031708, "endTime": 32469025621833}, "additional": {"logType": "info", "children": [], "durationId": "515e833c-19a8-4681-beaf-57afddb87af0"}}, {"head": {"id": "3c896ccc-be22-4726-bd3c-9017c9838bdd", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469104954125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c18fa7ec-e1d3-4cb3-bba9-5b07da08aad8", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469105161916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82085840-2876-4bf4-a65f-4d86fac312cc", "name": "default@CompileArkTS work[62] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469106180166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdf8ed5c-aff0-44fe-8570-23d82b03b306", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469106372166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85a376b9-45c5-4373-97ae-9d1697a6b073", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469106395250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d5d54e-ece4-424e-a4d3-c74c5ebfa2af", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469106414333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be187dcb-251c-41b3-9015-7d3ea95339c7", "name": "default@CompileArkTS work[61] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469106474375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8681263-4b4f-4f83-92ec-19d5f1956598", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469525827916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b943173-b89d-44d1-a4b3-057e0928cf6d", "name": "CopyResources is end, endTime: 32469526106208", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469526114125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1ba779f-73ff-45c2-9fc5-cbb642403b6d", "name": "default@CompileArkTS work[62] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469526242125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6edfe472-46f9-44a5-b6a6-5ef54da692ad", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32469521091333, "endTime": 32469525507791}, "additional": {"logType": "info", "children": [], "durationId": "ce6afdf4-585f-4ade-a2e7-092bc6d32e69", "parent": "c473aaea-291c-4cef-8d77-fff4917481a4"}}, {"head": {"id": "925cd28a-8ada-4b09-aeca-75a7b457c380", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469526349333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3dc56907-11f1-4628-b2a3-adf9002b2d47", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470957384333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c64525-4b2e-44d8-b2da-ddf271245830", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469107115541, "endTime": 32469114867000}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "9a9f0415-99b8-4743-8ed9-ab7fdb23beba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a9f0415-99b8-4743-8ed9-ab7fdb23beba", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469107115541, "endTime": 32469114867000}, "additional": {"logType": "info", "children": [], "durationId": "65c64525-4b2e-44d8-b2da-ddf271245830", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "44f58cff-c74a-4d19-9bf2-67332951ac49", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469114896291, "endTime": 32469114964375}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "22325595-fcbc-4c49-affd-ccf3fa284517"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22325595-fcbc-4c49-affd-ccf3fa284517", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469114896291, "endTime": 32469114964375}, "additional": {"logType": "info", "children": [], "durationId": "44f58cff-c74a-4d19-9bf2-67332951ac49", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "ebe8e58c-f3f6-43f0-a0e8-b186a8f8e202", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469114994583, "endTime": 32469115418708}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "85814c8e-13c7-4d00-8dc4-f7dae86b5bcb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "85814c8e-13c7-4d00-8dc4-f7dae86b5bcb", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469114994583, "endTime": 32469115418708}, "additional": {"logType": "info", "children": [], "durationId": "ebe8e58c-f3f6-43f0-a0e8-b186a8f8e202", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "314d340f-63a0-42e8-85f4-f35603b16497", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469115446041, "endTime": 32470622961458}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "2725a56b-7707-4ddc-a25e-805a84bf820c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2725a56b-7707-4ddc-a25e-805a84bf820c", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32469115446041, "endTime": 32470622961458}, "additional": {"logType": "info", "children": [], "durationId": "314d340f-63a0-42e8-85f4-f35603b16497", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "9dd56535-b963-4d88-bdf7-d69e598e8581", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32470622970708, "endTime": 32470735939916}, "additional": {"children": ["eb19e49b-60f7-45a7-b14e-29fecad89f4e", "7d73b5c5-f679-4b18-bb03-af6e2ebbc82d", "6d0f2cd6-3f2a-4169-810f-b93e717056c5"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "9a3a425d-deb7-4f15-b43b-b2935b91d466"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a3a425d-deb7-4f15-b43b-b2935b91d466", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470622970708, "endTime": 32470735939916}, "additional": {"logType": "info", "children": ["a9c05be1-5fee-48f3-b776-31c33dc40abd", "4e3359dd-6c41-4c49-b42a-4a7550fbd47c", "7ca0f9fd-53e4-4ed3-9441-47491f8e536f"], "durationId": "9dd56535-b963-4d88-bdf7-d69e598e8581", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "eb19e49b-60f7-45a7-b14e-29fecad89f4e", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32470622986541, "endTime": 32470622994625}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9dd56535-b963-4d88-bdf7-d69e598e8581", "logId": "a9c05be1-5fee-48f3-b776-31c33dc40abd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9c05be1-5fee-48f3-b776-31c33dc40abd", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470622986541, "endTime": 32470622994625}, "additional": {"logType": "info", "children": [], "durationId": "eb19e49b-60f7-45a7-b14e-29fecad89f4e", "parent": "9a3a425d-deb7-4f15-b43b-b2935b91d466"}}, {"head": {"id": "7d73b5c5-f679-4b18-bb03-af6e2ebbc82d", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32470622995500, "endTime": 32470697044333}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9dd56535-b963-4d88-bdf7-d69e598e8581", "logId": "4e3359dd-6c41-4c49-b42a-4a7550fbd47c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e3359dd-6c41-4c49-b42a-4a7550fbd47c", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470622995500, "endTime": 32470697044333}, "additional": {"logType": "info", "children": [], "durationId": "7d73b5c5-f679-4b18-bb03-af6e2ebbc82d", "parent": "9a3a425d-deb7-4f15-b43b-b2935b91d466"}}, {"head": {"id": "6d0f2cd6-3f2a-4169-810f-b93e717056c5", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32470697045416, "endTime": 32470735924583}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "9dd56535-b963-4d88-bdf7-d69e598e8581", "logId": "7ca0f9fd-53e4-4ed3-9441-47491f8e536f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7ca0f9fd-53e4-4ed3-9441-47491f8e536f", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470697045416, "endTime": 32470735924583}, "additional": {"logType": "info", "children": [], "durationId": "6d0f2cd6-3f2a-4169-810f-b93e717056c5", "parent": "9a3a425d-deb7-4f15-b43b-b2935b91d466"}}, {"head": {"id": "05c1a005-bf0f-4f6c-8697-18f7ef193eed", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32470735947166, "endTime": 32470854810583}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "35890bb6-b264-4f6c-947c-404e3ad328c4", "logId": "f2e4b44a-517f-4e09-9bad-d81781d4dc72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f2e4b44a-517f-4e09-9bad-d81781d4dc72", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470735947166, "endTime": 32470854810583}, "additional": {"logType": "info", "children": [], "durationId": "05c1a005-bf0f-4f6c-8697-18f7ef193eed", "parent": "282b8c43-c8e7-415b-916a-2a3fa23a9709"}}, {"head": {"id": "b40e615c-49d3-47d9-b5d8-a42bfe7d5e84", "name": "default@CompileArkTS work[61] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470960183125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "282b8c43-c8e7-415b-916a-2a3fa23a9709", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32469106791041, "endTime": 32470956439875}, "additional": {"logType": "info", "children": ["9a9f0415-99b8-4743-8ed9-ab7fdb23beba", "22325595-fcbc-4c49-affd-ccf3fa284517", "85814c8e-13c7-4d00-8dc4-f7dae86b5bcb", "2725a56b-7707-4ddc-a25e-805a84bf820c", "9a3a425d-deb7-4f15-b43b-b2935b91d466", "f2e4b44a-517f-4e09-9bad-d81781d4dc72"], "durationId": "35890bb6-b264-4f6c-947c-404e3ad328c4", "parent": "c473aaea-291c-4cef-8d77-fff4917481a4"}}, {"head": {"id": "68d0bfa0-3df3-4c7a-86ad-2a166987aba4", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470960265833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c473aaea-291c-4cef-8d77-fff4917481a4", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468991534250, "endTime": 32470960328958, "totalTime": 1862780542}, "additional": {"logType": "info", "children": ["282b8c43-c8e7-415b-916a-2a3fa23a9709", "6edfe472-46f9-44a5-b6a6-5ef54da692ad"], "durationId": "8fdc7639-0cb8-4b37-bcdc-091ef5d58894"}}, {"head": {"id": "ed36ad2a-4384-4fbe-b4d2-a1301d0416ef", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962765833, "endTime": 32470963112541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "d91a7e6b-830a-4811-b484-07f12d454b50", "logId": "69085008-646f-4306-b89e-5452263cfd4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d91a7e6b-830a-4811-b484-07f12d454b50", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962159125}, "additional": {"logType": "detail", "children": [], "durationId": "ed36ad2a-4384-4fbe-b4d2-a1301d0416ef"}}, {"head": {"id": "01cc4aff-b9a9-4c88-8efc-6d7069e812bc", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962347625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cda0a67c-0642-4d63-9efb-375e0ac297d9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962381166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaa24ec7-9e11-4d7b-8339-b32c4cd568b5", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962770875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "962512ff-bf21-4a34-9eaa-9fad8a6963bb", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962835833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5f97f7-29ba-49f4-b97f-6325e9f86d9a", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470963058000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78725331-3b8b-4c7e-bd67-55edbd84bb1f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.05913543701171875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470963087333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69085008-646f-4306-b89e-5452263cfd4a", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470962765833, "endTime": 32470963112541}, "additional": {"logType": "info", "children": [], "durationId": "ed36ad2a-4384-4fbe-b4d2-a1301d0416ef"}}, {"head": {"id": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470966615583, "endTime": 32471298968125}, "additional": {"children": ["8a481a40-6b58-4f8c-9ff4-89b92ab983ce", "ba19d33e-24aa-45d7-8ca1-c38932f42572", "d5a93053-967c-404c-a6cb-724ad69c1161"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets' has been changed."], "detailId": "f0b1a3ba-08b9-4836-bdc0-1fd7c80c98b2", "logId": "64a547d8-7e64-469b-b95e-eb5c810d32e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0b1a3ba-08b9-4836-bdc0-1fd7c80c98b2", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470964036041}, "additional": {"logType": "detail", "children": [], "durationId": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41"}}, {"head": {"id": "f7b18dbd-be50-4973-b1cb-8403ceebb26d", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470964149625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd273e52-6952-40a2-b576-318d29c3976a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470964176791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b8f8f1-658c-4e52-bd06-507c12b8051d", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470966620333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4874ab2-0d99-43ba-bcc4-73cd564c3285", "name": "entry:default@PackageHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470969580041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37abe1e2-4c41-4062-89f0-0897bd79fea1", "name": "Incremental task entry:default@PackageHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470969649416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a481a40-6b58-4f8c-9ff4-89b92ab983ce", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470970697083, "endTime": 32470971680375}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41", "logId": "eb6cb5e0-f6ca-4f5a-807e-5edaa67305ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70df78cf-640c-4d5d-920b-2c80ca764d71", "name": "Use tool [/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=utf-8',\n  '-jar',\n  '/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default',\n  '--json-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/package/default/module.json',\n  '--resources-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources',\n  '--index-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index',\n  '--pack-info-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info',\n  '--out-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap',\n  '--ets-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets'\n]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470971593083}, "additional": {"logType": "debug", "children": [], "durationId": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41"}}, {"head": {"id": "eb6cb5e0-f6ca-4f5a-807e-5edaa67305ad", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470970697083, "endTime": 32470971680375}, "additional": {"logType": "info", "children": [], "durationId": "8a481a40-6b58-4f8c-9ff4-89b92ab983ce", "parent": "64a547d8-7e64-469b-b95e-eb5c810d32e8"}}, {"head": {"id": "ba19d33e-24aa-45d7-8ca1-c38932f42572", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972067291, "endTime": 32470972957541}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41", "logId": "65fbef83-0386-4469-b4d8-40567f46849e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2984d888-8c57-4998-b10e-7e01fb762c41", "name": "default@PackageHap work[63] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972422375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5a93053-967c-404c-a6cb-724ad69c1161", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32471003568791, "endTime": 32471298636458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41", "logId": "bc4b3247-c992-4594-8bef-3847d0426382"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4843ee82-d365-4199-8f58-ac877bc6ecbd", "name": "default@PackageHap work[63] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972706291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054351db-bfd1-4f8d-ab39-07be7f755d23", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972738333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b6aa213-b57c-4f3f-bb25-16b8f77533c0", "name": "default@PackageHap work[63] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972792750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240fad62-2a0e-4b45-a5c3-f90afa57dc72", "name": "default@PackageHap work[63] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972816958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65fbef83-0386-4469-b4d8-40567f46849e", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470972067291, "endTime": 32470972957541}, "additional": {"logType": "info", "children": [], "durationId": "ba19d33e-24aa-45d7-8ca1-c38932f42572", "parent": "64a547d8-7e64-469b-b95e-eb5c810d32e8"}}, {"head": {"id": "a41eefc0-d6bb-4715-b528-7c637dabcec2", "name": "entry : default@PackageHap cost memory 0.7231674194335938", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470974527833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877826d4-5231-42ea-b798-5a12ea355622", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471298743375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22c62d9c-11f0-44e7-a522-ec5d4ca55207", "name": "default@PackageHap work[63] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471298881041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc4b3247-c992-4594-8bef-3847d0426382", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32471003568791, "endTime": 32471298636458}, "additional": {"logType": "info", "children": [], "durationId": "d5a93053-967c-404c-a6cb-724ad69c1161", "parent": "64a547d8-7e64-469b-b95e-eb5c810d32e8"}}, {"head": {"id": "14ea75b6-7f6b-4dfa-9d82-d388cf196dde", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471298937458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a547d8-7e64-469b-b95e-eb5c810d32e8", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32470966615583, "endTime": 32471298968125, "totalTime": 303023500}, "additional": {"logType": "info", "children": ["eb6cb5e0-f6ca-4f5a-807e-5edaa67305ad", "65fbef83-0386-4469-b4d8-40567f46849e", "bc4b3247-c992-4594-8bef-3847d0426382"], "durationId": "1d2a517d-81ef-4ade-b1ac-c62ac33f4c41"}}, {"head": {"id": "7112adb7-297d-471d-9825-89af1cec7567", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309354708, "endTime": 32471309893916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed."], "detailId": "03872d82-0841-4da1-b414-5ab01ec9f12a", "logId": "8b8926de-347d-4cee-9ca7-ee1961492087"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03872d82-0841-4da1-b414-5ab01ec9f12a", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471300845166}, "additional": {"logType": "detail", "children": [], "durationId": "7112adb7-297d-471d-9825-89af1cec7567"}}, {"head": {"id": "4523e92c-40f3-414d-8fd1-04045c8472f7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471304281583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "182aa274-ca87-4bc6-a01a-487e170a7b4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471304327208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27eeb961-720b-4976-b63c-3de4685ad0f3", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309365125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "032e17a8-1e77-4909-b4e3-168ac3671213", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309551250}, "additional": {"logType": "warn", "children": [], "durationId": "7112adb7-297d-471d-9825-89af1cec7567"}}, {"head": {"id": "790169fe-7c83-4f1d-836d-e704f686d9d2", "name": "entry:default@SignHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309681916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08067aa5-6cf1-4995-ad79-59c564002c11", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309714000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a0e4f9-d113-41ef-9ee0-6211cc9a036b", "name": "entry : default@SignHap cost memory 0.08802032470703125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309840375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8dfa80d-ca38-4f31-a021-829242c6ec8d", "name": "runTaskFromQueue task cost before running: 2 s 830 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309873166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b8926de-347d-4cee-9ca7-ee1961492087", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471309354708, "endTime": 32471309893916, "totalTime": 510208}, "additional": {"logType": "info", "children": [], "durationId": "7112adb7-297d-471d-9825-89af1cec7567"}}, {"head": {"id": "93be5a3c-e0f6-4df8-aa4c-cdfb1832684d", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310781458, "endTime": 32471310933458}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "519c750f-4abe-4c75-80f8-96ab2687e048", "logId": "f3fb5302-f5d5-428a-a257-6ea6b8e43e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "519c750f-4abe-4c75-80f8-96ab2687e048", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310755458}, "additional": {"logType": "detail", "children": [], "durationId": "93be5a3c-e0f6-4df8-aa4c-cdfb1832684d"}}, {"head": {"id": "854f0a88-56ce-4a99-90d2-a6419cb0c912", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310785375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd48993-8f10-478d-b6ac-87fa2bbf3c5e", "name": "entry : assembleHap cost memory 0.01134490966796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310878791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97435415-85a5-41a4-9266-7acd99b599f5", "name": "runTaskFromQueue task cost before running: 2 s 831 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310914958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3fb5302-f5d5-428a-a257-6ea6b8e43e35", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471310781458, "endTime": 32471310933458, "totalTime": 126917}, "additional": {"logType": "info", "children": [], "durationId": "93be5a3c-e0f6-4df8-aa4c-cdfb1832684d"}}, {"head": {"id": "94e43552-b2e7-4581-9fd8-dff2405dfdb2", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471314834916, "endTime": 32471314844833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dda61576-3a0a-4406-a1ce-d339dccad25d", "logId": "313ab155-ec59-4f17-a0a3-441cdf6d79fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "313ab155-ec59-4f17-a0a3-441cdf6d79fe", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471314834916, "endTime": 32471314844833}, "additional": {"logType": "info", "children": [], "durationId": "94e43552-b2e7-4581-9fd8-dff2405dfdb2"}}, {"head": {"id": "04671b9b-231e-4f97-a1ad-978a4e720475", "name": "BUILD SUCCESSFUL in 2 s 835 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471314867666}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "7e44fa66-12ca-4da0-8959-248a99561d1e", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32468480580541, "endTime": 32471314999375}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 33}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.13.1", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "bc616ac0-d934-46eb-a9a3-c9c143e58aa8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315050625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dea358e-c66a-4576-8076-d64e283a1601", "name": "There is no need to refresh cache, since the incremental task upcloud:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315069791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8b181b-da52-42e3-a7f2-dbc0b30191f9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315087750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb29a966-3087-4cc8-a82f-30e1646e864f", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315111708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df7d920c-468c-4825-a8c5-acf68bd9331a", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315210625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63f8a2f-441c-4632-9f93-76d556dae1ee", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315450958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f24738d-1e97-4487-b11d-a7193ae0b541", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315503125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33fcbbfa-8ddd-4aff-81d6-a6636babd8fe", "name": "There is no need to refresh cache, since the incremental task upcloud:default@CreateHarBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315521916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "249132fe-474d-4502-a841-fde48cd2b552", "name": "There is no need to refresh cache, since the incremental task upcloud:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315542500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29a561b3-6d5d-4ae2-94dc-05398aa7cf84", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315563916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1b7341-7573-4a78-b4c1-9a3aca338e2a", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471315617208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9d0caa-6766-435d-9877-2eb2bd3b2e5b", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316006750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84d16fe8-8054-49b2-8f56-2322b05f4ed1", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316097791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a38474bd-1780-4194-88e2-63d3b0205a21", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316117500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d76f71-d8a0-48a7-87b0-08a9aac57503", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316133125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "707f36a6-6667-4ac5-a8b3-3803b796ecc1", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316147958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55c7b077-1760-406f-a573-d11abf10c2a6", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316162791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a62705b-b659-4729-a1ce-d4a7c981c2c7", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316177000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e2282b-11c0-4d33-8157-17f8f25224a4", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316190625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8427035-851d-45cf-8341-a4792f53eb17", "name": "There is no need to refresh cache, since the incremental task upcloud:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316206083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb07a14-aaaf-4c5b-939a-94bd9300a7fe", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316221000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b96fc4-773b-4b28-b908-d239d80cafdd", "name": "There is no need to refresh cache, since the incremental task upcloud:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316238750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d6619f-872d-4aba-b1d5-ded929c9a418", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471316255875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee8e74f-0edf-49e5-a2e6-0493aa660b75", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471317156958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d971fbb3-d50f-465f-a365-7a595ecfb3fb", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321165750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4081d5f3-b7a7-4412-916e-aa700d09a6f3", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321198958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42130a6c-272a-46e3-ae6d-a320e9a54559", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321244916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a5da4b-0d4f-499a-a779-645c61f4dc44", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321281333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b3f2ff-7322-4762-91e9-400c225540d6", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321335583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39d135d4-1f0a-40e8-b58b-9c589ebc1b89", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471321367291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f581201f-3bf1-49b6-aa53-8c854f400e96", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471323850208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "953b72bc-9daf-4d54-bf5a-bb1667831dcf", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471332459750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29fd5fc5-9aa0-4773-b796-5217858109df", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471347974125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee4583f-404a-4a6c-81ab-00e1760c5a6c", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471363202208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c966cf-09f8-451f-9a4b-0670aa8faeb9", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471363644416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea74bd1-9640-4c0e-bd46-3534e9729e51", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471363685416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "263209c0-7f0e-41e7-b5b4-d8a3795db82f", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471363725083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04411e71-0444-4f49-bde3-da6dee9f6e6a", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471363890833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01db1fb0-3533-47d9-89fc-04884b818160", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471364425041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24fb48c3-221f-4933-ad0c-8efb86b9e245", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471364470583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abea7af6-9fc4-4ad2-962d-02cbd39ece56", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/mock/mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471364505458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c23b82eb-0a66-43dc-8f7f-bae64c4f0251", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471364561458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7383a918-a28b-4ba2-a50b-9977155f695d", "name": "Incremental task entry:default@CompileArkTS post-execution cost:49 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471364758416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f111c80-b770-4c1e-9959-57f5b3864738", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365323708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d62c8dc-4727-4214-bc45-96bf2582b953", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365484208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd6e21af-cb21-4892-a97c-f55a03accbd2", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365520000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0caf65b1-9042-4a7a-bb2f-29a750217e2f", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365555583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "950ed537-bb66-4b8d-af2a-061ba7e0d266", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365584166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b62947-7d5f-4eb5-b8ea-ad0ddd1ebaf2", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365612833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "008b5e24-1ce7-47ba-a08b-e521c40d852e", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471365686708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae830799-2d9d-4dfe-aade-cc5e45d7839d", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471366111708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e987f9fd-e217-4fc2-962e-d0055fd8b231", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471366825958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b02a4dc-744f-4805-9e01-e93691dc9681", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471366859458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8446263c-8f18-44b2-aeb4-355d5838c842", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471366898541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28a4cd7e-47a2-479f-9cfa-944466626d5e", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367109291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1184a0-e732-495f-9815-be50ca35965e", "name": "Incremental task entry:default@BuildJS post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367229375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "646cf690-bd00-46f7-bf39-6b552be82b8f", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367250166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0785a7b8-8149-419e-b144-66bcd61e543c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367266333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71289b7f-96ec-4c8e-be39-f8b44e99fb3a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367284625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56352fe3-fdf4-446e-828e-200bc1fea071", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471367301250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2163493f-f114-48e8-b049-ef5ec1125807", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471369207708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75df9ab-8574-4bf2-9a5f-4ea07874cca2", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471371682291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36648bc0-3037-48af-afbc-cf71a12dd137", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471371730541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21945e72-fe93-40b5-a987-f93aa121ebf7", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471373789500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb389dc-244d-47d9-b8d8-fab9ca4cd471", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471373824000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c43a1b5-aa22-4ec6-8714-7d64e8b8e99f", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471373854166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb77ac26-9c36-4edc-92d8-c118cb11e44c", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471373879125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84b77a56-cfc7-4769-9a41-820ff3a62766", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374516333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d9fd849-aee0-4d7f-ada6-272fd9703fc4", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/mapping/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374557958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a3ae74-fee3-45d3-87e3-018014ec225b", "name": "Incremental task entry:default@PackageHap post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374612958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79407a90-aeb4-4db0-947d-daee28c3410e", "name": "Update task entry:default@SignHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374654583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "852ca899-10c3-42cb-a7c7-3ad11cfc5b59", "name": "Update task entry:default@SignHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374675208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0577d3b0-4da5-418d-a828-6769663b9e98", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32471374731416}, "additional": {"logType": "debug", "children": []}}], "workLog": []}