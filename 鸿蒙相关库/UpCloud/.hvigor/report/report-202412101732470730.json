{"version": "2.0", "ppid": 17236, "events": [{"head": {"id": "c1478a0b-1ae1-48ac-a560-e9f1277abda6", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32430406606500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f721f40b-46f7-46f0-bf56-5f62ad6fcd9f", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32430409585583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14544c26-54d3-41e0-a3d5-2b5c58914e96", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32430414966791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cc5e7c2-c7bc-4ea0-bc0c-4cb304d35b6c", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32430415171083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af726dd-621e-4b3c-8521-c2c9ca7d7a34", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436594302875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436601705208, "endTime": 32436764673166}, "additional": {"children": ["9a60d122-9bc3-4c5f-a29f-f401c566da94", "334c51c0-5d25-4e0f-8e8f-a473e5999385", "88301a3a-38b8-48bf-8648-1d3ef647c9aa", "0aa0ae42-6568-4768-819f-f26615f9c3e4", "68bef005-9325-457d-a1c3-f44670719551", "b93a5826-5dcb-4b73-836d-90519091ae17", "3a36cb2a-c059-4d73-8151-2b1897d0dca9"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a60d122-9bc3-4c5f-a29f-f401c566da94", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436601707291, "endTime": 32436626490833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "1bff88d8-5073-46d1-aad4-bf95f72a874a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436626508041, "endTime": 32436763736625}, "additional": {"children": ["0229ee84-ed34-4a82-bd05-1635a7e6f081", "7391b7f0-f9f0-477e-afa2-95eeb7206718", "0f24694c-dab0-4399-96d5-8c982ac39a8c", "cf97a6e6-5c5b-493d-945c-e1b692873391", "67e3a90b-9eba-4e9d-b27f-507589fe0ad4", "741784fc-2d0c-47be-b784-433fb410faaf", "18229f12-b748-4f51-85fe-27be4c3f677f", "409b3b30-1f6e-4938-afff-26530b72e0da", "843425a6-2aaf-49b7-b264-c21e23debc05"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88301a3a-38b8-48bf-8648-1d3ef647c9aa", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436763752833, "endTime": 32436764668291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "faf347ce-0c42-4724-850c-8ca8773c01ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0aa0ae42-6568-4768-819f-f26615f9c3e4", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436764670666, "endTime": 32436764671416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "585ebf67-bf62-4eac-9cc6-b726429f3b3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "68bef005-9325-457d-a1c3-f44670719551", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436609346750, "endTime": 32436609385125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "54724468-0ebb-4590-a973-de07e4ffdc8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54724468-0ebb-4590-a973-de07e4ffdc8d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436609346750, "endTime": 32436609385125}, "additional": {"logType": "info", "children": [], "durationId": "68bef005-9325-457d-a1c3-f44670719551", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "b93a5826-5dcb-4b73-836d-90519091ae17", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436618596708, "endTime": 32436618612083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "265d6d61-320f-426c-9841-3dc66714c110"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "265d6d61-320f-426c-9841-3dc66714c110", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436618596708, "endTime": 32436618612083}, "additional": {"logType": "info", "children": [], "durationId": "b93a5826-5dcb-4b73-836d-90519091ae17", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "3ef90b6f-dd34-43f6-af46-4237fd33a048", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436618652000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a37a55-9708-4a17-9336-06da3fd89a89", "name": "Cache service initialization finished in 8 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436626392250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bff88d8-5073-46d1-aad4-bf95f72a874a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436601707291, "endTime": 32436626490833}, "additional": {"logType": "info", "children": [], "durationId": "9a60d122-9bc3-4c5f-a29f-f401c566da94", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "0229ee84-ed34-4a82-bd05-1635a7e6f081", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436631915291, "endTime": 32436631935125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "62d5af4e-7d52-4c2c-93fe-50a206e9f021"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7391b7f0-f9f0-477e-afa2-95eeb7206718", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436631945708, "endTime": 32436634965708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "efd65c1a-7178-423c-943f-35fdcc27cdbb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f24694c-dab0-4399-96d5-8c982ac39a8c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436634980125, "endTime": 32436678297958}, "additional": {"children": ["3f82e963-f48d-4c99-9b63-bb6c265285cb", "2047e205-acfb-44c2-bf9b-09789fe80144", "217677cb-3ec0-437d-a552-d53eb4fa871f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "5215be88-679b-43dc-953f-f23f04f1614c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf97a6e6-5c5b-493d-945c-e1b692873391", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678306875, "endTime": 32436698759875}, "additional": {"children": ["88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "a222c09b-1ea2-4eed-9786-c85bb17a623b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "b720f1c2-3fc8-4d13-b698-58240d6610fd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67e3a90b-9eba-4e9d-b27f-507589fe0ad4", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698764958, "endTime": 32436741220333}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "a8445695-03e2-4d21-aeb0-af0c358843ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "741784fc-2d0c-47be-b784-433fb410faaf", "name": "exec before all nodes", "description": "Execute before all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436629294083, "endTime": 32436763740041}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18229f12-b748-4f51-85fe-27be4c3f677f", "name": "exec after all nodes", "description": "Execute after all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436629985791, "endTime": 32436763740416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "409b3b30-1f6e-4938-afff-26530b72e0da", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436763621083, "endTime": 32436763723500}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "11722631-ab21-4f4d-a5de-74cd8968cdff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62d5af4e-7d52-4c2c-93fe-50a206e9f021", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436631915291, "endTime": 32436631935125}, "additional": {"logType": "info", "children": [], "durationId": "0229ee84-ed34-4a82-bd05-1635a7e6f081", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "efd65c1a-7178-423c-943f-35fdcc27cdbb", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436631945708, "endTime": 32436634965708}, "additional": {"logType": "info", "children": [], "durationId": "7391b7f0-f9f0-477e-afa2-95eeb7206718", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "3f82e963-f48d-4c99-9b63-bb6c265285cb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436635310166, "endTime": 32436635324125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f24694c-dab0-4399-96d5-8c982ac39a8c", "logId": "50bdf725-4405-465b-babb-1e375cd85e47"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50bdf725-4405-465b-babb-1e375cd85e47", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436635310166, "endTime": 32436635324125}, "additional": {"logType": "info", "children": [], "durationId": "3f82e963-f48d-4c99-9b63-bb6c265285cb", "parent": "5215be88-679b-43dc-953f-f23f04f1614c"}}, {"head": {"id": "2047e205-acfb-44c2-bf9b-09789fe80144", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436636806291, "endTime": 32436678013583}, "additional": {"children": ["a43beb65-e50a-459e-b7bb-7c32ebfb3ac6", "16ec626c-547b-45b5-8ba9-f963ba77b86d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f24694c-dab0-4399-96d5-8c982ac39a8c", "logId": "e1975c94-1719-49ff-b72a-bd0023fb7f91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a43beb65-e50a-459e-b7bb-7c32ebfb3ac6", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436636806875, "endTime": 32436641854125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2047e205-acfb-44c2-bf9b-09789fe80144", "logId": "096e2533-ca44-4da2-86a4-90a88a45a5e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16ec626c-547b-45b5-8ba9-f963ba77b86d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436641861458, "endTime": 32436678006041}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2047e205-acfb-44c2-bf9b-09789fe80144", "logId": "2990a3a5-67b4-4827-909e-0a55566dddc7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a877ddb-b8b6-4b23-b5c1-471f1cfd6fa9", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436636809541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2191dcfc-e40c-4cbc-b2d4-374ce82b512f", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436641791333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "096e2533-ca44-4da2-86a4-90a88a45a5e9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436636806875, "endTime": 32436641854125}, "additional": {"logType": "info", "children": [], "durationId": "a43beb65-e50a-459e-b7bb-7c32ebfb3ac6", "parent": "e1975c94-1719-49ff-b72a-bd0023fb7f91"}}, {"head": {"id": "10253c0e-8d8c-4c45-9c3e-b2ac24544540", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436641865833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9568b291-d5df-4d37-9536-61a6b3e47150", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436647582541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "387438ba-f9b7-4cbe-8687-f0251b260e8c", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436647638875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499396d0-ffba-45ea-8913-934fb75b2176", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436647683250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e50b9fb-1c29-4579-9e45-e971f5b1032e", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436647734375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea0eaf9c-d3cf-4caf-bfc8-301f7314c344", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436649048833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a966043d-4f60-4b63-9d6c-f2f1211aac18", "name": "not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436651200750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61d19e1d-0652-4362-ac0c-bcfebec20d01", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436652482375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afa475e5-a9b0-423a-bcba-8bd5da8a9881", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436656379250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc06abb9-162f-4cee-b70d-dbe90e0fb3cd", "name": "Sdk init in 16 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436668691541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c25935a-9709-468b-8311-1f1d812ebdb1", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436668771708}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 32}, "markType": "other"}}, {"head": {"id": "a949eecf-abb5-4917-98a4-8f189f26cd21", "name": "caseSensitive<PERSON><PERSON>ck<PERSON>ff", "description": "caseSensitive<PERSON><PERSON><PERSON> check is off", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436668797000}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 32}, "markType": "other"}}, {"head": {"id": "cf23ce90-45b5-4b68-85fa-7164cadf1984", "name": "Project task initialization takes 9 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436677438541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc4de18c-5f55-496b-9cbd-57ac98bdb73a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436677503291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "370f5265-102e-48e3-b6cc-9d9e005e3552", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436677525083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40c1a696-401e-441f-9976-77e0aacba2d4", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436677542666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2990a3a5-67b4-4827-909e-0a55566dddc7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436641861458, "endTime": 32436678006041}, "additional": {"logType": "info", "children": [], "durationId": "16ec626c-547b-45b5-8ba9-f963ba77b86d", "parent": "e1975c94-1719-49ff-b72a-bd0023fb7f91"}}, {"head": {"id": "e1975c94-1719-49ff-b72a-bd0023fb7f91", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436636806291, "endTime": 32436678013583}, "additional": {"logType": "info", "children": ["096e2533-ca44-4da2-86a4-90a88a45a5e9", "2990a3a5-67b4-4827-909e-0a55566dddc7"], "durationId": "2047e205-acfb-44c2-bf9b-09789fe80144", "parent": "5215be88-679b-43dc-953f-f23f04f1614c"}}, {"head": {"id": "217677cb-3ec0-437d-a552-d53eb4fa871f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678279083, "endTime": 32436678290416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0f24694c-dab0-4399-96d5-8c982ac39a8c", "logId": "dc40df21-0739-467f-ba7b-0d8ec7a24a3c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc40df21-0739-467f-ba7b-0d8ec7a24a3c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678279083, "endTime": 32436678290416}, "additional": {"logType": "info", "children": [], "durationId": "217677cb-3ec0-437d-a552-d53eb4fa871f", "parent": "5215be88-679b-43dc-953f-f23f04f1614c"}}, {"head": {"id": "5215be88-679b-43dc-953f-f23f04f1614c", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436634980125, "endTime": 32436678297958}, "additional": {"logType": "info", "children": ["50bdf725-4405-465b-babb-1e375cd85e47", "e1975c94-1719-49ff-b72a-bd0023fb7f91", "dc40df21-0739-467f-ba7b-0d8ec7a24a3c"], "durationId": "0f24694c-dab0-4399-96d5-8c982ac39a8c", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678526916, "endTime": 32436688611791}, "additional": {"children": ["25871395-5d36-4b88-bb2b-1e8f5217a03c", "20543928-64a7-419d-9c93-8226e31<PERSON>ae", "b8ae0d6e-0151-4d31-b489-26906bd82f6f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf97a6e6-5c5b-493d-945c-e1b692873391", "logId": "9a3b7fbc-e342-4fde-964e-be73cb45dcc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25871395-5d36-4b88-bb2b-1e8f5217a03c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436679792208, "endTime": 32436679805833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "logId": "141b215b-95a6-46d5-81b1-70056145ba94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "141b215b-95a6-46d5-81b1-70056145ba94", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436679792208, "endTime": 32436679805833}, "additional": {"logType": "info", "children": [], "durationId": "25871395-5d36-4b88-bb2b-1e8f5217a03c", "parent": "9a3b7fbc-e342-4fde-964e-be73cb45dcc2"}}, {"head": {"id": "20543928-64a7-419d-9c93-8226e31<PERSON>ae", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436680640625, "endTime": 32436687991416}, "additional": {"children": ["0df0387c-a271-4d20-901c-7d374a839a80", "65642274-1f0e-41b1-aa5f-de6561402d35"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "logId": "060b8803-8ecb-444f-9e8d-3b30b3549783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0df0387c-a271-4d20-901c-7d374a839a80", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436680641375, "endTime": 32436683245083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "20543928-64a7-419d-9c93-8226e31<PERSON>ae", "logId": "3a1d3d79-5284-4614-9adf-01de67cb5ed6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65642274-1f0e-41b1-aa5f-de6561402d35", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436683253958, "endTime": 32436687986000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "20543928-64a7-419d-9c93-8226e31<PERSON>ae", "logId": "25301b23-a247-4446-a400-21ad0f82dc28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "96af7f13-4b77-4278-922b-944a9aacc1a2", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436680644000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "784c8bd2-b27a-490f-ae48-731a20655391", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436683191416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a1d3d79-5284-4614-9adf-01de67cb5ed6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436680641375, "endTime": 32436683245083}, "additional": {"logType": "info", "children": [], "durationId": "0df0387c-a271-4d20-901c-7d374a839a80", "parent": "060b8803-8ecb-444f-9e8d-3b30b3549783"}}, {"head": {"id": "254c9d44-5607-42cb-a3f5-ccfd3f86205b", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436683258041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71d44d9f-636a-4f21-8f16-0aba3cae3c3b", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436685907166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54af866f-ee69-4f07-8116-f46e49963cf5", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436685973791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d0da76a-3f60-4200-a560-732daea58588", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436686088208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24b8bde3-cc0a-4bad-b29f-46ff6aa6b68f", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436686171750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69e758fb-6e1a-4970-a8b4-8d5778971a43", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436686195500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afced19a-9976-447c-9101-5a20f51c25d0", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436686214166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c4619f8-2e4f-4007-9536-0c2c29af8cb7", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436686238583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e13af06-feb1-4f42-b683-87c36bc659a8", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436687878125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0e68d9-8726-479d-95f5-8c1d8735fa6a", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436687932458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147dc644-ba90-471d-be2a-535a0050215a", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436687952500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e11ed134-3976-4197-9a45-7a58b6eb3f6d", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436687969250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25301b23-a247-4446-a400-21ad0f82dc28", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436683253958, "endTime": 32436687986000}, "additional": {"logType": "info", "children": [], "durationId": "65642274-1f0e-41b1-aa5f-de6561402d35", "parent": "060b8803-8ecb-444f-9e8d-3b30b3549783"}}, {"head": {"id": "060b8803-8ecb-444f-9e8d-3b30b3549783", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436680640625, "endTime": 32436687991416}, "additional": {"logType": "info", "children": ["3a1d3d79-5284-4614-9adf-01de67cb5ed6", "25301b23-a247-4446-a400-21ad0f82dc28"], "durationId": "20543928-64a7-419d-9c93-8226e31<PERSON>ae", "parent": "9a3b7fbc-e342-4fde-964e-be73cb45dcc2"}}, {"head": {"id": "b8ae0d6e-0151-4d31-b489-26906bd82f6f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436688590166, "endTime": 32436688601875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "logId": "e89bd84f-b811-4519-bc92-cb4e91a09a9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e89bd84f-b811-4519-bc92-cb4e91a09a9f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436688590166, "endTime": 32436688601875}, "additional": {"logType": "info", "children": [], "durationId": "b8ae0d6e-0151-4d31-b489-26906bd82f6f", "parent": "9a3b7fbc-e342-4fde-964e-be73cb45dcc2"}}, {"head": {"id": "9a3b7fbc-e342-4fde-964e-be73cb45dcc2", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678526916, "endTime": 32436688611791}, "additional": {"logType": "info", "children": ["141b215b-95a6-46d5-81b1-70056145ba94", "060b8803-8ecb-444f-9e8d-3b30b3549783", "e89bd84f-b811-4519-bc92-cb4e91a09a9f"], "durationId": "88ab1bbf-edf4-48c4-93e7-2c9b2b585b16", "parent": "b720f1c2-3fc8-4d13-b698-58240d6610fd"}}, {"head": {"id": "a222c09b-1ea2-4eed-9786-c85bb17a623b", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436688861333, "endTime": 32436698754583}, "additional": {"children": ["606c05ba-2af1-4b53-bdbb-9fbc91b93df7", "515614d4-33d6-466d-af8d-fa0ac50b098f", "26e4435e-8410-4b1f-bbe0-5ff8c76c8e17"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf97a6e6-5c5b-493d-945c-e1b692873391", "logId": "0b8182e3-0b9f-4d13-a531-802e30a0bfd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "606c05ba-2af1-4b53-bdbb-9fbc91b93df7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690007666, "endTime": 32436690014208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a222c09b-1ea2-4eed-9786-c85bb17a623b", "logId": "6a102d0a-3073-42b6-afc0-1b98dadb7d87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a102d0a-3073-42b6-afc0-1b98dadb7d87", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690007666, "endTime": 32436690014208}, "additional": {"logType": "info", "children": [], "durationId": "606c05ba-2af1-4b53-bdbb-9fbc91b93df7", "parent": "0b8182e3-0b9f-4d13-a531-802e30a0bfd1"}}, {"head": {"id": "515614d4-33d6-466d-af8d-fa0ac50b098f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690780333, "endTime": 32436698242791}, "additional": {"children": ["b34f991a-251d-4076-807b-38ea05865a77", "3245519b-ce60-4fba-ad87-8fb3e085108d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a222c09b-1ea2-4eed-9786-c85bb17a623b", "logId": "26f8119b-a89c-44bc-8f18-ee9f40a2d50b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b34f991a-251d-4076-807b-38ea05865a77", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690781291, "endTime": 32436693233291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "515614d4-33d6-466d-af8d-fa0ac50b098f", "logId": "f20abbf1-1b41-40a9-a7ab-88395eebc7ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3245519b-ce60-4fba-ad87-8fb3e085108d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436693241916, "endTime": 32436698237000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "515614d4-33d6-466d-af8d-fa0ac50b098f", "logId": "d528f8dc-2dfb-49c2-a49e-cd84d9c56edb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5f53144-55e1-4f1a-9c38-74631d041cf0", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690783958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934f5b49-11a8-44d8-983c-5dc298bb73b8", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436693178166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20abbf1-1b41-40a9-a7ab-88395eebc7ca", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690781291, "endTime": 32436693233291}, "additional": {"logType": "info", "children": [], "durationId": "b34f991a-251d-4076-807b-38ea05865a77", "parent": "26f8119b-a89c-44bc-8f18-ee9f40a2d50b"}}, {"head": {"id": "c8f1444b-61fc-475d-b319-a6f06fa8bf83", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436693245833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b1be5e9-f201-44b5-90cf-aec1e62516d1", "name": "Start initialize module-target build option map, moduleName=upcloud, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696507833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b5fd25a-2605-47b0-80f5-947d5c713298", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696598083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d35302b-acde-4f20-8db7-55145651d460", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696694375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "148c1133-d03b-4e32-a12d-3b9159b3d09b", "name": "Module 'upcloud' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696735916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32885c7b-6433-451f-ba67-c4bb8385f33f", "name": "Module 'upcloud' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696800000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0ee7c35-7c99-4d59-a120-68b8a398de30", "name": "End initialize module-target build option map, moduleName=upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696823791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d17baf1f-05e1-4fe0-8c7b-db2e7a3d8cda", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436696856625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed36d0c4-6f5e-41a6-aa33-d144d79b3759", "name": "Module upcloud task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698065958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435ea7c1-63f1-48e0-a94c-6ae25da7e9db", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698132375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71743002-cfe9-4809-b7b7-8fee7e990a9d", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698172958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb5e28b-6e25-441e-8439-6e2cb3622373", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698214583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d528f8dc-2dfb-49c2-a49e-cd84d9c56edb", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436693241916, "endTime": 32436698237000}, "additional": {"logType": "info", "children": [], "durationId": "3245519b-ce60-4fba-ad87-8fb3e085108d", "parent": "26f8119b-a89c-44bc-8f18-ee9f40a2d50b"}}, {"head": {"id": "26f8119b-a89c-44bc-8f18-ee9f40a2d50b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436690780333, "endTime": 32436698242791}, "additional": {"logType": "info", "children": ["f20abbf1-1b41-40a9-a7ab-88395eebc7ca", "d528f8dc-2dfb-49c2-a49e-cd84d9c56edb"], "durationId": "515614d4-33d6-466d-af8d-fa0ac50b098f", "parent": "0b8182e3-0b9f-4d13-a531-802e30a0bfd1"}}, {"head": {"id": "26e4435e-8410-4b1f-bbe0-5ff8c76c8e17", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698741083, "endTime": 32436698748708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a222c09b-1ea2-4eed-9786-c85bb17a623b", "logId": "9abe494f-b898-47dd-822f-52750c25b14b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9abe494f-b898-47dd-822f-52750c25b14b", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698741083, "endTime": 32436698748708}, "additional": {"logType": "info", "children": [], "durationId": "26e4435e-8410-4b1f-bbe0-5ff8c76c8e17", "parent": "0b8182e3-0b9f-4d13-a531-802e30a0bfd1"}}, {"head": {"id": "0b8182e3-0b9f-4d13-a531-802e30a0bfd1", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436688861333, "endTime": 32436698754583}, "additional": {"logType": "info", "children": ["6a102d0a-3073-42b6-afc0-1b98dadb7d87", "26f8119b-a89c-44bc-8f18-ee9f40a2d50b", "9abe494f-b898-47dd-822f-52750c25b14b"], "durationId": "a222c09b-1ea2-4eed-9786-c85bb17a623b", "parent": "b720f1c2-3fc8-4d13-b698-58240d6610fd"}}, {"head": {"id": "b720f1c2-3fc8-4d13-b698-58240d6610fd", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436678306875, "endTime": 32436698759875}, "additional": {"logType": "info", "children": ["9a3b7fbc-e342-4fde-964e-be73cb45dcc2", "0b8182e3-0b9f-4d13-a531-802e30a0bfd1"], "durationId": "cf97a6e6-5c5b-493d-945c-e1b692873391", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "bdaeb56c-310b-433e-9a69-1acd4f2f1aee", "name": "watch files: [\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1846 more items\n]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436710040625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c94513c-1142-4b2c-a1aa-574d56e14b02", "name": "hvigorfile, resolve hvigorfile dependencies in 43 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436741100916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8445695-03e2-4d21-aeb0-af0c358843ed", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436698764958, "endTime": 32436741220333}, "additional": {"logType": "info", "children": [], "durationId": "67e3a90b-9eba-4e9d-b27f-507589fe0ad4", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "843425a6-2aaf-49b7-b264-c21e23debc05", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436741721958, "endTime": 32436741850250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "logId": "ae1b1502-3894-4d67-be83-eafa24ab7495"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "257ed9c3-d378-4e6c-a7b7-0034bf568d11", "name": "project has submodules:entry,upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436741741625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7e15da2-2b7e-43e4-81ca-04149d7a848c", "name": "module:upcloud no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436741828041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae1b1502-3894-4d67-be83-eafa24ab7495", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436741721958, "endTime": 32436741850250}, "additional": {"logType": "info", "children": [], "durationId": "843425a6-2aaf-49b7-b264-c21e23debc05", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "09cedd79-f841-4483-ac0b-95b39adbb82d", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436742596750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14f4ab11-f2ce-4c56-9532-6a7410e8df64", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436745405583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5716b375-174a-407e-b9ef-36996603169a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436748525958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "31e90efd-43d7-4b37-a758-6c0eb1fdff71", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436748824500}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8fb6155b-7156-4e0d-9789-71433b0db684", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436749110000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0378bb80-2b33-41f5-922d-804965228ef7", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436749490041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "95313a34-7cb8-406b-8ddc-3e733eafcc14", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436749822666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "ae261901-ea68-4642-aa2e-b764879714bd", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436750129333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "29987f08-a067-496c-bc6d-fe8f413bb95c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436750416875}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "98a81222-15f1-4c24-b8d6-0af0700ec18a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436750663666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "6a30efd9-1c83-4ea4-b7b5-e38476c8aef1", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436750940083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e978755a-4873-4ad7-a979-0e66cd25b384", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436751209041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d40e758c-1667-434d-8b40-d58e4787dff6", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436751458166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "ed804dfb-92a8-41eb-b258-2665eb9ac0c0", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436751719375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "466d908c-c861-471c-abf3-f1ba1e6b64e6", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436751970958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "fa348924-7544-428f-a93c-fa1b7dafe6dd", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436752226416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "3fd74c10-5664-4427-bd30-67933b444788", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436752498375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "f9014c53-3268-4c6e-a881-3bdc5f23eefd", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436753258708}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "39bdb22c-2644-403e-be61-559bd6d1cd4c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436753550166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8c44fb2b-3230-464c-8a9b-f707054522ae", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436753819958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5061d5a8-2488-4514-b6fb-ac0e5db1172a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436754089750}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "45a6a9f4-3b19-46aa-9e04-96df8c0e078a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436754338083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2e3a882f-916b-4f13-a02e-d113ba7b43eb", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436754580250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "043a8455-b873-4806-ac19-e93bb8bb9dac", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436754825625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "96706d93-64fd-41d0-99e4-84c58ccf7696", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436755106833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2dc3e517-775f-413c-bc8d-649f49f9daca", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436755358833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8ad57563-37a7-4fe2-a8b9-49ea82b81a1c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436755607125}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "01d44c12-cfbc-4ffd-9d13-4d8edb90f6a1", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436756004291}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "eecb065c-8eae-477f-befd-45b9ae877027", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436756363250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "578f4693-7379-46d9-8bea-c61a77ec9f04", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436756636875}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cd53ec8a-1a72-498f-9cf3-6b137cbd600f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436756901333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "23b544dd-f0c5-4b74-bffd-b781b3096891", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436757155625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "382d4315-0d9c-4288-b6ad-fc5efac8430a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436757452166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a8eb4d2a-8982-46d8-a809-5597c95596ba", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436757761875}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "56b3cad6-635f-4ee4-847f-7e672486b12a", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436758035041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1de67a42-6698-49a7-892f-775b212a4dc9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436758277625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "15216b33-6341-41f9-8702-bc0015279a92", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436758539375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2793d0dd-7fbf-460d-94ec-6fefdca0c9f8", "name": "Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436758598166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b70140a5-325b-464a-abe1-73b03c3c5dfa", "name": "Module UpCloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436758630125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8ebc568-5083-4370-b965-c4c1eb3a0af2", "name": "Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436759534541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cc34693-4e1b-42c5-9ec2-1cf28b10cff8", "name": "Module entry's total dependency: 9", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436759565125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a69f781-ce22-4176-812f-a6e926665756", "name": "Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436762454208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49e841ad-240c-4f67-9864-a14ef0655b9e", "name": "Module upcloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436762509958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98bbe7b6-b0e5-4414-91f1-0a10a429cf83", "name": "Configuration phase cost:132 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436763641458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11722631-ab21-4f4d-a5de-74cd8968cdff", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436763621083, "endTime": 32436763723500}, "additional": {"logType": "info", "children": [], "durationId": "409b3b30-1f6e-4938-afff-26530b72e0da", "parent": "71c59cf3-1ad0-431d-8d88-15f844e8b904"}}, {"head": {"id": "71c59cf3-1ad0-431d-8d88-15f844e8b904", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436626508041, "endTime": 32436763736625}, "additional": {"logType": "info", "children": ["62d5af4e-7d52-4c2c-93fe-50a206e9f021", "efd65c1a-7178-423c-943f-35fdcc27cdbb", "5215be88-679b-43dc-953f-f23f04f1614c", "b720f1c2-3fc8-4d13-b698-58240d6610fd", "a8445695-03e2-4d21-aeb0-af0c358843ed", "11722631-ab21-4f4d-a5de-74cd8968cdff", "ae1b1502-3894-4d67-be83-eafa24ab7495"], "durationId": "334c51c0-5d25-4e0f-8e8f-a473e5999385", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "3a36cb2a-c059-4d73-8151-2b1897d0dca9", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436764649958, "endTime": 32436764660875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ad6f0e11-5280-4466-99b7-93df65b6e9dd", "logId": "050262c7-8d12-4b6b-8390-74df65a4a6dd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "050262c7-8d12-4b6b-8390-74df65a4a6dd", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436764649958, "endTime": 32436764660875}, "additional": {"logType": "info", "children": [], "durationId": "3a36cb2a-c059-4d73-8151-2b1897d0dca9", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "faf347ce-0c42-4724-850c-8ca8773c01ba", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436763752833, "endTime": 32436764668291}, "additional": {"logType": "info", "children": [], "durationId": "88301a3a-38b8-48bf-8648-1d3ef647c9aa", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "585ebf67-bf62-4eac-9cc6-b726429f3b3f", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436764670666, "endTime": 32436764671416}, "additional": {"logType": "info", "children": [], "durationId": "0aa0ae42-6568-4768-819f-f26615f9c3e4", "parent": "15862e82-6f32-468a-8c40-c9df2f6ccb7a"}}, {"head": {"id": "15862e82-6f32-468a-8c40-c9df2f6ccb7a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436601705208, "endTime": 32436764673166}, "additional": {"logType": "info", "children": ["1bff88d8-5073-46d1-aad4-bf95f72a874a", "71c59cf3-1ad0-431d-8d88-15f844e8b904", "faf347ce-0c42-4724-850c-8ca8773c01ba", "585ebf67-bf62-4eac-9cc6-b726429f3b3f", "54724468-0ebb-4590-a973-de07e4ffdc8d", "265d6d61-320f-426c-9841-3dc66714c110", "050262c7-8d12-4b6b-8390-74df65a4a6dd"], "durationId": "ad6f0e11-5280-4466-99b7-93df65b6e9dd"}}, {"head": {"id": "fc10bc36-8f7b-4f95-8bab-9e765c1811e3", "name": "Configuration task cost before running: 169 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436764817375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84026d28-a78e-44d8-98c5-f2c5b67c1038", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436766703625, "endTime": 32436768470375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "dc702cf5-6ed3-4b2d-8527-4cee4b88cb42", "logId": "cf8ddb08-5ee9-4f82-9140-c7b45d3d6c53"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc702cf5-6ed3-4b2d-8527-4cee4b88cb42", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436765485583}, "additional": {"logType": "detail", "children": [], "durationId": "84026d28-a78e-44d8-98c5-f2c5b67c1038"}}, {"head": {"id": "3d59e6e8-c99b-4fd6-8ed1-13a38c864fda", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436765785041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8b21e76-1077-48f5-9d06-991a49ca925c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436765824708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46e22654-37a3-49a5-9fbc-903e96fec577", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436766708708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1124e7c-38b5-401a-a58c-8354b113133e", "name": "Incremental task entry:default@PreBuild pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436768401000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "74024839-4fea-4841-916c-24d94c9c47e8", "name": "entry : default@PreBuild cost memory 0.22431182861328125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436768447875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf8ddb08-5ee9-4f82-9140-c7b45d3d6c53", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436766703625, "endTime": 32436768470375}, "additional": {"logType": "info", "children": [], "durationId": "84026d28-a78e-44d8-98c5-f2c5b67c1038"}}, {"head": {"id": "902c1b50-36fd-4a05-9a53-1df7504f4d2a", "name": "upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436770400625, "endTime": 32436771442125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Verification", "taskRunReasons": [], "detailId": "5aa02c86-2e78-41f2-8b14-7909fa017eac", "logId": "1bbc6049-8717-4c16-844f-d01055cbf1a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5aa02c86-2e78-41f2-8b14-7909fa017eac", "name": "create upcloud:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436769492791}, "additional": {"logType": "detail", "children": [], "durationId": "902c1b50-36fd-4a05-9a53-1df7504f4d2a"}}, {"head": {"id": "612c26fc-c6c0-4e60-9e8d-7ef8994b984f", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436769625500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b22b9f1-6884-4e21-b61d-274e2cda805f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436769652125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbaa5a5f-9137-4799-a887-a13fe8301572", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436769668375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8437659f-a706-4b6c-9623-ebbf4a7255f9", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436769685250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f7c9cb7-1707-4beb-9073-9526f7c14c84", "name": "Executing task :upcloud:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436770403416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bc4aebc-1a7c-41df-bd16-c452b4d918bb", "name": "Incremental task upcloud:default@PreBuild pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436771381750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c0b0a7-f6f1-4839-bc86-eab08e54f5a9", "name": "upcloud : default@PreBuild cost memory 0.17092132568359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436771421041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bbc6049-8717-4c16-844f-d01055cbf1a5", "name": "UP-TO-DATE :upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436770400625, "endTime": 32436771442125}, "additional": {"logType": "info", "children": [], "durationId": "902c1b50-36fd-4a05-9a53-1df7504f4d2a"}}, {"head": {"id": "43bfb109-684e-40df-a813-5ee116c3ef81", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772775541, "endTime": 32436773253208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "0e0937d0-eae9-4212-a597-cd8115cc4ac3", "logId": "43809c02-9951-4f57-8436-85a14c3193f8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e0937d0-eae9-4212-a597-cd8115cc4ac3", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772320083}, "additional": {"logType": "detail", "children": [], "durationId": "43bfb109-684e-40df-a813-5ee116c3ef81"}}, {"head": {"id": "a2ba77a5-8f73-4d9f-a07f-7c04485ac7fc", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772446875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e99c590-07e9-4305-968d-9b87fffc874c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772480833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1427cf1c-75ca-41b2-aba9-365c59324f02", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772779166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f9b426-8621-4c36-a6b4-3a36d13eb1a0", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436773017333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e7f19d0-aa6c-43c3-a7a3-01e91b8183f4", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436773198500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ede815e-d9d2-4ed9-9fe9-02dfae9d96db", "name": "entry : default@GenerateMetadata cost memory 0.0811767578125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436773227833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43809c02-9951-4f57-8436-85a14c3193f8", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436772775541, "endTime": 32436773253208}, "additional": {"logType": "info", "children": [], "durationId": "43bfb109-684e-40df-a813-5ee116c3ef81"}}, {"head": {"id": "2bf1c7f2-ed8d-4b73-8e24-3ec30c59516c", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774620250, "endTime": 32436774799833}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "18f603df-00cf-42f2-a54c-dbe3d001fd96", "logId": "681b9696-b268-461d-8975-4ecd87e7c522"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18f603df-00cf-42f2-a54c-dbe3d001fd96", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436773840291}, "additional": {"logType": "detail", "children": [], "durationId": "2bf1c7f2-ed8d-4b73-8e24-3ec30c59516c"}}, {"head": {"id": "2438caf7-bfc6-4635-8267-4cead85d3171", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436773970666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97e9b7b2-5451-4a31-991c-dc3bd1e6e5a9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774033541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13758cd0-85c4-47c2-ab29-e73de8491696", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774642750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7e91848-fdc0-422e-960d-d24a61622387", "name": "entry : default@PreCheckSyscap cost memory 0.01282501220703125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774714500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb48ab3-a450-4904-ae8d-7899d24b7803", "name": "runTaskFromQueue task cost before running: 179 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774754916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "681b9696-b268-461d-8975-4ecd87e7c522", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436774620250, "endTime": 32436774799833, "totalTime": 123667}, "additional": {"logType": "info", "children": [], "durationId": "2bf1c7f2-ed8d-4b73-8e24-3ec30c59516c"}}, {"head": {"id": "2eaa6c95-76e9-427a-bda9-ac4f6b756c71", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781388583, "endTime": 32436781758333}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "6442138e-7cd2-4b53-84eb-6c89baf41a59", "logId": "fd552bc5-bddb-485c-b929-44d8e8672708"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6442138e-7cd2-4b53-84eb-6c89baf41a59", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436775535500}, "additional": {"logType": "detail", "children": [], "durationId": "2eaa6c95-76e9-427a-bda9-ac4f6b756c71"}}, {"head": {"id": "cc28da32-2309-4f5f-bccd-6243f7ed665c", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436775723166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bcbb447-3be6-43d4-93ca-829bfae07b83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436775748916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b7ca634-dadf-41d2-bd37-91b64b541a3d", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781399791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a27bfb2-6ab6-4981-af81-2f6e475f87b9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781569750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75ef7304-b636-4200-8db6-093b8222c05a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.055328369140625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781686708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab47f9a8-2f8b-41f9-9b3f-69055c6eea5c", "name": "runTaskFromQueue task cost before running: 186 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781728500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd552bc5-bddb-485c-b929-44d8e8672708", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436781388583, "endTime": 32436781758333, "totalTime": 330542}, "additional": {"logType": "info", "children": [], "durationId": "2eaa6c95-76e9-427a-bda9-ac4f6b756c71"}}, {"head": {"id": "5b80ba53-ebc9-4f74-8020-a435ed7425f6", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436783755500, "endTime": 32436784639458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "c5f20683-ec0e-4fc3-a7f3-2b6c83517864", "logId": "a44f3d7d-3aaa-4cfd-97d6-91d43ad421dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5f20683-ec0e-4fc3-a7f3-2b6c83517864", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436782543625}, "additional": {"logType": "detail", "children": [], "durationId": "5b80ba53-ebc9-4f74-8020-a435ed7425f6"}}, {"head": {"id": "d8f0662b-4d99-4f6b-9ba8-7dd4124b7c95", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436782809125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ffcae63-2377-489f-b653-34b07bdb109c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436782858291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "938ce839-8074-4105-8e5d-1240757928f7", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436783763208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76e93486-d3d7-4bf1-8469-0a30f879240e", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436784450083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e117842-9bf6-4863-8794-2235c20680d9", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436784520125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50701085-5307-480d-a016-3a7b28a05a73", "name": "entry : default@ProcessIntegratedHsp cost memory 0.0827789306640625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436784585041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedd2a69-eb3d-42b2-8448-3e34106c4a50", "name": "runTaskFromQueue task cost before running: 189 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436784615416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44f3d7d-3aaa-4cfd-97d6-91d43ad421dc", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436783755500, "endTime": 32436784639458, "totalTime": 856084}, "additional": {"logType": "info", "children": [], "durationId": "5b80ba53-ebc9-4f74-8020-a435ed7425f6"}}, {"head": {"id": "3591f1e4-aa70-464d-b169-369af3b49de0", "name": "upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786034208, "endTime": 32436786396083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Generate", "taskRunReasons": [], "detailId": "4b571f9d-cd73-4646-af51-3e85e4b82aa7", "logId": "ab5debd9-fc55-4cfb-a341-b53e970a1fe9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b571f9d-cd73-4646-af51-3e85e4b82aa7", "name": "create upcloud:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436785502416}, "additional": {"logType": "detail", "children": [], "durationId": "3591f1e4-aa70-464d-b169-369af3b49de0"}}, {"head": {"id": "8c5cc752-2c0b-47b7-b25c-e503f5a7aaa5", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436785662916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2bd81c-eead-451f-8fba-52fd1fbe7976", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436785696750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec38294-f702-44cc-b8ea-1e22dbd6dab0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436785715000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d290459-47ba-410b-81d1-d660a93ee949", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436785732875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb0f6ed0-7d9f-4a6f-9edd-6547d9422d28", "name": "Executing task :upcloud:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786038125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d868a1a1-8f00-41b6-8cce-759bbddb7364", "name": "Task 'upcloud:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786135125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4bd5268-cece-46c6-a797-d0543de131f7", "name": "Incremental task upcloud:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786348041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfb398be-4dc7-44e7-98dd-ae611cf12215", "name": "upcloud : default@CreateHarBuildProfile cost memory 0.073944091796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786373375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab5debd9-fc55-4cfb-a341-b53e970a1fe9", "name": "UP-TO-DATE :upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786034208, "endTime": 32436786396083}, "additional": {"logType": "info", "children": [], "durationId": "3591f1e4-aa70-464d-b169-369af3b49de0"}}, {"head": {"id": "0c23908f-5ae1-4118-a4b5-70891b43e008", "name": "upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787608875, "endTime": 32436787708791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "61a5f4b6-abcc-4aad-a918-377eea8d5082", "logId": "de1eebc2-9aa0-4b7f-9cc1-e48d70992fb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61a5f4b6-abcc-4aad-a918-377eea8d5082", "name": "create upcloud:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436786895250}, "additional": {"logType": "detail", "children": [], "durationId": "0c23908f-5ae1-4118-a4b5-70891b43e008"}}, {"head": {"id": "d3679bc5-785e-4950-bdbe-27ecbfa4eb40", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787061291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f06fb17-1565-41cb-8645-931cd5b7c069", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787092416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2faf7e1-906e-44db-968a-db2618e4ad7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787110958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b88434cd-ebb3-4794-a058-9d443c9a3bae", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787128916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8613798b-5526-4326-8e39-2c7cad67a414", "name": "Executing task :upcloud:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787612333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a4d75b4-fe47-419c-92c2-09a5d7fc1bfd", "name": "upcloud : default@ConfigureCmake cost memory 0.01369476318359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787660791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71ea664f-8834-41e2-857d-f65cd0a63f6c", "name": "runTaskFromQueue task cost before running: 192 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787692000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de1eebc2-9aa0-4b7f-9cc1-e48d70992fb0", "name": "Finished :upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436787608875, "endTime": 32436787708791, "totalTime": 73750}, "additional": {"logType": "info", "children": [], "durationId": "0c23908f-5ae1-4118-a4b5-70891b43e008"}}, {"head": {"id": "aa981a7e-c867-4b8a-9fb0-9973a6ae90e9", "name": "upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436789534083, "endTime": 32436790155125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Config", "taskRunReasons": [], "detailId": "23a73827-006b-415e-830b-9804cce14f5d", "logId": "1931bbcb-38ff-40ca-afda-4f742b8082ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23a73827-006b-415e-830b-9804cce14f5d", "name": "create upcloud:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436788320000}, "additional": {"logType": "detail", "children": [], "durationId": "aa981a7e-c867-4b8a-9fb0-9973a6ae90e9"}}, {"head": {"id": "03657759-36a1-480a-bf24-dddc454830cf", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436788486875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77755e3e-7083-4efc-a7dd-a1b5e2c8e61a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436788509625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af5027b-f242-4bbb-bc6e-220ed8e00ca7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436788524166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4708c0b9-ceaa-4094-88a4-ff1a80519e11", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436788539291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38918db-9394-44e0-a5cd-13c621f07688", "name": "Executing task :upcloud:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436789542750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91c095c4-9b2a-417e-bdd0-163d777d9097", "name": "Incremental task upcloud:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436790042000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b3e6bda-e149-4ef2-a216-fffc856503cb", "name": "upcloud : default@MergeProfile cost memory 0.09961700439453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436790106583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1931bbcb-38ff-40ca-afda-4f742b8082ff", "name": "UP-TO-DATE :upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436789534083, "endTime": 32436790155125}, "additional": {"logType": "info", "children": [], "durationId": "aa981a7e-c867-4b8a-9fb0-9973a6ae90e9"}}, {"head": {"id": "8d4fcdaf-9a5f-4617-8692-0b2729a39244", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791440041, "endTime": 32436791956541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "dfd2fb44-e0a4-4fb3-947b-6b0466c225f4", "logId": "00fd445f-01b9-435e-940f-bb56ee45c35c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfd2fb44-e0a4-4fb3-947b-6b0466c225f4", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436790856375}, "additional": {"logType": "detail", "children": [], "durationId": "8d4fcdaf-9a5f-4617-8692-0b2729a39244"}}, {"head": {"id": "ac04f5fe-3880-41a8-b0d3-e6fdfed70637", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791003541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa19dbbc-9924-4627-bf16-f1006eb973eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791031208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bc7cc7c-02b6-4cda-ba94-e54bd49f3839", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791461083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2f38f57-0b86-40e1-a0d6-52f2a72380c9", "name": "File: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791497500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "878dfc28-dc1d-45a3-9ac2-a31ea2291604", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791573375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bfce2e5-cbb1-44c1-bcf9-bf1f397af5cf", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791848916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a439f152-0eb9-4574-951b-8238a7c4499e", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791875083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d2aec8-f347-44b6-9d18-7ffebe67f1f7", "name": "entry : default@SyscapTransform cost memory 0.09991455078125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791910625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bce22f6-00aa-45e1-82ef-ce06bd30e975", "name": "runTaskFromQueue task cost before running: 196 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791937916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00fd445f-01b9-435e-940f-bb56ee45c35c", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436791440041, "endTime": 32436791956541, "totalTime": 492458}, "additional": {"logType": "info", "children": [], "durationId": "8d4fcdaf-9a5f-4617-8692-0b2729a39244"}}, {"head": {"id": "91557418-6160-465c-bb06-ea571576bb35", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436793435083, "endTime": 32436794403416}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "52cfed89-0410-4ed2-89e6-37feddac434d", "logId": "86dcebbc-e841-4e2b-9199-603ab048428f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52cfed89-0410-4ed2-89e6-37feddac434d", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436792630208}, "additional": {"logType": "detail", "children": [], "durationId": "91557418-6160-465c-bb06-ea571576bb35"}}, {"head": {"id": "1ec737bd-458b-47f3-854b-139dd6ac984c", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436792779125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e20265b9-f1d8-4349-b836-dfbde5d16902", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436792821333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f820b34-fd84-4303-9e82-b3e13dafebcb", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436793439208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a5d2b89-f440-48b3-b06a-f804322a3d5d", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436794350708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ee24687-fa9d-4e24-b1f5-7663d3580a25", "name": "entry : default@ProcessRouterMap cost memory 0.18309783935546875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436794382166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86dcebbc-e841-4e2b-9199-603ab048428f", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436793435083, "endTime": 32436794403416}, "additional": {"logType": "info", "children": [], "durationId": "91557418-6160-465c-bb06-ea571576bb35"}}, {"head": {"id": "ef127ddd-fa30-4ee7-a2b4-08888e4f2c21", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795890416, "endTime": 32436796604000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4a924a6c-0226-4469-ae60-780487724f3e", "logId": "645cb43c-835a-4534-a2f9-fcbe210c6de7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a924a6c-0226-4469-ae60-780487724f3e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795174083}, "additional": {"logType": "detail", "children": [], "durationId": "ef127ddd-fa30-4ee7-a2b4-08888e4f2c21"}}, {"head": {"id": "c943bb78-27b4-43da-a85f-96353bfae4bd", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795324500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce18b86-f983-4da6-96b0-f9c4ad472f0f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795356083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710b1b34-4a27-4129-a8ba-406d54ea6327", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795895750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9376230-3c5e-4cc7-b75c-32e25a2a37ec", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436796210375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a456411-387a-4a71-a252-3b72bf0d639c", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436796529916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa17c1f-f3d8-4455-8884-f16f94f594e3", "name": "entry : default@CreateBuildProfile cost memory 0.08626556396484375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436796568666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "645cb43c-835a-4534-a2f9-fcbe210c6de7", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436795890416, "endTime": 32436796604000}, "additional": {"logType": "info", "children": [], "durationId": "ef127ddd-fa30-4ee7-a2b4-08888e4f2c21"}}, {"head": {"id": "009d5e88-a896-47c3-9b13-77f4a910d4d7", "name": "upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436800168208, "endTime": 32436800386791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "4ab4cb27-2438-4948-ae96-dd13e7a6908c", "logId": "c801e5ae-1b48-4069-894d-eb060228a106"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ab4cb27-2438-4948-ae96-dd13e7a6908c", "name": "create upcloud:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436797615833}, "additional": {"logType": "detail", "children": [], "durationId": "009d5e88-a896-47c3-9b13-77f4a910d4d7"}}, {"head": {"id": "a531e97a-9087-42f3-817a-0f3efc2137fa", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436797952083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06cf73cb-ec69-42a9-add0-a077228363b7", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436799583541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3a75612-a191-4aa9-94d5-4c2b70e0780d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436799605750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3f5579-67a7-452c-9a07-0e22d3ec249b", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436799626208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcd8786a-9752-44c1-bf1f-97244d4a8329", "name": "Executing task :upcloud:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436800173833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91c6445-b179-433c-89e8-99fd8637e764", "name": "upcloud : default@BuildNativeWithCmake cost memory 0.01374053955078125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436800249708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "456e09fb-51e3-4367-9293-bc867368fbdb", "name": "runTaskFromQueue task cost before running: 205 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436800362375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c801e5ae-1b48-4069-894d-eb060228a106", "name": "Finished :upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436800168208, "endTime": 32436800386791, "totalTime": 183584}, "additional": {"logType": "info", "children": [], "durationId": "009d5e88-a896-47c3-9b13-77f4a910d4d7"}}, {"head": {"id": "6a240e61-3c51-4ea6-a7c1-bf5a7e49209d", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436803116416, "endTime": 32436804024541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "01e35587-302c-4478-8689-24956b89ff1c", "logId": "368186cf-236b-470f-ba55-e9d4b6063f87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01e35587-302c-4478-8689-24956b89ff1c", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436801261833}, "additional": {"logType": "detail", "children": [], "durationId": "6a240e61-3c51-4ea6-a7c1-bf5a7e49209d"}}, {"head": {"id": "4a36532d-a5fb-4798-bec4-1f88a692ed0d", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436801414375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa0f6317-f9e9-4ac2-bf3b-a0cd10ddb18c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436801453041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "120cefa1-4679-4a85-839c-bbf747ef09ab", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436803120500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0315d263-b0ca-4c99-b857-f2782b24d092", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436803805958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d226adeb-2904-4fa8-b0fe-22740251a023", "name": "entry : default@MergeProfile cost memory 0.1121826171875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436803996958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368186cf-236b-470f-ba55-e9d4b6063f87", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436803116416, "endTime": 32436804024541}, "additional": {"logType": "info", "children": [], "durationId": "6a240e61-3c51-4ea6-a7c1-bf5a7e49209d"}}, {"head": {"id": "e62d09a6-1ce5-4d1e-a75d-c3e38b0d713e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436811204208, "endTime": 32436835410541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a0efed84-b4a7-41a6-a1d9-0d523ec0c187", "logId": "eb93b53d-6114-47ac-a48e-f3fa695a8af5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0efed84-b4a7-41a6-a1d9-0d523ec0c187", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436805045500}, "additional": {"logType": "detail", "children": [], "durationId": "e62d09a6-1ce5-4d1e-a75d-c3e38b0d713e"}}, {"head": {"id": "9424b53d-d9c4-4759-9d9d-1350bc66d645", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436805298583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda70b0d-9437-48ae-976c-7dc7c8a06fd1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436805373250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23e7823b-f94b-45e9-aecf-0c09294bc304", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436805855166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be438f99-3377-462b-b6f6-eaf2a325db6b", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436811211458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6720aa55-2229-4c1f-86bb-578f64a105e0", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436812104375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb351f13-ffe4-4822-8b18-c9fdc70d6b60", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436816390958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "481ada3f-f590-4a2d-bad7-afa5e6060ea4", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436822804875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8f95a69-ba3e-4c0e-bca3-d8fad55442e8", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436824268291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb2899b3-8898-47d0-b175-bbd2416f5908", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436826884916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6237ecff-9689-4fd5-bc2c-70db3d24846d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 18 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436835307250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13ee45a1-8f3c-4878-a1c0-21a9cdf1f6f6", "name": "entry : default@GenerateLoaderJson cost memory -4.7521820068359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436835382166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb93b53d-6114-47ac-a48e-f3fa695a8af5", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436811204208, "endTime": 32436835410541}, "additional": {"logType": "info", "children": [], "durationId": "e62d09a6-1ce5-4d1e-a75d-c3e38b0d713e"}}, {"head": {"id": "b077397e-b8dc-4889-a3f1-f39dc09f43c9", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436836672583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a810e1a-00a0-435d-8798-90a86e3039e6", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436838048083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3658e28c-cc72-4b01-a0e2-47b65c908e45", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436840278291, "endTime": 32436840413875}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5d01619f-b320-42b8-9426-d9286ae23cd4", "logId": "d5206527-d20d-46a5-9e7f-fd579cfc0d41"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d01619f-b320-42b8-9426-d9286ae23cd4", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436839600166}, "additional": {"logType": "detail", "children": [], "durationId": "3658e28c-cc72-4b01-a0e2-47b65c908e45"}}, {"head": {"id": "5c28bc9a-e557-47fe-8fd8-df4360339a57", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436839784291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d000eef8-7da0-4cb5-83ad-a37b6f2d80ba", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436839825291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aed4f81d-2ca7-4318-9a4c-2389571e8a6e", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436840283958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf226d79-9647-4c58-b2fb-a6b90d3256fe", "name": "entry : default@ConfigureCmake cost memory 0.0127716064453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436840355125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d49d3684-01c8-4713-8029-073c3974980b", "name": "runTaskFromQueue task cost before running: 245 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436840392375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5206527-d20d-46a5-9e7f-fd579cfc0d41", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436840278291, "endTime": 32436840413875, "totalTime": 102625}, "additional": {"logType": "info", "children": [], "durationId": "3658e28c-cc72-4b01-a0e2-47b65c908e45"}}, {"head": {"id": "fdc179e3-5a42-4464-85d3-9a7458158cef", "name": "upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841752250, "endTime": 32436842194208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "f1f019b2-af52-47ec-bf3e-7987b5080998", "logId": "fe8e2c8c-7178-4435-bf98-02bd9721ef11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1f019b2-af52-47ec-bf3e-7987b5080998", "name": "create upcloud:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841076000}, "additional": {"logType": "detail", "children": [], "durationId": "fdc179e3-5a42-4464-85d3-9a7458158cef"}}, {"head": {"id": "b55505c6-2bd0-4891-b4a0-4ea1db832a64", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841283958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8debed57-0b72-4c38-b2f8-c7363669f3e5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841332750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114291c0-642c-4140-9ee3-8411471a1df3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841349500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe138b5c-fb9e-4ae5-87da-a7273a7951b4", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841366041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4df292a9-3a16-4f91-a441-5d14f5e07987", "name": "Executing task :upcloud:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841757458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59aa67a2-b157-45a6-b56a-5eee76fd1493", "name": "upcloud : default@BuildNativeWithNinja cost memory 0.028564453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436842129250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53865725-7316-488b-a792-1886f1dbe609", "name": "runTaskFromQueue task cost before running: 246 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436842172416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8e2c8c-7178-4435-bf98-02bd9721ef11", "name": "Finished :upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436841752250, "endTime": 32436842194208, "totalTime": 408625}, "additional": {"logType": "info", "children": [], "durationId": "fdc179e3-5a42-4464-85d3-9a7458158cef"}}, {"head": {"id": "17cc1c98-51c7-4894-9d88-3015a2b1833c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436843323666, "endTime": 32436844368250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8b8f3399-a603-4d64-ba66-0ba288f5f9a7", "logId": "4b1eb34c-ccc1-4f58-bbef-b43470ece985"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b8f3399-a603-4d64-ba66-0ba288f5f9a7", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436842920333}, "additional": {"logType": "detail", "children": [], "durationId": "17cc1c98-51c7-4894-9d88-3015a2b1833c"}}, {"head": {"id": "38f86ed0-8deb-4f50-ab4e-9103e542ce32", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436843064000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3a97648-93d6-40cc-a730-74fcb8c4b689", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436843088541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af57aeee-a516-49bc-ae3d-54893186c626", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436843327250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b342fb5-03cd-4664-9bb9-bfaf7207a4f7", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436844306708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "471a0b16-205d-4219-b3ac-11e3a7ba04e7", "name": "entry : default@MakePackInfo cost memory 0.12664031982421875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436844344500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b1eb34c-ccc1-4f58-bbef-b43470ece985", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436843323666, "endTime": 32436844368250}, "additional": {"logType": "info", "children": [], "durationId": "17cc1c98-51c7-4894-9d88-3015a2b1833c"}}, {"head": {"id": "e67b63bf-615c-44dc-8cfe-793921770d1e", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436846345083, "endTime": 32436846641125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "f9b54e97-bca1-4b6e-98e8-3a85a2b45a0a", "logId": "d55a91b4-521b-4c02-85ea-0c6bd9925c73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9b54e97-bca1-4b6e-98e8-3a85a2b45a0a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436845592916}, "additional": {"logType": "detail", "children": [], "durationId": "e67b63bf-615c-44dc-8cfe-793921770d1e"}}, {"head": {"id": "5583563a-137a-4f96-b10f-28db4d55b902", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436845763125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c81f79af-d0f0-4fcf-83bd-17312d860fc5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436845790750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b15153d-9382-4e90-abf2-871c94dcf1c9", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436846349833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8ac483-df3b-403f-9ffa-7ba1a6e3b155", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436846589166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f935d5d1-5fe9-48cd-b549-86f7f1868828", "name": "entry : default@ProcessProfile cost memory 0.048309326171875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436846618291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d55a91b4-521b-4c02-85ea-0c6bd9925c73", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436846345083, "endTime": 32436846641125}, "additional": {"logType": "info", "children": [], "durationId": "e67b63bf-615c-44dc-8cfe-793921770d1e"}}, {"head": {"id": "fd757faf-45f6-4569-a450-592e37de054a", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847836166, "endTime": 32436847936083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "5ca82ba3-44ca-4a29-b5d2-8ea79bbae1af", "logId": "4aee41ae-fc89-436c-9b0f-345ee2288e2a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ca82ba3-44ca-4a29-b5d2-8ea79bbae1af", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847352166}, "additional": {"logType": "detail", "children": [], "durationId": "fd757faf-45f6-4569-a450-592e37de054a"}}, {"head": {"id": "105e7676-4337-4e22-80f8-219a5d7ffc48", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847518416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65090f17-efd0-4e4b-b10d-2872f3109bcc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847544458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "557386b9-eaf6-4913-aa97-d4a32c4c9e28", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847840500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f70425c7-9495-4e61-8bb7-99c36499d898", "name": "entry : default@BuildNativeWithCmake cost memory 0.013671875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847887583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b1bc856-350d-4fa4-af0d-ed63e8f01140", "name": "runTaskFromQueue task cost before running: 252 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847917916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aee41ae-fc89-436c-9b0f-345ee2288e2a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436847836166, "endTime": 32436847936083, "totalTime": 73583}, "additional": {"logType": "info", "children": [], "durationId": "fd757faf-45f6-4569-a450-592e37de054a"}}, {"head": {"id": "8254c12c-b8d6-43d5-a887-aa7fa96552a1", "name": "upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849729416, "endTime": 32436851378291}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Resources", "taskRunReasons": [], "detailId": "9ae26d63-dc89-4129-9ecd-e987ba9075d3", "logId": "4a5fc559-350e-45d9-a2f4-890cb0b9c202"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ae26d63-dc89-4129-9ecd-e987ba9075d3", "name": "create upcloud:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436848941958}, "additional": {"logType": "detail", "children": [], "durationId": "8254c12c-b8d6-43d5-a887-aa7fa96552a1"}}, {"head": {"id": "2517ccda-300d-480f-b3a5-ad77cc48dfe3", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849183375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4536791e-1bdb-4cb4-80e9-1b15cc57950c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849259041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7492f1d-ddfa-4b90-87c3-a784316fb9cd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849280291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb4fa439-9b6e-4b81-b7b8-b14f561e5c67", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849298833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4e0f689-a0f6-4c18-a686-a47476bd03ce", "name": "Executing task :upcloud:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849735875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ebbb1f4-fd1c-4e07-b55c-3c720f6e2a7d", "name": "Incremental task upcloud:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436851305625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51064e3-81a2-45a8-b87f-ab556934d021", "name": "upcloud : default@ProcessLibs cost memory 0.129638671875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436851352500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a5fc559-350e-45d9-a2f4-890cb0b9c202", "name": "UP-TO-DATE :upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436849729416, "endTime": 32436851378291}, "additional": {"logType": "info", "children": [], "durationId": "8254c12c-b8d6-43d5-a887-aa7fa96552a1"}}, {"head": {"id": "bae36e0a-62ef-4663-9be5-5081f4d66a6f", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436854725000, "endTime": 32436857026041}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4b6cc092-34a2-4304-87dc-9aa2e6cc0f21", "logId": "4ad31a70-4f52-48c7-97b9-dde8747f1c2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b6cc092-34a2-4304-87dc-9aa2e6cc0f21", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436852112416}, "additional": {"logType": "detail", "children": [], "durationId": "bae36e0a-62ef-4663-9be5-5081f4d66a6f"}}, {"head": {"id": "5d101b93-945c-497c-adbd-9eb374183d41", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436852324416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8646f7a4-55e2-4f5f-a89a-cf1f1246752c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436852354500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "993f060e-d52d-4a41-9c6a-8b88a5deefa7", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436853023583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516008a1-9283-4ff3-b636-2f4d2f1839fc", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436855649958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "770ba558-348e-4d65-81ee-c4b6a8c21ee6", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436855916250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9bf567a-2d31-4cc1-953f-6078633b71c5", "name": "entry : default@ProcessResource cost memory 0.108551025390625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436855948833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ad31a70-4f52-48c7-97b9-dde8747f1c2b", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436854725000, "endTime": 32436857026041}, "additional": {"logType": "info", "children": [], "durationId": "bae36e0a-62ef-4663-9be5-5081f4d66a6f"}}, {"head": {"id": "36326381-07f9-40c6-8ce7-d371ad95cb30", "name": "upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860301333, "endTime": 32436860927916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "8a33e199-fcaf-4a27-8b2c-3a7f4ab2987a", "logId": "d82dbc64-5bc9-4a64-8b14-deb1c96eb4a2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a33e199-fcaf-4a27-8b2c-3a7f4ab2987a", "name": "create upcloud:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436858800583}, "additional": {"logType": "detail", "children": [], "durationId": "36326381-07f9-40c6-8ce7-d371ad95cb30"}}, {"head": {"id": "78a1043d-7b72-4449-b8ae-a3a5ee5b6bb5", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436859012875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2033a3-0a61-4a68-ac6f-c2b02055511a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436859052541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61711fdf-90c5-45d0-beb8-9767fb38523e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436859070541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1ba6c1-2962-4423-b24b-52b6f0cfd647", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436859126708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2fdaffda-6c8f-49d6-ad21-68db5ef0ddee", "name": "Executing task :upcloud:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860305083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "333d0600-9569-4c10-aeab-e3121748c3b1", "name": "Task 'upcloud:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860381958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6b94fe1-a289-4d5f-9475-c51cd394fe64", "name": "Incremental task upcloud:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860870041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1956b31a-28f4-4cbf-8711-117b0a20cde8", "name": "upcloud : default@DoNativeStrip cost memory 0.065582275390625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860902666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d82dbc64-5bc9-4a64-8b14-deb1c96eb4a2", "name": "UP-TO-DATE :upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436860301333, "endTime": 32436860927916}, "additional": {"logType": "info", "children": [], "durationId": "36326381-07f9-40c6-8ce7-d371ad95cb30"}}, {"head": {"id": "e367fe6d-d2df-42eb-88e8-72b2fb91d566", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436866967750, "endTime": 32436881872000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b117090f-c843-468d-afe5-190fe04098e7", "logId": "02546da8-4aa6-4abc-810c-f4b2737f321b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b117090f-c843-468d-afe5-190fe04098e7", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436861634166}, "additional": {"logType": "detail", "children": [], "durationId": "e367fe6d-d2df-42eb-88e8-72b2fb91d566"}}, {"head": {"id": "1756d6cd-15fe-4e51-b1f1-e4da03dee643", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436861806291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9417e772-8b9c-470d-8e26-c6587d76b770", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436861835500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08f79eaf-ea49-4ce3-978d-07da1badcf08", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436862235208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36370ebf-9c91-4edd-99e7-9baaa05a9bae", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436867003750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd1115e-a5ff-4785-8386-ba223343bf31", "name": "Incremental task entry:default@CompileResource pre-execution cost: 15 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436881736625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0996461-0a93-4c96-902c-e7936344cab6", "name": "entry : default@CompileResource cost memory 0.860687255859375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436881822041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02546da8-4aa6-4abc-810c-f4b2737f321b", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436866967750, "endTime": 32436881872000}, "additional": {"logType": "info", "children": [], "durationId": "e367fe6d-d2df-42eb-88e8-72b2fb91d566"}}, {"head": {"id": "d5b888e8-7e62-4d76-895a-079667c538ca", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436883249666, "endTime": 32436883680333}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1e9c4d58-56ab-4858-b624-9ee219153449", "logId": "cd018f1d-57e6-4224-a5a3-afaf6e69b2b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e9c4d58-56ab-4858-b624-9ee219153449", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436882798375}, "additional": {"logType": "detail", "children": [], "durationId": "d5b888e8-7e62-4d76-895a-079667c538ca"}}, {"head": {"id": "64dd39cb-4ab9-4375-ae88-12ab380103bb", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436882939750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd86c1cc-12de-4772-8a81-aad809b8122e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436882978625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a03e9219-defd-4fff-b642-3e9091f77c45", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436883252708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab278a6-a01f-4a71-b334-967be906a9c1", "name": "entry : default@BuildNativeWithNinja cost memory 0.02843475341796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436883582583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0b0549-c4fd-44f8-91a0-6a7716de9b82", "name": "runTaskFromQueue task cost before running: 288 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436883639041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd018f1d-57e6-4224-a5a3-afaf6e69b2b8", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436883249666, "endTime": 32436883680333, "totalTime": 378541}, "additional": {"logType": "info", "children": [], "durationId": "d5b888e8-7e62-4d76-895a-079667c538ca"}}, {"head": {"id": "11df9ea1-b559-4419-9fb7-d8c6cea15996", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436886585916, "endTime": 32436930939000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "f1312586-2bad-4aca-95a4-411ecfbed1df", "logId": "4617515c-796b-43cd-9f10-be5254ebd219"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1312586-2bad-4aca-95a4-411ecfbed1df", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436884600791}, "additional": {"logType": "detail", "children": [], "durationId": "11df9ea1-b559-4419-9fb7-d8c6cea15996"}}, {"head": {"id": "ff797a71-4207-4ab7-bbd7-ce3a7d4fe935", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436884787291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93ddb156-2268-4718-811f-cca7235ee17b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436884825791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55b562d-e314-49ce-8ecc-073b7d8622ef", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436886592333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a567852a-e35d-41f0-a57e-454b128aa726", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 42 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436930815958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "114eec1e-8ec9-426b-b10c-b532a6abff11", "name": "entry : default@CompileArkTS cost memory 2.5537185668945312", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436930906166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4617515c-796b-43cd-9f10-be5254ebd219", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436886585916, "endTime": 32436930939000}, "additional": {"logType": "info", "children": [], "durationId": "11df9ea1-b559-4419-9fb7-d8c6cea15996"}}, {"head": {"id": "649e19fb-ff03-4ea3-8e5a-8123c5c64e78", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436941562375, "endTime": 32436942948875}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0e2c28d3-4d35-4a1d-956f-d6a9213fc312", "logId": "de371386-e85e-48e7-afba-2a472086b35f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e2c28d3-4d35-4a1d-956f-d6a9213fc312", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436934622083}, "additional": {"logType": "detail", "children": [], "durationId": "649e19fb-ff03-4ea3-8e5a-8123c5c64e78"}}, {"head": {"id": "ad147f8e-f1b7-4f89-98c2-e43498996e4d", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436934825625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "676d58ad-8392-4cd2-ae40-58c2569c440b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436934852500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdccbc4a-0dae-45fa-b2e1-7044e2a7e649", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436941573416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc3eec8-5ffd-4ebe-8f4f-1906e227334c", "name": "entry : default@BuildJS cost memory 0.11182403564453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436942861750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97d95fd-fd79-4077-b2d7-af20dab4a059", "name": "runTaskFromQueue task cost before running: 347 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436942924916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de371386-e85e-48e7-afba-2a472086b35f", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436941562375, "endTime": 32436942948875, "totalTime": 1349917}, "additional": {"logType": "info", "children": [], "durationId": "649e19fb-ff03-4ea3-8e5a-8123c5c64e78"}}, {"head": {"id": "9c8f34d9-e9f2-47ba-9e5d-23c5d051ef83", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436944068041, "endTime": 32436948016458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "d8c2add2-9d2a-4e8f-81cc-9450ba203553", "logId": "3e73bcd8-207b-401b-a84b-f08364a28cd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8c2add2-9d2a-4e8f-81cc-9450ba203553", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436943628125}, "additional": {"logType": "detail", "children": [], "durationId": "9c8f34d9-e9f2-47ba-9e5d-23c5d051ef83"}}, {"head": {"id": "0819ec22-4bbd-4dfb-9b35-6acc49e60b4e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436943760625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d9973dc-6899-49b4-8d0b-d50aabaffcc3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436943797375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "991f7234-eb5a-4935-ae7d-3a675b38ed9a", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436944072291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be98cd5d-e52c-4b36-baf8-35d64a9a292c", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436947949250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de0732fc-f36b-48f9-9950-710b4f8bc33b", "name": "entry : default@ProcessLibs cost memory 0.5990371704101562", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436947989583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e73bcd8-207b-401b-a84b-f08364a28cd5", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436944068041, "endTime": 32436948016458}, "additional": {"logType": "info", "children": [], "durationId": "9c8f34d9-e9f2-47ba-9e5d-23c5d051ef83"}}, {"head": {"id": "44df3ea6-7bf7-4fcd-985b-6b8a380b6120", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436949950875, "endTime": 32436958209250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "e8b4ac38-88a1-4a29-99c1-2d9ee86d5f8d", "logId": "a41fe474-aafa-4077-80ba-2a323eb4f99e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8b4ac38-88a1-4a29-99c1-2d9ee86d5f8d", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436948707458}, "additional": {"logType": "detail", "children": [], "durationId": "44df3ea6-7bf7-4fcd-985b-6b8a380b6120"}}, {"head": {"id": "47dccacf-fa18-48c2-b9fb-40971556a80c", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436948890208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d1a8fbb-8f87-4722-9315-6d9e6a9352ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436948938791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4538af1-8084-434d-8817-c9b1562be5d4", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436949956375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e568285-c761-4e07-8f2c-8b004fc84fb0", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436950032125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac2473cd-8af1-4cc7-8783-5dd1d78336dc", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436958088041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c155597-dd7c-4f93-b262-f094b04629bf", "name": "entry : default@DoNativeStrip cost memory 0.0604095458984375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436958173208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a41fe474-aafa-4077-80ba-2a323eb4f99e", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436949950875, "endTime": 32436958209250}, "additional": {"logType": "info", "children": [], "durationId": "44df3ea6-7bf7-4fcd-985b-6b8a380b6120"}}, {"head": {"id": "32e79952-a700-47ef-aa42-11e90584ca11", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960030708, "endTime": 32436960555916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "799dcfbe-7470-4b23-a717-945248faf80f", "logId": "ada938a8-2002-41ce-bb21-b1095ea3c93c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "799dcfbe-7470-4b23-a717-945248faf80f", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436958884291}, "additional": {"logType": "detail", "children": [], "durationId": "32e79952-a700-47ef-aa42-11e90584ca11"}}, {"head": {"id": "fe3a4e92-b934-43db-87fa-35f5f8b481b8", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436959060958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "05e36a82-042f-42ff-966f-4d04071c746d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436959085750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d7a6de8-822c-402b-8808-9e632dd8bc74", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960037958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be7c8da7-ac8c-4bfe-91c7-14ebf309bebe", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960104541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d0b833d-0eb2-4136-bf44-97fe136dc107", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960495375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6b44d6-a9e9-48ac-8fb1-5b16cc49c0a3", "name": "entry : default@CacheNativeLibs cost memory 0.0688629150390625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960532625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada938a8-2002-41ce-bb21-b1095ea3c93c", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436960030708, "endTime": 32436960555916}, "additional": {"logType": "info", "children": [], "durationId": "32e79952-a700-47ef-aa42-11e90584ca11"}}, {"head": {"id": "10184f0d-1a97-448c-b42e-768c7c290531", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961846166, "endTime": 32436962143000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "9d75b277-0e2b-46d1-b036-f4985b6d6c34", "logId": "ebd63fa4-158b-430c-9919-672abe10f394"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d75b277-0e2b-46d1-b036-f4985b6d6c34", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961197708}, "additional": {"logType": "detail", "children": [], "durationId": "10184f0d-1a97-448c-b42e-768c7c290531"}}, {"head": {"id": "73aee8dd-7b6a-4081-84c0-f55295529644", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961347875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f68aa8-30a0-486f-a441-e4c54e540459", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961387708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66fea04-22c0-4e43-8b06-95ea7f296d15", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961851166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4a902c7-858c-4762-af1d-37e96fe0ce8a", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961915750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbb3266b-20c1-4dfb-8ab5-0f831444a8b2", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436962085541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7ea1021-c38a-42a0-b00c-10e8e3cb3e68", "name": "entry : default@GeneratePkgModuleJson cost memory 0.06444549560546875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436962116333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebd63fa4-158b-430c-9919-672abe10f394", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436961846166, "endTime": 32436962143000}, "additional": {"logType": "info", "children": [], "durationId": "10184f0d-1a97-448c-b42e-768c7c290531"}}, {"head": {"id": "0db432ea-7948-4c1e-8b7e-d45866ad0a64", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436966168416, "endTime": 32436968806875}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "538b166f-fa06-4927-bfc3-6da9010f202a", "logId": "338e14ed-1f27-40f8-b39f-ef8bfaf5aae6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "538b166f-fa06-4927-bfc3-6da9010f202a", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436962980208}, "additional": {"logType": "detail", "children": [], "durationId": "0db432ea-7948-4c1e-8b7e-d45866ad0a64"}}, {"head": {"id": "9a94e34a-9fd8-4ca6-a61a-7ce975020918", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436963105750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fedfa8e6-887e-4422-b073-3f624d2f787b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436963130875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "915503ee-c3c5-48d3-8b59-3da0824b9906", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436966176500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50fde17c-481a-4b20-bc75-5b450c41d3f9", "name": "Incremental task entry:default@PackageHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436968718000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb98bf8f-880c-4e83-98c6-a710cfa6a119", "name": "entry : default@PackageHap cost memory 0.324188232421875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436968777541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338e14ed-1f27-40f8-b39f-ef8bfaf5aae6", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436966168416, "endTime": 32436968806875}, "additional": {"logType": "info", "children": [], "durationId": "0db432ea-7948-4c1e-8b7e-d45866ad0a64"}}, {"head": {"id": "5336b985-45d2-41cb-a521-e774f12876fd", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971105583, "endTime": 32436971516416}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap' does not exist."], "detailId": "c850eb51-1e27-4df7-881b-b6e2e3c8d737", "logId": "c6857542-f1b8-4b55-99b1-f72dffc8535d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c850eb51-1e27-4df7-881b-b6e2e3c8d737", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436969850583}, "additional": {"logType": "detail", "children": [], "durationId": "5336b985-45d2-41cb-a521-e774f12876fd"}}, {"head": {"id": "9e71ad9d-1bba-4fb5-a1a1-d8459bc107bc", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436969997708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfd2cddd-5d19-4155-bbfc-1ff5d050c39e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436970031416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5426092-253d-46fb-a76f-c2dd86cf3585", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971114791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af3de6b-8349-4e86-9b1b-e5a542eb0ffc", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971231291}, "additional": {"logType": "warn", "children": [], "durationId": "5336b985-45d2-41cb-a521-e774f12876fd"}}, {"head": {"id": "5daf1f68-a670-4c9e-b5c1-0ece9f60334b", "name": "entry:default@SignHap is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971364375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "757f6b57-2ab7-4942-8d91-64e6add3ebef", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971399666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f54e74ea-40d4-40c1-ae80-6da4c8febc13", "name": "entry : default@SignHap cost memory 0.08379364013671875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971466291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3790166-d04b-4634-beef-715115e62caf", "name": "runTaskFromQueue task cost before running: 376 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971496625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6857542-f1b8-4b55-99b1-f72dffc8535d", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436971105583, "endTime": 32436971516416, "totalTime": 384250}, "additional": {"logType": "info", "children": [], "durationId": "5336b985-45d2-41cb-a521-e774f12876fd"}}, {"head": {"id": "8e788ac8-a144-4b41-bdcd-c2c5a943082e", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972353208, "endTime": 32436972488916}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "1294f870-fa39-4dc4-b8be-e2c85a8f539a", "logId": "e2f1c948-2184-4a7c-b7e8-fead01744faa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1294f870-fa39-4dc4-b8be-e2c85a8f539a", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972321041}, "additional": {"logType": "detail", "children": [], "durationId": "8e788ac8-a144-4b41-bdcd-c2c5a943082e"}}, {"head": {"id": "b6bbab95-68f4-4ef6-9556-dc8869a21668", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972357916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cfd18ed-e407-4312-bc14-6c0e38c086aa", "name": "entry : assembleHap cost memory 0.0113525390625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972433791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce702e8-4283-4353-a0ec-fe753f6c192a", "name": "runTaskFromQueue task cost before running: 377 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972467750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f1c948-2184-4a7c-b7e8-fead01744faa", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436972353208, "endTime": 32436972488916, "totalTime": 105667}, "additional": {"logType": "info", "children": [], "durationId": "8e788ac8-a144-4b41-bdcd-c2c5a943082e"}}, {"head": {"id": "a4bc692b-2b13-4344-8e0f-4017e51fbfd8", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973549791, "endTime": 32436973561416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9e4f033d-79d1-48b0-88db-c39c020565c6", "logId": "a0160be0-f6ec-485d-8417-481bea2c6a35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0160be0-f6ec-485d-8417-481bea2c6a35", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973549791, "endTime": 32436973561416}, "additional": {"logType": "info", "children": [], "durationId": "a4bc692b-2b13-4344-8e0f-4017e51fbfd8"}}, {"head": {"id": "55573977-25c6-441a-a6fc-df41dee961ff", "name": "BUILD SUCCESSFUL in 378 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973580583}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "d9848e0a-7dee-49c2-9921-d80320cc1a41", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436596285750, "endTime": 32436973659250}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 32}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.13.1", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "1714cbcf-9d62-4924-9d23-db55fa408d08", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973674041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2263c5a5-5e54-4507-b6cb-da691389f738", "name": "There is no need to refresh cache, since the incremental task upcloud:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973693791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab587b5-fc4f-45fb-a054-e2623b2b797d", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973710833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34cf3a6d-408d-4838-b96f-329529d51a83", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973735291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a13d0bc-cdf8-4525-9f5b-039117ac3344", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436973833041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7885cc20-b26c-4eee-a6cf-e23201028fb0", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974037083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8cecd63-ae14-4e99-b8b0-3e69f4e03f58", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974103041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db463c04-75bb-40d7-b19d-9641843785d1", "name": "There is no need to refresh cache, since the incremental task upcloud:default@CreateHarBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974127541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0420ca5a-aa47-4a8f-aca5-4d87a62cc3cf", "name": "There is no need to refresh cache, since the incremental task upcloud:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974147750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d28704e-6005-4822-8ead-1abff26ede51", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974170291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "550b2e65-97f4-478b-a6c8-937a6578a983", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974218541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1b6d72c-bbb2-4868-a306-6b5cb668616b", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974441875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ab1718e-743d-4794-9f2d-a271e6a8d954", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974497625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46dfc52c-53bd-4041-998c-884b1fb96374", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974516208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb817a01-3702-4f77-b6b8-da01871a608d", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974536333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d026d27e-3a18-4f0e-b349-15116f5101ca", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974553250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c696d17-3d73-48e5-907c-3067a8adad65", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974571208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8bb43e-c044-49cc-a187-9e6286118cab", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974588875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b2d22c0-723f-4003-953e-eacfe3651396", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974604250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a40969f-0689-4d9b-9cc8-de1001d99239", "name": "There is no need to refresh cache, since the incremental task upcloud:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974620208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c865a4e2-6d2b-4d06-b11e-a9a73d0b6dbf", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974636583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45db5e1c-9b26-4fee-810c-226593c828be", "name": "There is no need to refresh cache, since the incremental task upcloud:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974659208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a063b632-dd7e-4738-9f36-408fa518144c", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974676333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73284041-2216-4ed6-9192-636abf47660b", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436974693000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f3afe7-d1a5-488d-bbe1-cb6d6958ccf3", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975235875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a29053e-518d-45e3-bbc5-d2b845626da8", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975441291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b1beb4c-fac4-4da8-839c-7d10b329f9ea", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975485250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155ecf34-ba58-4650-b798-51b25985f762", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975537708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a5d70c2-71c9-49ba-8fe9-29cce74a9f9e", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975571250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43c19a75-fb54-4a2e-8097-336152b08316", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975604125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff71640-b265-4ad9-84b6-597c88bd5653", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975632750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d0dec4-3d77-47e5-a1a1-d32539baf37b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436975788708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3b7c3a-a48c-449e-b275-f6ab7a798502", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976106625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd5ad8c9-e849-4c23-ba28-0887fcc10c34", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976157916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0504eeab-721b-46a5-9c1b-3de9ad5c08a2", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976190666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f927b21-2325-48ee-91f9-09894c0a6a64", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976332625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fdb6ab3-25a1-4079-8da6-4a4c96362135", "name": "Incremental task entry:default@BuildJS post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976408250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18046514-ac15-4a9b-9cc6-e369760277cc", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976428416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75b597c-8452-4f5a-83e9-896cfbeff2ef", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976443541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16abb6ec-6f67-4cbc-85ec-6c705e5bfd8a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976460958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0be1d71-367f-4684-9b37-86024578ffb6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976476041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24babaea-8d63-4967-9406-57e43e31bf76", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976489875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47c5a01-adeb-4317-b87c-9c1d456a6101", "name": "Update task entry:default@SignHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976522875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "717809d2-0601-4b98-898f-bddec1327db1", "name": "Update task entry:default@SignHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976552500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030a4844-8dcb-46d5-acf7-8fb5945ccac0", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436976591208}, "additional": {"logType": "debug", "children": []}}], "workLog": []}