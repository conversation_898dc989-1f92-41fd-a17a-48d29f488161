{"version": "2.0", "ppid": 17236, "events": [{"head": {"id": "e53179b6-cee3-49d1-ad45-83c6ee350b16", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32436983545708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0b15b2a-aef2-4302-9bf1-37781768a597", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32437015493291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2f08f72-4c12-4d1b-a501-553fcab64546", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32437015632416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ad8c2c-d8ac-4a68-bde2-6659d3ec4e02", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449025473250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c795c31-03b9-4916-94d1-be7b98364bc3", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449032825666, "endTime": 32449250519583}, "additional": {"children": ["3e0f5d59-00c3-449e-9a60-8358cf763313", "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "731f4d3e-aa77-4f16-a7f4-959fa2398e56", "b35c1405-f1ff-44d9-b5e4-f9ccec988d11", "e65a5e9c-efb5-4013-8565-82bc29b5243a", "fe9e1e4e-08c2-45f4-a211-7b56651a5f7d", "647c8fbe-9b37-4370-96de-11f4a1a18d02"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e0f5d59-00c3-449e-9a60-8358cf763313", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449032826833, "endTime": 32449045006250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "b4037537-dd23-461e-8449-826234f0dcc8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449045019208, "endTime": 32449249614083}, "additional": {"children": ["4e897409-de40-441f-91f0-b20ecdd8b697", "e7c2e367-4b53-4bdc-96c6-fe12952a6a2d", "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "4a82e078-3ff3-40b3-97aa-4e99830785a3", "4c1f7b24-de2a-46d6-b051-c385e9f9d521", "fe8daabd-3441-4c39-a1fb-04ad954665a1", "f7e6469c-d245-407d-971b-f1e1b2fd70b4", "0ccb42d4-aced-4d5d-aeff-22bd71a4fdc9", "b73bf5e9-b837-4ca1-a4eb-3b8ac8ffc55a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "731f4d3e-aa77-4f16-a7f4-959fa2398e56", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449249636791, "endTime": 32449250513416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "19080ae6-3828-4143-86e1-d48a7863810c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b35c1405-f1ff-44d9-b5e4-f9ccec988d11", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449250516041, "endTime": 32449250516750}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "adab8f86-a086-4247-957d-8c85fe5d3953"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e65a5e9c-efb5-4013-8565-82bc29b5243a", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449034697291, "endTime": 32449034732875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "ebf0a745-cb53-435d-b9c8-c40b13435e88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebf0a745-cb53-435d-b9c8-c40b13435e88", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449034697291, "endTime": 32449034732875}, "additional": {"logType": "info", "children": [], "durationId": "e65a5e9c-efb5-4013-8565-82bc29b5243a", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "fe9e1e4e-08c2-45f4-a211-7b56651a5f7d", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449039359333, "endTime": 32449039374625}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "502012ad-f278-4596-83d1-1c189fe2c081"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "502012ad-f278-4596-83d1-1c189fe2c081", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449039359333, "endTime": 32449039374625}, "additional": {"logType": "info", "children": [], "durationId": "fe9e1e4e-08c2-45f4-a211-7b56651a5f7d", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "0f1d53f9-3406-4a4b-98f8-0895aaf4ac74", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449039416375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "881dff98-befa-473a-81bf-30d14998f352", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449044929875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4037537-dd23-461e-8449-826234f0dcc8", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449032826833, "endTime": 32449045006250}, "additional": {"logType": "info", "children": [], "durationId": "3e0f5d59-00c3-449e-9a60-8358cf763313", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "4e897409-de40-441f-91f0-b20ecdd8b697", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449048104250, "endTime": 32449048115875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "8f2f3a05-5447-48f1-922e-c3e595e04494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7c2e367-4b53-4bdc-96c6-fe12952a6a2d", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449048128541, "endTime": 32449051027791}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "e43d2fe9-a375-4031-823a-a2c6627c370a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449051038750, "endTime": 32449122747208}, "additional": {"children": ["fde3ced9-3520-4e10-ab90-3543b59b8b61", "2f6a1105-61fe-47b1-9503-45f88eb1a830", "97dbd23c-96bf-473c-8aba-5f44b1780aa3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "c09d0254-5cbc-4ff0-adff-fd04c4708733"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a82e078-3ff3-40b3-97aa-4e99830785a3", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122753916, "endTime": 32449163533750}, "additional": {"children": ["ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "c0f6279f-12fd-4000-895c-663a24e9fd33"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "7a846442-b14a-4135-8948-516484eb6900"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c1f7b24-de2a-46d6-b051-c385e9f9d521", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449163539666, "endTime": 32449204991916}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "4a585789-dcd8-41bb-a32c-3e7c61554fb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe8daabd-3441-4c39-a1fb-04ad954665a1", "name": "exec before all nodes", "description": "Execute before all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449046612375, "endTime": 32449249618958}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7e6469c-d245-407d-971b-f1e1b2fd70b4", "name": "exec after all nodes", "description": "Execute after all nodes evaluated.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449046898666, "endTime": 32449249619166}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ccb42d4-aced-4d5d-aeff-22bd71a4fdc9", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449249487083, "endTime": 32449249584791}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "193c1e46-7856-44e6-b001-baefc213d99e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f2f3a05-5447-48f1-922e-c3e595e04494", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449048104250, "endTime": 32449048115875}, "additional": {"logType": "info", "children": [], "durationId": "4e897409-de40-441f-91f0-b20ecdd8b697", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "e43d2fe9-a375-4031-823a-a2c6627c370a", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449048128541, "endTime": 32449051027791}, "additional": {"logType": "info", "children": [], "durationId": "e7c2e367-4b53-4bdc-96c6-fe12952a6a2d", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "fde3ced9-3520-4e10-ab90-3543b59b8b61", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449051545916, "endTime": 32449051564708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "logId": "b19b0899-e26a-4540-99c4-0dea1d55ea23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b19b0899-e26a-4540-99c4-0dea1d55ea23", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449051545916, "endTime": 32449051564708}, "additional": {"logType": "info", "children": [], "durationId": "fde3ced9-3520-4e10-ab90-3543b59b8b61", "parent": "c09d0254-5cbc-4ff0-adff-fd04c4708733"}}, {"head": {"id": "2f6a1105-61fe-47b1-9503-45f88eb1a830", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449053243541, "endTime": 32449122435166}, "additional": {"children": ["da617857-245a-474f-877e-70a8887c1b3f", "a41cbe73-0870-4d25-8b4f-f0ef1b017416"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "logId": "6b6056f2-8a56-4fb6-91f6-0a080651dbea"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da617857-245a-474f-877e-70a8887c1b3f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449053244000, "endTime": 32449064256125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f6a1105-61fe-47b1-9503-45f88eb1a830", "logId": "8d64b52f-bd76-4133-8457-bd746d115ea3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a41cbe73-0870-4d25-8b4f-f0ef1b017416", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449064268125, "endTime": 32449122429125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2f6a1105-61fe-47b1-9503-45f88eb1a830", "logId": "740b1444-aaba-46eb-a926-2d4d6b1643ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14ca988b-ebf0-4d2d-ab13-02363fd788c7", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449053248166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f88337a-28ee-461d-a578-7d5d82dd0f60", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449064182458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d64b52f-bd76-4133-8457-bd746d115ea3", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449053244000, "endTime": 32449064256125}, "additional": {"logType": "info", "children": [], "durationId": "da617857-245a-474f-877e-70a8887c1b3f", "parent": "6b6056f2-8a56-4fb6-91f6-0a080651dbea"}}, {"head": {"id": "5ca90b2b-99e5-48b1-9c59-d3fb075d9e89", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449064274375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa24baf-07db-46ba-b0e0-a8c25860805b", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449076748375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0529cd1f-403a-4cc1-9de3-60ee4e648bd0", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449076811625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b192e68-c7a7-4246-b949-b971604c308b", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449076853250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00155a1b-71af-4bd5-afb3-0bf155864f79", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449077521416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af630d2c-7882-489f-acbf-914f2935bc9b", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449078520250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa4cb923-c66a-4f7f-a911-bd3b8fbe3386", "name": "not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449081293375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d20214b-90fc-423f-93df-29d65a7c2f6b", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449082967958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "272d3d28-d760-4dec-bdf7-fa57e9189508", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449089711500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47892937-9f68-463e-abf0-b87d646f12b7", "name": "Sdk init in 22 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449106000708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b672e98-3a5e-4f50-9fed-cf95e1ef7de8", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449106085375}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 32}, "markType": "other"}}, {"head": {"id": "0b2a6563-2ccd-4b2d-9afb-e3586ecb7abf", "name": "caseSensitive<PERSON><PERSON>ck<PERSON>ff", "description": "caseSensitive<PERSON><PERSON><PERSON> check is off", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449106111750}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 32}, "markType": "other"}}, {"head": {"id": "7ad16e1e-cb9b-4909-a9f9-af0a9b0e3104", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122299458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db4ea287-c77e-4681-a8fb-69a43ce79aea", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122366625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2e486d-0655-4ba7-9e01-83544bfea8b1", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122390125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3376d232-7e92-4a3b-b298-5f9c74141a8d", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122409041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "740b1444-aaba-46eb-a926-2d4d6b1643ef", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449064268125, "endTime": 32449122429125}, "additional": {"logType": "info", "children": [], "durationId": "a41cbe73-0870-4d25-8b4f-f0ef1b017416", "parent": "6b6056f2-8a56-4fb6-91f6-0a080651dbea"}}, {"head": {"id": "6b6056f2-8a56-4fb6-91f6-0a080651dbea", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449053243541, "endTime": 32449122435166}, "additional": {"logType": "info", "children": ["8d64b52f-bd76-4133-8457-bd746d115ea3", "740b1444-aaba-46eb-a926-2d4d6b1643ef"], "durationId": "2f6a1105-61fe-47b1-9503-45f88eb1a830", "parent": "c09d0254-5cbc-4ff0-adff-fd04c4708733"}}, {"head": {"id": "97dbd23c-96bf-473c-8aba-5f44b1780aa3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122731125, "endTime": 32449122741000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "logId": "be008c45-0014-481d-b605-a6395eff3cab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be008c45-0014-481d-b605-a6395eff3cab", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122731125, "endTime": 32449122741000}, "additional": {"logType": "info", "children": [], "durationId": "97dbd23c-96bf-473c-8aba-5f44b1780aa3", "parent": "c09d0254-5cbc-4ff0-adff-fd04c4708733"}}, {"head": {"id": "c09d0254-5cbc-4ff0-adff-fd04c4708733", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449051038750, "endTime": 32449122747208}, "additional": {"logType": "info", "children": ["b19b0899-e26a-4540-99c4-0dea1d55ea23", "6b6056f2-8a56-4fb6-91f6-0a080651dbea", "be008c45-0014-481d-b605-a6395eff3cab"], "durationId": "60fb5ad4-4ff0-4bc0-bd35-0ade12005ccb", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449123016041, "endTime": 32449135750125}, "additional": {"children": ["14ff0988-0a4e-4e9d-91c2-22868891702d", "280e1b8f-fa7e-4f04-acad-1dd4ff71ed5b", "7b8a3243-b13d-43fb-b7d4-498618ac1566"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a82e078-3ff3-40b3-97aa-4e99830785a3", "logId": "921a7cc2-21dc-4316-90ca-8163dcb26e32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "14ff0988-0a4e-4e9d-91c2-22868891702d", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449124527458, "endTime": 32449124534250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "logId": "2ad28970-bc1e-417a-906c-2bc0d5b36604"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ad28970-bc1e-417a-906c-2bc0d5b36604", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449124527458, "endTime": 32449124534250}, "additional": {"logType": "info", "children": [], "durationId": "14ff0988-0a4e-4e9d-91c2-22868891702d", "parent": "921a7cc2-21dc-4316-90ca-8163dcb26e32"}}, {"head": {"id": "280e1b8f-fa7e-4f04-acad-1dd4ff71ed5b", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449125413541, "endTime": 32449134798250}, "additional": {"children": ["d940afc0-d8a2-4703-8360-51f4cee7a472", "79421158-f7e4-4f3b-90aa-db309ea9aaa4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "logId": "c853785e-83f5-480e-b84d-e4bb1cd113f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d940afc0-d8a2-4703-8360-51f4cee7a472", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449125414125, "endTime": 32449128495041}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "280e1b8f-fa7e-4f04-acad-1dd4ff71ed5b", "logId": "c124e18d-2a4f-42f4-9716-e8d8517b26f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79421158-f7e4-4f3b-90aa-db309ea9aaa4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449128502625, "endTime": 32449134790125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "280e1b8f-fa7e-4f04-acad-1dd4ff71ed5b", "logId": "715ec7b0-5d20-40a0-95ff-d8825bb5b50b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50e5b141-0119-4eea-9ce1-45e98dc054b1", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449125415666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e115e06-6f3d-4d74-8e18-b0920ddb1f2f", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449128435416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c124e18d-2a4f-42f4-9716-e8d8517b26f0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449125414125, "endTime": 32449128495041}, "additional": {"logType": "info", "children": [], "durationId": "d940afc0-d8a2-4703-8360-51f4cee7a472", "parent": "c853785e-83f5-480e-b84d-e4bb1cd113f5"}}, {"head": {"id": "17ad7d03-b893-4487-95da-6363e3ead3f1", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449128506583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a150227c-9275-44a9-9162-5fc328a490ce", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131775500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b094378-9a32-44ef-896b-550fcc51e8d7", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131835791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f46db38d-7922-4409-9fd3-1b47546c7e70", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131913875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91ffbbdc-e6b1-4b5f-aeba-a6215e5cb7bc", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131953375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06606efa-3f65-47ff-aa98-023f16159956", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131972583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c10db32b-cda9-42d7-81e2-5d656256c52b", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449131989041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0843c13c-23f5-4ad2-9226-6c66b19ce508", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449132068166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "488ad013-54e6-4590-bcc6-d0af3624564f", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449134643125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "533ab8e4-aba0-48a2-81b2-6dfff7ac71f9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449134726166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c335535-92ce-4260-aa7f-0d7e4ecf418a", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449134748000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "269ab405-eecc-426d-b987-8facf57c7375", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449134771708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "715ec7b0-5d20-40a0-95ff-d8825bb5b50b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449128502625, "endTime": 32449134790125}, "additional": {"logType": "info", "children": [], "durationId": "79421158-f7e4-4f3b-90aa-db309ea9aaa4", "parent": "c853785e-83f5-480e-b84d-e4bb1cd113f5"}}, {"head": {"id": "c853785e-83f5-480e-b84d-e4bb1cd113f5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449125413541, "endTime": 32449134798250}, "additional": {"logType": "info", "children": ["c124e18d-2a4f-42f4-9716-e8d8517b26f0", "715ec7b0-5d20-40a0-95ff-d8825bb5b50b"], "durationId": "280e1b8f-fa7e-4f04-acad-1dd4ff71ed5b", "parent": "921a7cc2-21dc-4316-90ca-8163dcb26e32"}}, {"head": {"id": "7b8a3243-b13d-43fb-b7d4-498618ac1566", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449135725666, "endTime": 32449135738583}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "logId": "3d62d59f-7d2f-4be4-aa22-9ce021542494"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d62d59f-7d2f-4be4-aa22-9ce021542494", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449135725666, "endTime": 32449135738583}, "additional": {"logType": "info", "children": [], "durationId": "7b8a3243-b13d-43fb-b7d4-498618ac1566", "parent": "921a7cc2-21dc-4316-90ca-8163dcb26e32"}}, {"head": {"id": "921a7cc2-21dc-4316-90ca-8163dcb26e32", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449123016041, "endTime": 32449135750125}, "additional": {"logType": "info", "children": ["2ad28970-bc1e-417a-906c-2bc0d5b36604", "c853785e-83f5-480e-b84d-e4bb1cd113f5", "3d62d59f-7d2f-4be4-aa22-9ce021542494"], "durationId": "ec98f8bd-6901-460f-befb-ccbda8b3bc8a", "parent": "7a846442-b14a-4135-8948-516484eb6900"}}, {"head": {"id": "c0f6279f-12fd-4000-895c-663a24e9fd33", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449136880416, "endTime": 32449163527125}, "additional": {"children": ["8274ad88-9925-454f-9a45-d298d648074c", "28f0f918-f2bd-41c7-bd66-aa41d2168247", "a457eb75-c063-454b-89af-898723ceaa3a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4a82e078-3ff3-40b3-97aa-4e99830785a3", "logId": "e82cffd6-6d91-4a3a-8a1d-495d7765bc72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8274ad88-9925-454f-9a45-d298d648074c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449144594958, "endTime": 32449144607250}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f6279f-12fd-4000-895c-663a24e9fd33", "logId": "a6d36e88-3e4b-4d66-ae59-bb7c8c2cf4c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6d36e88-3e4b-4d66-ae59-bb7c8c2cf4c4", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449144594958, "endTime": 32449144607250}, "additional": {"logType": "info", "children": [], "durationId": "8274ad88-9925-454f-9a45-d298d648074c", "parent": "e82cffd6-6d91-4a3a-8a1d-495d7765bc72"}}, {"head": {"id": "28f0f918-f2bd-41c7-bd66-aa41d2168247", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449146052875, "endTime": 32449162503666}, "additional": {"children": ["0a6469c9-d453-4f59-8882-b40906a4e5b7", "ebf0f413-36f7-4a88-b870-f359b08bba3a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f6279f-12fd-4000-895c-663a24e9fd33", "logId": "bef5bc39-2459-4ddd-ba5c-7761a616a0ef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a6469c9-d453-4f59-8882-b40906a4e5b7", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449146053625, "endTime": 32449148532708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28f0f918-f2bd-41c7-bd66-aa41d2168247", "logId": "a957885a-7799-46bf-896b-bdbade44f007"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ebf0f413-36f7-4a88-b870-f359b08bba3a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449148541583, "endTime": 32449162498583}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "28f0f918-f2bd-41c7-bd66-aa41d2168247", "logId": "3e293ecd-4b57-43db-9856-ffe868c3d165"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed0022e5-af86-44bf-8d84-c9e3745eb91b", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449146056166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3934d35b-573c-4ee9-82c0-a55765b566ea", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449148476083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a957885a-7799-46bf-896b-bdbade44f007", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449146053625, "endTime": 32449148532708}, "additional": {"logType": "info", "children": [], "durationId": "0a6469c9-d453-4f59-8882-b40906a4e5b7", "parent": "bef5bc39-2459-4ddd-ba5c-7761a616a0ef"}}, {"head": {"id": "d282433f-edb6-421e-a285-506f7bb56717", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449148545583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a484a3a8-dbeb-4564-a4a0-e92049db5c5a", "name": "Start initialize module-target build option map, moduleName=upcloud, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449159881333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2adb4f-a1e3-4b1a-969a-ee623b274df4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449159960625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d89d6e7-e117-4af8-a73e-5c6098c381a0", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449160049833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a6b77b9-8ae1-4e6d-9f73-fe4790093a5d", "name": "Module 'upcloud' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449160249583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c983320-6067-44f7-9e8d-c0762af899d8", "name": "Module 'upcloud' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449160279833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "145ba1fb-70a2-4306-959f-c8c418c2d87d", "name": "End initialize module-target build option map, moduleName=upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449160298791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8733540-9474-4c64-8662-5ad825cb88e7", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449160329791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d9216b-0110-4f41-ba8e-faa8361c4528", "name": "Module upcloud task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449162383958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63a2acb4-cb8a-432d-ae7a-6c853b200978", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449162439708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb505847-0003-40bb-9938-44c298502b7c", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449162461791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4daf346d-f24f-48f7-a8cc-c63efa7285d7", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449162480166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e293ecd-4b57-43db-9856-ffe868c3d165", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449148541583, "endTime": 32449162498583}, "additional": {"logType": "info", "children": [], "durationId": "ebf0f413-36f7-4a88-b870-f359b08bba3a", "parent": "bef5bc39-2459-4ddd-ba5c-7761a616a0ef"}}, {"head": {"id": "bef5bc39-2459-4ddd-ba5c-7761a616a0ef", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449146052875, "endTime": 32449162503666}, "additional": {"logType": "info", "children": ["a957885a-7799-46bf-896b-bdbade44f007", "3e293ecd-4b57-43db-9856-ffe868c3d165"], "durationId": "28f0f918-f2bd-41c7-bd66-aa41d2168247", "parent": "e82cffd6-6d91-4a3a-8a1d-495d7765bc72"}}, {"head": {"id": "a457eb75-c063-454b-89af-898723ceaa3a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449163511000, "endTime": 32449163519625}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c0f6279f-12fd-4000-895c-663a24e9fd33", "logId": "7be35fb5-d778-4d46-9a48-43702da35fdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7be35fb5-d778-4d46-9a48-43702da35fdb", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449163511000, "endTime": 32449163519625}, "additional": {"logType": "info", "children": [], "durationId": "a457eb75-c063-454b-89af-898723ceaa3a", "parent": "e82cffd6-6d91-4a3a-8a1d-495d7765bc72"}}, {"head": {"id": "e82cffd6-6d91-4a3a-8a1d-495d7765bc72", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449136880416, "endTime": 32449163527125}, "additional": {"logType": "info", "children": ["a6d36e88-3e4b-4d66-ae59-bb7c8c2cf4c4", "bef5bc39-2459-4ddd-ba5c-7761a616a0ef", "7be35fb5-d778-4d46-9a48-43702da35fdb"], "durationId": "c0f6279f-12fd-4000-895c-663a24e9fd33", "parent": "7a846442-b14a-4135-8948-516484eb6900"}}, {"head": {"id": "7a846442-b14a-4135-8948-516484eb6900", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449122753916, "endTime": 32449163533750}, "additional": {"logType": "info", "children": ["921a7cc2-21dc-4316-90ca-8163dcb26e32", "e82cffd6-6d91-4a3a-8a1d-495d7765bc72"], "durationId": "4a82e078-3ff3-40b3-97aa-4e99830785a3", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "403df6ff-a71e-4cac-91e4-61459e96522b", "name": "watch files: [\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1846 more items\n]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449177265041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c8de3b-f633-4b87-b494-249284699179", "name": "hvigorfile, resolve hvigorfile dependencies in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449204898916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a585789-dcd8-41bb-a32c-3e7c61554fb3", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449163539666, "endTime": 32449204991916}, "additional": {"logType": "info", "children": [], "durationId": "4c1f7b24-de2a-46d6-b051-c385e9f9d521", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "b73bf5e9-b837-4ca1-a4eb-3b8ac8ffc55a", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449205670208, "endTime": 32449205809875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "logId": "bd9fc65f-8588-4fca-934e-7f6d941b35f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f959bf09-86ff-43e2-8554-c711c697a78a", "name": "project has submodules:entry,upcloud", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449205688458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de0a26f7-cf4f-41b2-a521-873861433c42", "name": "module:upcloud no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449205786791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9fc65f-8588-4fca-934e-7f6d941b35f9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449205670208, "endTime": 32449205809875}, "additional": {"logType": "info", "children": [], "durationId": "b73bf5e9-b837-4ca1-a4eb-3b8ac8ffc55a", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "9227835c-1a30-4da8-875b-32634d77cdc1", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449210409000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e074507-e239-4a43-b9ff-7c9d5c2815bc", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449219575125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04d4ff99-9e4f-4d1d-8f0a-7e58f08ba0a5", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449234896458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0b663fdd-f39a-4dee-b8c6-2b41e00e4353", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449235216625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e4edc81f-39e3-42b8-a30f-c5b1421610e1", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449235462125}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1682f3e0-7fc0-4541-b12e-e63477b1d7e4", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449235700791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5751ee8d-bf27-4efd-9b1c-29385fbb2d08", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449235942916}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0ce1d825-67cf-4254-96cc-d0bc43a420f1", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449236189625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "302136d1-6080-4c9b-8ddd-345f9fdd6457", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449236448875}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b05fa213-df31-45f9-9d11-fdf9f0e48dc7", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449236698250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "82312806-b5ed-4313-974b-8a8481dbd209", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449236947416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "92a509f4-96a0-4251-becd-8f08111e9942", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449237193083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a7adc457-de6e-4727-9dfe-4d21898b31d4", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449237433041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cf4047eb-b8e1-4b8b-990b-43859c8c616f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449237670625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a32b8644-5137-4800-a9e3-84b191dbf1c5", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449237907416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8bdfae79-0d8f-418e-8f1b-6093a1fdf234", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449238160750}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b0c35874-c200-400e-85d3-633f45035d04", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449238403625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "96898276-649a-4698-a2bd-5089bd4f21b4", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449239141125}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c3b09207-e9d6-40a4-8b08-fee619e61a26", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449239378666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "17099320-7e25-4fa9-84bb-4bc686600196", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449239624291}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "828e2e12-1809-4356-98b4-a1aaaba1a219", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449239872958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "612c7e82-ac9d-4ed7-896b-bb0eea6dbb15", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449240111250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "dabdc3c0-749c-4712-83df-15caf2cffbce", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449240352125}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "272ca7df-5633-4835-8e49-71db3bc08f5f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449240599375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c89be767-4a44-45de-a84e-8ef40639dc79", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449240843333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "62667f72-bcc2-45fa-a3e7-cc0eb64cd058", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449241087333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8907fecc-75b6-4ec3-bc6d-2f0d6883b980", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449241333791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "820c8fa5-34b1-4d57-a6e3-d973a4509ccf", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449241578791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c43827c3-5c9e-4e58-a676-56f44ee165ae", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449241963666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cd07164d-83f1-4663-8944-0dad50c9525c", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449242304250}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "87335e4a-eac9-4c2a-b522-fcc335cf0ee9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449242563166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8d571b28-f5bb-4c97-b796-457508ed9492", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449242852333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b61b052e-6f3c-4940-b11e-96c9f46cd1c6", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449243166833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b118766f-f3a6-4114-aa85-c5a2e422c68e", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449243499041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "9751d74e-26d6-4d7e-9cf8-a4d8e4cbf9e5", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449243758041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0a3a7cda-15b6-48c9-a929-050b252ddd71", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449243990333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "3b82159c-66de-4929-828a-d6d4cf0beb46", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449244229458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0b419ea1-4833-42f0-8c33-354bb3429664", "name": "Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449244288208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199cda06-7f82-4769-aa2e-33b8c71627d5", "name": "Module UpCloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449244313166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1200c4-6913-4e4f-a862-1c9dfaa68d5b", "name": "Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449245171083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "951fada3-0cce-4aad-9cd5-bc825c9b1179", "name": "Module entry's total dependency: 9", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449245201500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d0ce294-a608-4b76-84a7-fe80d44973a0", "name": "Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449247741666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8f3d53-dc8c-464a-a662-77f3db51a655", "name": "Module upcloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449247778750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af4e47b9-9d55-4401-976a-c607e23904fc", "name": "Configuration phase cost:202 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449249507375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "193c1e46-7856-44e6-b001-baefc213d99e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449249487083, "endTime": 32449249584791}, "additional": {"logType": "info", "children": [], "durationId": "0ccb42d4-aced-4d5d-aeff-22bd71a4fdc9", "parent": "1e4b7b06-c120-4fc9-915f-53cce8fbf951"}}, {"head": {"id": "1e4b7b06-c120-4fc9-915f-53cce8fbf951", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449045019208, "endTime": 32449249614083}, "additional": {"logType": "info", "children": ["8f2f3a05-5447-48f1-922e-c3e595e04494", "e43d2fe9-a375-4031-823a-a2c6627c370a", "c09d0254-5cbc-4ff0-adff-fd04c4708733", "7a846442-b14a-4135-8948-516484eb6900", "4a585789-dcd8-41bb-a32c-3e7c61554fb3", "193c1e46-7856-44e6-b001-baefc213d99e", "bd9fc65f-8588-4fca-934e-7f6d941b35f9"], "durationId": "97e48b7f-a6a5-4e04-b2c3-08d144583e35", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "647c8fbe-9b37-4370-96de-11f4a1a18d02", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449250491875, "endTime": 32449250504583}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5c795c31-03b9-4916-94d1-be7b98364bc3", "logId": "a0d7e969-df81-4289-bcff-6080e73ecfe5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0d7e969-df81-4289-bcff-6080e73ecfe5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449250491875, "endTime": 32449250504583}, "additional": {"logType": "info", "children": [], "durationId": "647c8fbe-9b37-4370-96de-11f4a1a18d02", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "19080ae6-3828-4143-86e1-d48a7863810c", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449249636791, "endTime": 32449250513416}, "additional": {"logType": "info", "children": [], "durationId": "731f4d3e-aa77-4f16-a7f4-959fa2398e56", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "adab8f86-a086-4247-957d-8c85fe5d3953", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449250516041, "endTime": 32449250516750}, "additional": {"logType": "info", "children": [], "durationId": "b35c1405-f1ff-44d9-b5e4-f9ccec988d11", "parent": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752"}}, {"head": {"id": "9e0d2eb2-0fce-401a-b5b2-1df347bb7752", "name": "init", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449032825666, "endTime": 32449250519583}, "additional": {"logType": "info", "children": ["b4037537-dd23-461e-8449-826234f0dcc8", "1e4b7b06-c120-4fc9-915f-53cce8fbf951", "19080ae6-3828-4143-86e1-d48a7863810c", "adab8f86-a086-4247-957d-8c85fe5d3953", "ebf0a745-cb53-435d-b9c8-c40b13435e88", "502012ad-f278-4596-83d1-1c189fe2c081", "a0d7e969-df81-4289-bcff-6080e73ecfe5"], "durationId": "5c795c31-03b9-4916-94d1-be7b98364bc3"}}, {"head": {"id": "a454e6c4-a129-4660-8a5f-1b4215417ee3", "name": "Configuration task cost before running: 221 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449250700125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b634ff16-5812-4fa4-9f63-c9155bcd4b99", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449258116708, "endTime": 32449262944166}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "4c5b9f92-0e6a-4dd1-903f-7f15ad3bff75", "logId": "d9818ed4-4c8c-437b-bb04-842d3703811d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c5b9f92-0e6a-4dd1-903f-7f15ad3bff75", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449253967208}, "additional": {"logType": "detail", "children": [], "durationId": "b634ff16-5812-4fa4-9f63-c9155bcd4b99"}}, {"head": {"id": "8a69a181-d9d1-4291-a30e-e7be973d5439", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449255800041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07fb8a8d-f434-4a23-853c-1da72c16abc7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449255865041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eff5a699-e3fc-4d02-8f8e-54fa219fa81f", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449258188208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07acdad6-82bf-4e4f-bd0d-a60d4a772144", "name": "Incremental task entry:default@PreBuild pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449262828666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "929a74d0-4318-44b3-95ab-0ae0d402244c", "name": "entry : default@PreBuild cost memory 0.22442626953125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449262908166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9818ed4-4c8c-437b-bb04-842d3703811d", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449258116708, "endTime": 32449262944166}, "additional": {"logType": "info", "children": [], "durationId": "b634ff16-5812-4fa4-9f63-c9155bcd4b99"}}, {"head": {"id": "1bcb8163-bedb-46b0-a662-7b253c2c9be2", "name": "upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449273214458, "endTime": 32449276009000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Verification", "taskRunReasons": [], "detailId": "41373985-0022-4f41-9a15-a4c427c17953", "logId": "a675614d-416f-4f12-bb03-1f8597a364e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41373985-0022-4f41-9a15-a4c427c17953", "name": "create upcloud:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449266944000}, "additional": {"logType": "detail", "children": [], "durationId": "1bcb8163-bedb-46b0-a662-7b253c2c9be2"}}, {"head": {"id": "2df3fb24-5976-4034-8b02-2ce13cdf26e2", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449267738750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a906ac85-790e-4c44-968c-cc06ab8f6b7d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449267867541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61952c63-c2c2-4916-bbbd-0b9a1516c43c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449268025166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ee5d10c-0e0b-4894-ac25-65daf6e7f8be", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449268052625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a261eba-7eae-4286-93f1-329f556c5fbd", "name": "Executing task :upcloud:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449273220666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958febfe-6af7-44bd-a663-a8f1ff65ab5d", "name": "Incremental task upcloud:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449275854625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e763a793-403f-40e4-80f5-6784e3d4d5f6", "name": "upcloud : default@PreBuild cost memory 0.171295166015625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449275910541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a675614d-416f-4f12-bb03-1f8597a364e1", "name": "UP-TO-DATE :upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449273214458, "endTime": 32449276009000}, "additional": {"logType": "info", "children": [], "durationId": "1bcb8163-bedb-46b0-a662-7b253c2c9be2"}}, {"head": {"id": "6e4772a3-4c82-4b6b-9279-b70b9ce63cdd", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449286330416, "endTime": 32449291394208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "01e438b3-f553-4545-a379-4c3ed9806f47", "logId": "1321c086-bb6b-4a05-b5de-5759dfb097c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01e438b3-f553-4545-a379-4c3ed9806f47", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449279054958}, "additional": {"logType": "detail", "children": [], "durationId": "6e4772a3-4c82-4b6b-9279-b70b9ce63cdd"}}, {"head": {"id": "e5292227-6100-462b-a21c-8432738a8f83", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449279913625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0aab03b2-a51f-490b-a8f0-5f1823dc8ff1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449280007750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67af5a14-1af2-450c-868b-8f8b67e2386b", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449286350625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88110115-af7f-4bcd-b95a-23b128edaad1", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 4 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449290916750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cb9be13-c3c4-40fc-b453-ada94f09a466", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449291308166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db0eef16-24d3-46fe-8052-99caea4f2ebd", "name": "entry : default@GenerateMetadata cost memory 0.082244873046875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449291362541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1321c086-bb6b-4a05-b5de-5759dfb097c5", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449286330416, "endTime": 32449291394208}, "additional": {"logType": "info", "children": [], "durationId": "6e4772a3-4c82-4b6b-9279-b70b9ce63cdd"}}, {"head": {"id": "0b5156e8-9ad8-4377-bf55-6f8292f3879a", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449293155375, "endTime": 32449293373083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "294d1cf4-72d4-4389-8544-a8ad20aa4f83", "logId": "45bad8e7-0b04-4d9b-b189-88466048a24d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "294d1cf4-72d4-4389-8544-a8ad20aa4f83", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449292278708}, "additional": {"logType": "detail", "children": [], "durationId": "0b5156e8-9ad8-4377-bf55-6f8292f3879a"}}, {"head": {"id": "f2b3d26e-f6f9-40b5-a1e0-1907d3421081", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449292540125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f49ce607-2112-4b7f-8ae6-d3f37103d8f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449292571333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12bb17f-3f72-4128-bedd-87fec338e961", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449293160833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a83225-5a9c-4fc7-a732-a9e8f3d88e6b", "name": "entry : default@PreCheckSyscap cost memory 0.12989044189453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449293300041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8237b6-2368-409d-baf6-80d3d82a8316", "name": "runTaskFromQueue task cost before running: 263 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449293352208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45bad8e7-0b04-4d9b-b189-88466048a24d", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449293155375, "endTime": 32449293373083, "totalTime": 171125}, "additional": {"logType": "info", "children": [], "durationId": "0b5156e8-9ad8-4377-bf55-6f8292f3879a"}}, {"head": {"id": "52abce71-b082-43a6-9a78-48d8a03e7810", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449300985750, "endTime": 32449301373916}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "58b8f541-79c9-42c0-a59e-233f172cfc85", "logId": "4ce239aa-10b9-4dc1-be69-d85f7fd72fc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "58b8f541-79c9-42c0-a59e-233f172cfc85", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449295655333}, "additional": {"logType": "detail", "children": [], "durationId": "52abce71-b082-43a6-9a78-48d8a03e7810"}}, {"head": {"id": "1b8cebf3-9bf4-475c-a06b-3d9b9382b6ba", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449295871375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706287dc-2a1e-4942-952c-9406628f5375", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449295905083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8b28c2f-f36d-4330-aa3e-6025b1abb6e1", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449300998208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a456112c-f673-4af3-b656-f7e56e808ad9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449301167583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "524c5f35-0941-43a6-a2aa-5735c1a6ded6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.05536651611328125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449301317208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "855dfc33-8b51-4916-9290-3730f122176c", "name": "runTaskFromQueue task cost before running: 271 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449301352458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce239aa-10b9-4dc1-be69-d85f7fd72fc2", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449300985750, "endTime": 32449301373916, "totalTime": 361500}, "additional": {"logType": "info", "children": [], "durationId": "52abce71-b082-43a6-9a78-48d8a03e7810"}}, {"head": {"id": "592b0df9-0df4-44ae-a636-f7750776e313", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306219416, "endTime": 32449306991083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "13a8f961-035f-40cc-842c-11fa7e6279e9", "logId": "292aafc4-4171-4859-bdc0-c6b9b8e579ba"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13a8f961-035f-40cc-842c-11fa7e6279e9", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449302373791}, "additional": {"logType": "detail", "children": [], "durationId": "592b0df9-0df4-44ae-a636-f7750776e313"}}, {"head": {"id": "9e68ecc6-ca53-4a8c-8483-7368a7923f6f", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449303335250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee5d8e1-b9a4-4f36-b188-3674311317a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449303406916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b66a5f80-c2f6-4a49-b750-05a8a8ebd075", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306226541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee82247-540e-4bca-9943-dc1cecfd8f79", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306859291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37639810-1bf8-4805-96c0-27e63728b9dc", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306899208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dca5b0a9-315e-4c54-aded-f772c3c14db9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.083038330078125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306935708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c30596-4a53-4f9b-863a-d437177aa9d8", "name": "runTaskFromQueue task cost before running: 277 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306970500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "292aafc4-4171-4859-bdc0-c6b9b8e579ba", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449306219416, "endTime": 32449306991083, "totalTime": 746500}, "additional": {"logType": "info", "children": [], "durationId": "592b0df9-0df4-44ae-a636-f7750776e313"}}, {"head": {"id": "7f62924a-535f-4676-ad1b-797f9a91992a", "name": "upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310864750, "endTime": 32449311288833}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Generate", "taskRunReasons": [], "detailId": "44b7e887-27f2-482a-ba0c-ef4944713a9b", "logId": "b358dc63-84aa-4c13-8fe0-508d02cba701"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44b7e887-27f2-482a-ba0c-ef4944713a9b", "name": "create upcloud:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449309560125}, "additional": {"logType": "detail", "children": [], "durationId": "7f62924a-535f-4676-ad1b-797f9a91992a"}}, {"head": {"id": "135e3c36-3ad8-4629-8534-b913d82ad4a3", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310307791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "806ceaf3-4340-4905-a2bf-33864eb57e95", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310349333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51810c00-24e7-4a02-a9a6-a0b6276ddd73", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310365625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bbfa347-f99e-4b88-b1e3-40625a1f6b4c", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310383000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a9784ad-9343-4645-8f53-912aabd127ee", "name": "Executing task :upcloud:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310870541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7923acb1-2207-465b-97c7-0f191173e15b", "name": "Task 'upcloud:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449311007333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47c1e200-4a43-4018-9b71-1401d4899c45", "name": "Incremental task upcloud:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449311233916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afac042-50dc-4fa2-aea2-b8211abaae1e", "name": "upcloud : default@CreateHarBuildProfile cost memory 0.07659912109375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449311263208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b358dc63-84aa-4c13-8fe0-508d02cba701", "name": "UP-TO-DATE :upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449310864750, "endTime": 32449311288833}, "additional": {"logType": "info", "children": [], "durationId": "7f62924a-535f-4676-ad1b-797f9a91992a"}}, {"head": {"id": "c624ac4d-aafb-40f7-a244-2767e0875305", "name": "upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314776125, "endTime": 32449314885708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "88c8f9c8-1a17-4b8d-9f74-d64f8e9afda6", "logId": "133831a9-9ef5-4d6a-9e3c-238d007a673b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88c8f9c8-1a17-4b8d-9f74-d64f8e9afda6", "name": "create upcloud:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449313851708}, "additional": {"logType": "detail", "children": [], "durationId": "c624ac4d-aafb-40f7-a244-2767e0875305"}}, {"head": {"id": "9151c1eb-adf4-4b77-ba52-b21365d4f858", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314159791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f590a6-ecc1-4067-99a7-15097907fdd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314201375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0ac4d9-5f41-4587-ae59-93fa1b3961fc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314216291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a394a2d5-a5c6-44bf-b8e2-44ae09da1369", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314232000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23553982-3791-4178-8a20-e94c59b5536d", "name": "Executing task :upcloud:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314780583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5add52d-528d-444d-91cc-e3565a8eceaf", "name": "upcloud : default@ConfigureCmake cost memory 0.014190673828125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314833416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c29ae8b4-0609-4ffc-965a-5e0598872f97", "name": "runTaskFromQueue task cost before running: 285 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314866833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "133831a9-9ef5-4d6a-9e3c-238d007a673b", "name": "Finished :upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449314776125, "endTime": 32449314885708, "totalTime": 80750}, "additional": {"logType": "info", "children": [], "durationId": "c624ac4d-aafb-40f7-a244-2767e0875305"}}, {"head": {"id": "fbf99921-9ed0-44ca-b4ee-92f50e2decd5", "name": "upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449323568708, "endTime": 32449324191541}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Config", "taskRunReasons": [], "detailId": "7072b2d5-c46c-4ab7-abb2-de73d9297c47", "logId": "9aa90d9a-899a-473d-ad3b-cc39c02bd22e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7072b2d5-c46c-4ab7-abb2-de73d9297c47", "name": "create upcloud:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449316066958}, "additional": {"logType": "detail", "children": [], "durationId": "fbf99921-9ed0-44ca-b4ee-92f50e2decd5"}}, {"head": {"id": "30a51374-77b8-47d1-90c7-41449f46e5ca", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449316355333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1caeb48-8d95-42e0-8a61-3a0625cb79b8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449316414666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16cda278-64fe-481d-8a85-014f2eb28cee", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449316431708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "349fc907-60d1-4876-85c8-938b94807825", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449316447916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8909922-bb47-4b2f-a0c6-4ef62cabddca", "name": "Executing task :upcloud:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449323592500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70c95a27-7136-4fd1-bd5d-c55cc9a24c1f", "name": "Incremental task upcloud:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449324126208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87d7fbdd-1157-4523-8d3d-f8bca7b63ad4", "name": "upcloud : default@MergeProfile cost memory 0.09975433349609375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449324167458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa90d9a-899a-473d-ad3b-cc39c02bd22e", "name": "UP-TO-DATE :upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449323568708, "endTime": 32449324191541}, "additional": {"logType": "info", "children": [], "durationId": "fbf99921-9ed0-44ca-b4ee-92f50e2decd5"}}, {"head": {"id": "0494266f-efdd-4529-a8c8-91b96f0d19ac", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449326687208, "endTime": 32449328200083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "922c43da-bdcb-42ea-aeb5-c0d96778ecc0", "logId": "663fe93f-5b70-4237-a3d7-15cfd3f03635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "922c43da-bdcb-42ea-aeb5-c0d96778ecc0", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449325522041}, "additional": {"logType": "detail", "children": [], "durationId": "0494266f-efdd-4529-a8c8-91b96f0d19ac"}}, {"head": {"id": "28eb5304-e663-4420-9136-ebd92897b5c9", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449325852375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa026816-191f-4511-b4a8-434541714361", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449325940541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe39891b-10d8-4578-85f5-8aeafdce17a9", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449326691583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db70bc5f-2e1c-4241-8b36-4ed9690e9432", "name": "File: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449326734125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6353cee-2378-4f40-891c-08626926478f", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449326921125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a28c4faa-8288-46c5-add2-ab2923a2c187", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449328074791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9241123-1bb6-4193-9bc5-b4d04decc6e5", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449328113333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69c80f7b-dd35-406f-bbc7-0c589f5a8e1d", "name": "entry : default@SyscapTransform cost memory 0.10123443603515625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449328151708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60336ee8-2617-4aba-a6ba-4b7da32f9600", "name": "runTaskFromQueue task cost before running: 298 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449328180500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663fe93f-5b70-4237-a3d7-15cfd3f03635", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449326687208, "endTime": 32449328200083, "totalTime": 1487625}, "additional": {"logType": "info", "children": [], "durationId": "0494266f-efdd-4529-a8c8-91b96f0d19ac"}}, {"head": {"id": "b627adf1-c923-4d84-ab2f-57afa2c09474", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449330501541, "endTime": 32449332735458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "22aa0303-4420-453a-a731-9ecdb0780615", "logId": "3c9dc130-8326-4044-aa32-877509351c11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22aa0303-4420-453a-a731-9ecdb0780615", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449329588541}, "additional": {"logType": "detail", "children": [], "durationId": "b627adf1-c923-4d84-ab2f-57afa2c09474"}}, {"head": {"id": "13651fe3-1bca-4319-a4b0-e1ca6cc31aff", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449329769875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211d8a73-8eaa-426c-a678-dede873240b8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449329804708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51b1789-c6d9-4656-9441-8aaeadef7586", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449330506083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce6777fd-8b3d-452a-94e2-9c899ca53c50", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449332635666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca97e725-b9b4-45ad-b177-c886ddb4c0d3", "name": "entry : default@ProcessRouterMap cost memory 0.183929443359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449332687541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c9dc130-8326-4044-aa32-877509351c11", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449330501541, "endTime": 32449332735458}, "additional": {"logType": "info", "children": [], "durationId": "b627adf1-c923-4d84-ab2f-57afa2c09474"}}, {"head": {"id": "8ac9855c-932a-4346-bca4-9f2398c33996", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341021875, "endTime": 32449341656000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "e77299bf-ffc1-479b-aca5-f084832a5347", "logId": "474f3734-2b9d-443b-8a61-db144ef7ac69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e77299bf-ffc1-479b-aca5-f084832a5347", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449335699500}, "additional": {"logType": "detail", "children": [], "durationId": "8ac9855c-932a-4346-bca4-9f2398c33996"}}, {"head": {"id": "2bd4c4fb-bea0-4368-ba76-6e8ce80160e6", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449340239208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b8a3d4c-8078-43bf-b229-eab899bd6a50", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449340309250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1b9d43f-18f4-4f60-b28b-31cfc3f1cd28", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341029250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd4e15c-6d6f-4859-b262-1693692dc201", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341370250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d267f40-1a65-4fe9-a165-15e4a7c96fdc", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341603458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a3fd41a-9f19-41dd-bdf3-8e4225874313", "name": "entry : default@CreateBuildProfile cost memory 0.090789794921875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341631208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "474f3734-2b9d-443b-8a61-db144ef7ac69", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449341021875, "endTime": 32449341656000}, "additional": {"logType": "info", "children": [], "durationId": "8ac9855c-932a-4346-bca4-9f2398c33996"}}, {"head": {"id": "d765cb65-605f-408f-83d3-81a15e19902f", "name": "upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343032791, "endTime": 32449343127958}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "edb4b871-2305-40f1-9191-3506314d1cd4", "logId": "defb06d0-0ec4-4e3e-8691-a92f049bfe19"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "edb4b871-2305-40f1-9191-3506314d1cd4", "name": "create upcloud:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449342445041}, "additional": {"logType": "detail", "children": [], "durationId": "d765cb65-605f-408f-83d3-81a15e19902f"}}, {"head": {"id": "99e6e8d7-4f04-477c-a300-1d2961cb50cc", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449342694708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2aa848b-ba44-4363-9ff5-ec1c65249f57", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449342724583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abb3985f-61ba-4359-9a10-20e8c160d84a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449342740708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b583ce1-f4f2-4f7b-bd19-7c56701996b2", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449342758541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd9d273a-9747-4657-95c2-2255d0f82e01", "name": "Executing task :upcloud:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343036208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4acf2ca7-92af-42aa-b743-6cb2dea0d5ee", "name": "upcloud : default@BuildNativeWithCmake cost memory 0.01374053955078125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343079166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3cf3973-43c7-4607-a73e-c2ca5a770793", "name": "runTaskFromQueue task cost before running: 313 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343110208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "defb06d0-0ec4-4e3e-8691-a92f049bfe19", "name": "Finished :upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343032791, "endTime": 32449343127958, "totalTime": 68208}, "additional": {"logType": "info", "children": [], "durationId": "d765cb65-605f-408f-83d3-81a15e19902f"}}, {"head": {"id": "ade275f5-ff47-4961-a29c-8b765a3fca79", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449345558083, "endTime": 32449346133958}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "bdeaf6b9-a362-4484-bd8c-68f31404b8da", "logId": "9020ee5a-c40f-44fb-8ef0-9a0f55f5b368"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdeaf6b9-a362-4484-bd8c-68f31404b8da", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449343790000}, "additional": {"logType": "detail", "children": [], "durationId": "ade275f5-ff47-4961-a29c-8b765a3fca79"}}, {"head": {"id": "90e487fc-707d-4a7b-8d74-9b72546c4bc7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449344007291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "818c42a0-3a79-4c5c-b863-483f2081cfc8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449344038291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5be0da0b-7074-4f66-b7b3-cc825a1d6a6e", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449345563416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3899ee6b-090c-4b8a-996d-670a1716d4d0", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449346082458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "494cf898-f8b1-4a94-a164-7659ddd3253b", "name": "entry : default@MergeProfile cost memory 0.11222076416015625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449346110833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9020ee5a-c40f-44fb-8ef0-9a0f55f5b368", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449345558083, "endTime": 32449346133958}, "additional": {"logType": "info", "children": [], "durationId": "ade275f5-ff47-4961-a29c-8b765a3fca79"}}, {"head": {"id": "d21ba288-bbe0-4177-8b3a-237eb2d604ec", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449351922875, "endTime": 32449358015291}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "13ab1493-c179-46c9-a21e-68b09bc1b855", "logId": "fc7c4653-3a94-4282-a5ba-4c40912860ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13ab1493-c179-46c9-a21e-68b09bc1b855", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449346863000}, "additional": {"logType": "detail", "children": [], "durationId": "d21ba288-bbe0-4177-8b3a-237eb2d604ec"}}, {"head": {"id": "a7f4514b-e90f-4958-ac62-9f29be42b14e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449347037958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5e46703-dec3-4379-864d-b4a3ad6cd7f6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449347060916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd57444b-ca52-4991-8ce6-6c0d58027a60", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449347362458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c28ad27-f7e4-4e6a-b18d-35cea7748e80", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449351938166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce8b1b1-1f3b-49b8-9557-abe610b07f5f", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449352922583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03dc903f-c922-48f5-a6fa-48228e3fa767", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449354096250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60bcd515-6128-46a3-92ba-70512af9cbe1", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449355217166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79a1ed38-1a24-4dcf-b2b9-f5526907d612", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449355940291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f030603-633d-4183-8ca3-d2d2941d8a83", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449357065541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c282354-bf55-48bc-a694-8f97603f4181", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449357941625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e7dd767-33e4-4312-a8f2-8745e13ced6c", "name": "entry : default@GenerateLoaderJson cost memory 0.8094253540039062", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449357989375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7c4653-3a94-4282-a5ba-4c40912860ca", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449351922875, "endTime": 32449358015291}, "additional": {"logType": "info", "children": [], "durationId": "d21ba288-bbe0-4177-8b3a-237eb2d604ec"}}, {"head": {"id": "b44638cc-504d-4693-b09a-8a6f4ec2a934", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449358612833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b49c3400-0c58-4e83-90bf-3fbc23567138", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449359694500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7907f5da-793c-42ee-a4fb-e7bda60fd05d", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361653500, "endTime": 32449361787791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "35161fb2-81d0-41dc-966b-9863a3795c25", "logId": "41d1b27b-33f2-43b9-8118-04ee2ca084b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35161fb2-81d0-41dc-966b-9863a3795c25", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449360936750}, "additional": {"logType": "detail", "children": [], "durationId": "7907f5da-793c-42ee-a4fb-e7bda60fd05d"}}, {"head": {"id": "96775ae4-8a44-4efd-b123-7e005eb75805", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361067250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3805ea56-8df9-40a9-bb65-38a108989529", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361096583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b183a8e-ba29-4d70-b3e6-49a50ab6e4a5", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361661666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ada0fbc-0ab8-4760-82c0-42ef8260ab63", "name": "entry : default@ConfigureCmake cost memory 0.0125885009765625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361733375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "919c5f14-362f-4e1c-b2aa-81cf0436b75e", "name": "runTaskFromQueue task cost before running: 332 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361768291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d1b27b-33f2-43b9-8118-04ee2ca084b6", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449361653500, "endTime": 32449361787791, "totalTime": 104875}, "additional": {"logType": "info", "children": [], "durationId": "7907f5da-793c-42ee-a4fb-e7bda60fd05d"}}, {"head": {"id": "7ef94dfd-4f59-452a-8e5e-9654fef43d3f", "name": "upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449363202875, "endTime": 32449363664708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "2e180465-452f-47fe-bb8f-ddf575473639", "logId": "09e448ce-ef36-4c94-87cd-641134861679"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e180465-452f-47fe-bb8f-ddf575473639", "name": "create upcloud:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449362522958}, "additional": {"logType": "detail", "children": [], "durationId": "7ef94dfd-4f59-452a-8e5e-9654fef43d3f"}}, {"head": {"id": "473bf5fd-9828-4c39-a907-59a213b6f95f", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449362739333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "247fac7d-4ef6-4f64-94f0-582166a14d96", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449362789291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14c927e0-e622-4913-ade4-a2a3d642a260", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449362808791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0b8647-886e-4658-be9f-b5c053a75796", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449362843208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3a9a9cf-fbdb-4bbb-ba4a-043f459e6a8c", "name": "Executing task :upcloud:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449363208833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0c33e10-c503-48ea-b20b-dafbf5be97b5", "name": "upcloud : default@BuildNativeWithNinja cost memory 0.028564453125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449363592375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73ca3edf-5f98-457d-adc9-b4278dd285ae", "name": "runTaskFromQueue task cost before running: 334 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449363641541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09e448ce-ef36-4c94-87cd-641134861679", "name": "Finished :upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449363202875, "endTime": 32449363664708, "totalTime": 426833}, "additional": {"logType": "info", "children": [], "durationId": "7ef94dfd-4f59-452a-8e5e-9654fef43d3f"}}, {"head": {"id": "0e2d4b14-e6a3-4304-b6af-3efab3d08e1b", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364685125, "endTime": 32449365777000}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f5804f70-9f0f-43d6-8e05-96404b1805ad", "logId": "e9ffddd3-eb2c-4394-a5ef-f3e37f2f2fda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5804f70-9f0f-43d6-8e05-96404b1805ad", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364269541}, "additional": {"logType": "detail", "children": [], "durationId": "0e2d4b14-e6a3-4304-b6af-3efab3d08e1b"}}, {"head": {"id": "0162f4a1-7862-40fc-be75-ebbb15f97365", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364404958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0002fbd3-c1b7-43d0-98ad-3175fd488a93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364438291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9d0bf25-9662-4c5e-8deb-c30bb1a35fba", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364688875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb54adc6-e9ab-4456-8576-85c267517df2", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449365708375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2d8b8f6-072d-4101-82c9-946802bafe89", "name": "entry : default@MakePackInfo cost memory 0.12659454345703125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449365752291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9ffddd3-eb2c-4394-a5ef-f3e37f2f2fda", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449364685125, "endTime": 32449365777000}, "additional": {"logType": "info", "children": [], "durationId": "0e2d4b14-e6a3-4304-b6af-3efab3d08e1b"}}, {"head": {"id": "0886c7e3-8d95-4bfb-8196-d3f1fc7f1c88", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449368997666, "endTime": 32449369340333}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "af2aea43-9c6b-4b91-bd00-145481fa8031", "logId": "aaefd1ce-0317-4c31-bf76-6e02bf40a687"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af2aea43-9c6b-4b91-bd00-145481fa8031", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449368029250}, "additional": {"logType": "detail", "children": [], "durationId": "0886c7e3-8d95-4bfb-8196-d3f1fc7f1c88"}}, {"head": {"id": "f8e53c5c-d9be-4a44-895c-9ba8c7e6e7ce", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449368445041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39e432a8-e2f8-41c1-9665-0e39ef64c886", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449368497208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be04705c-e7bf-4b6f-b451-9a8e9e6886de", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449369002750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff5eabf-e84c-4bfe-9ac0-017cd38f24a6", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449369273958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "882d1ee0-b344-4dc4-81a6-1fc23d37a01c", "name": "entry : default@ProcessProfile cost memory 0.05027008056640625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449369313583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aaefd1ce-0317-4c31-bf76-6e02bf40a687", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449368997666, "endTime": 32449369340333}, "additional": {"logType": "info", "children": [], "durationId": "0886c7e3-8d95-4bfb-8196-d3f1fc7f1c88"}}, {"head": {"id": "49cc501e-567e-4596-a4d3-be9d7cc12a9b", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370679541, "endTime": 32449370822083}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8219aa52-8393-44f8-a6ae-b3e41e802729", "logId": "40fb3b68-142c-4f88-a01c-f0907f7981b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8219aa52-8393-44f8-a6ae-b3e41e802729", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370025083}, "additional": {"logType": "detail", "children": [], "durationId": "49cc501e-567e-4596-a4d3-be9d7cc12a9b"}}, {"head": {"id": "8dab52dd-e0ee-4f4f-a82e-f1889439ac84", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370183625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa8cfcbd-4818-4c4f-8e16-6c5dcc5dc07f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370216208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b2b633-07f4-4091-81cc-8d19471d1f0b", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370687583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91e61a6-a04e-4764-836e-9bb1923fdd5c", "name": "entry : default@BuildNativeWithCmake cost memory 0.01706695556640625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370762291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38802c75-8434-4deb-baf9-685e067e9126", "name": "runTaskFromQueue task cost before running: 341 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370799958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40fb3b68-142c-4f88-a01c-f0907f7981b5", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449370679541, "endTime": 32449370822083, "totalTime": 109541}, "additional": {"logType": "info", "children": [], "durationId": "49cc501e-567e-4596-a4d3-be9d7cc12a9b"}}, {"head": {"id": "af0bb03c-d50e-4d18-9be2-a625320ba14e", "name": "upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449374621791, "endTime": 32449376263708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Resources", "taskRunReasons": [], "detailId": "8e931c9c-53c9-4251-964e-e219b85cc1be", "logId": "149055ed-b3fe-41d8-8c59-a3ba20975139"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e931c9c-53c9-4251-964e-e219b85cc1be", "name": "create upcloud:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449371450083}, "additional": {"logType": "detail", "children": [], "durationId": "af0bb03c-d50e-4d18-9be2-a625320ba14e"}}, {"head": {"id": "9abba831-b9c7-4819-a25f-06665c78c342", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449371640666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da59915b-3874-425f-8716-d837b2d2f662", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449371682958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5472a00-4f06-4b76-a25b-ea842a4beaf4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449373895416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cac9ee68-b01b-4801-bd9d-1b46ff346211", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449373943666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a357ff-e5fa-40b1-82c9-4d9f8f500571", "name": "Executing task :upcloud:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449374628916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda30973-ff58-47f7-b0ef-0ffc658657a0", "name": "Incremental task upcloud:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449376105583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1af2175-2501-40cb-9d3f-84c9ced67605", "name": "upcloud : default@ProcessLibs cost memory 0.1060333251953125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449376233625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "149055ed-b3fe-41d8-8c59-a3ba20975139", "name": "UP-TO-DATE :upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449374621791, "endTime": 32449376263708}, "additional": {"logType": "info", "children": [], "durationId": "af0bb03c-d50e-4d18-9be2-a625320ba14e"}}, {"head": {"id": "a913df1e-87b6-4709-a0da-87d98eb7e539", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449379300500, "endTime": 32449381079125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "86748606-6fe8-4a99-bcde-32697ea4c20d", "logId": "1e4936f0-0d54-4322-b40f-b9731e104d71"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "86748606-6fe8-4a99-bcde-32697ea4c20d", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449376955000}, "additional": {"logType": "detail", "children": [], "durationId": "a913df1e-87b6-4709-a0da-87d98eb7e539"}}, {"head": {"id": "c65a8ab1-613a-41cb-9ef0-bba1e362c0aa", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449377162541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "045f6fa4-eb9e-4c2f-bdfd-093be7719f43", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449377199666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f68cc2f2-74bb-4169-9127-a6875dd44139", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449377762416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53f6f966-e53d-48a3-acbc-5cafb683418b", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449380229208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78c5685-a9ff-4ee9-aa21-29b236a76a91", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449380570250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a857c7-5c15-4fb5-82e6-5c9ba591296e", "name": "entry : default@ProcessResource cost memory 0.1070404052734375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449380603625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e4936f0-0d54-4322-b40f-b9731e104d71", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449379300500, "endTime": 32449381079125}, "additional": {"logType": "info", "children": [], "durationId": "a913df1e-87b6-4709-a0da-87d98eb7e539"}}, {"head": {"id": "c5dd4738-4920-4e0f-af6c-a016bb754116", "name": "upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449384492041, "endTime": 32449385160375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "b0a1818b-9764-47e8-b831-c046a2f1d32f", "logId": "3ee28b66-fa15-4350-a21d-1e00f31f2efe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b0a1818b-9764-47e8-b831-c046a2f1d32f", "name": "create upcloud:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449382417833}, "additional": {"logType": "detail", "children": [], "durationId": "c5dd4738-4920-4e0f-af6c-a016bb754116"}}, {"head": {"id": "f0ba919b-443b-4635-b9ac-1bf084f01031", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449382610291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9111df4-760b-476d-8833-0cfc94255e1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449382648041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d8d80c2-3185-4347-b618-e3b1defec497", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449382663833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2d91d14-41a4-4780-b2be-35d8309f9805", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449382679875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7632438-405f-4187-ac18-c8af2de69c69", "name": "Executing task :upcloud:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449384503041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20705e56-2d9f-434d-903d-d4a606fcb238", "name": "Task 'upcloud:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449384616208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90585367-666b-438a-aa1a-f85a47402c2f", "name": "Incremental task upcloud:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449385075541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c474888f-08c0-4cb1-b70b-780ccd6170c9", "name": "upcloud : default@DoNativeStrip cost memory 0.0666351318359375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449385131250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee28b66-fa15-4350-a21d-1e00f31f2efe", "name": "UP-TO-DATE :upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449384492041, "endTime": 32449385160375}, "additional": {"logType": "info", "children": [], "durationId": "c5dd4738-4920-4e0f-af6c-a016bb754116"}}, {"head": {"id": "02c710e8-21b5-4c31-9113-321e1024231a", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449388421375, "endTime": 32449408217458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "8e748210-7d93-42f7-8a56-a2140343c1ba", "logId": "d07f56b0-36b8-466c-99bf-a6741bfa26b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8e748210-7d93-42f7-8a56-a2140343c1ba", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449385771166}, "additional": {"logType": "detail", "children": [], "durationId": "02c710e8-21b5-4c31-9113-321e1024231a"}}, {"head": {"id": "e7e7a8cf-6085-4a80-a28d-87130ea5ce89", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449386004500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98ce21a3-3ec8-47a1-96f1-251cf1e87286", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449386037291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cad949c-919e-4418-9a82-3d191cb3b6a8", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449386327541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c503058e-e029-49dd-9250-0ebdd0de6603", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449388451875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f2e43d-c8e1-4b08-89bc-0e5126910cd7", "name": "Incremental task entry:default@CompileResource pre-execution cost: 19 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449408062208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83034af2-14f2-4d61-b8ac-b5433902de96", "name": "entry : default@CompileResource cost memory 0.8441390991210938", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449408160541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d07f56b0-36b8-466c-99bf-a6741bfa26b7", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449388421375, "endTime": 32449408217458}, "additional": {"logType": "info", "children": [], "durationId": "02c710e8-21b5-4c31-9113-321e1024231a"}}, {"head": {"id": "8829ae86-4570-46d4-932e-16843739ddf3", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449410039875, "endTime": 32449410467666}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "35dc56c4-7054-4efc-bc61-8c302b91d626", "logId": "bd17e9cf-fa71-4d2b-858d-11219b03e061"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35dc56c4-7054-4efc-bc61-8c302b91d626", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449409485875}, "additional": {"logType": "detail", "children": [], "durationId": "8829ae86-4570-46d4-932e-16843739ddf3"}}, {"head": {"id": "495429f6-8447-4cc9-a178-378a32c1b290", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449409656500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3afd6f7e-9d17-46f5-ab9f-eefe8794cb4f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449409695125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29012f52-7b5e-4a49-98bf-713fdffb2835", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449410045500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8267abaf-774e-483c-b13f-e5e1bd8d46c8", "name": "entry : default@BuildNativeWithNinja cost memory 0.0295562744140625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449410403125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92a4dc02-866b-406a-b9ba-a5417bb80cc2", "name": "runTaskFromQueue task cost before running: 380 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449410444750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd17e9cf-fa71-4d2b-858d-11219b03e061", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449410039875, "endTime": 32449410467666, "totalTime": 392667}, "additional": {"logType": "info", "children": [], "durationId": "8829ae86-4570-46d4-932e-16843739ddf3"}}, {"head": {"id": "aec3a654-7b08-4372-8208-09b3aa860d21", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449413132291, "endTime": 32451362967833}, "additional": {"children": ["70b4940b-3d09-49e6-a132-f2845102c0ac", "047008ec-445e-4eaf-884c-885c7306e868"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets' has been changed."], "detailId": "a1c4f4af-5633-4064-b531-9c9e0c4604cc", "logId": "eb97f25f-336e-4cdd-b8ee-1c5c1834bbae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a1c4f4af-5633-4064-b531-9c9e0c4604cc", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449411043166}, "additional": {"logType": "detail", "children": [], "durationId": "aec3a654-7b08-4372-8208-09b3aa860d21"}}, {"head": {"id": "4bbca085-0850-49e6-b6f9-97ed77374ca0", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449411183208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0f0c60-ab8b-4953-b780-024518af9bb2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449411211958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c461b123-61d4-46eb-8d04-a80500fe215b", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449413140875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c60ace1-8579-4e35-863f-8e98fa5d4a34", "name": "entry:default@CompileArkTS is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449418244833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0196f72a-02aa-404e-a2ad-cf193090af72", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449418338708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08b174d0-72b0-4227-b385-bbf5cd417ceb", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449424524333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b614ba94-cc75-47d0-b224-ac837ba3e3ea", "name": "Compile arkts with external api path: /Applications/DevEco-Studio.app/Contents/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449424926125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207150ef-2d30-42f1-b98a-5d953e1ea2ec", "name": "default@CompileArkTS work[58] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449425414666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70b4940b-3d09-49e6-a132-f2845102c0ac", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449631438916, "endTime": 32451358806458}, "additional": {"children": ["668587a0-f9b9-4b4b-8c0f-22d9ba69e0f6", "376e2674-9258-4d3b-9406-f85424f0fd4b", "d3c8304a-bb37-4402-beff-84389210281d", "2bfe363a-a644-464f-b2f6-6d1f01e176f1", "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "b4a22b09-3a7d-4ee8-b6e3-cd6a889aefbc"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aec3a654-7b08-4372-8208-09b3aa860d21", "logId": "b6ef012b-729d-426b-96ac-1c7482456d9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbc3f907-71cb-4e0b-b0a5-c8ae04a58640", "name": "default@CompileArkTS work[58] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449425967166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca4970c7-270d-49e2-8c1f-f5a7a1a67332", "name": "default@CompileArkTS work[58] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426115125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc15cb1a-3530-440c-8be7-ec8e191020e4", "name": "CopyResources startTime: 32449426149125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426150583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60aa95fd-25b7-48fa-91f5-1a46888bcdb0", "name": "default@CompileArkTS work[59] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426177500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "047008ec-445e-4eaf-884c-885c7306e868", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32450063900083, "endTime": 32450068641791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aec3a654-7b08-4372-8208-09b3aa860d21", "logId": "f2093cf6-f009-4853-8974-9d30b479bf3f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "12eded3b-1082-49f9-986d-c68de79db340", "name": "default@CompileArkTS work[59] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426583541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd16558a-10fa-46e0-b1b0-95c9f12d76f0", "name": "default@CompileArkTS work[59] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426607833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "304372ce-7f6d-4fa2-853f-742395220c50", "name": "entry : default@CompileArkTS cost memory 1.28515625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449426677625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c77c6c-26e2-4db9-9aca-a396b2316226", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449429773083, "endTime": 32449430697583}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "03857fb9-c1d5-4020-bd00-cbbb97685cbf", "logId": "896fb073-36f4-4a36-8e1e-992e1ae1891d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "03857fb9-c1d5-4020-bd00-cbbb97685cbf", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449427480166}, "additional": {"logType": "detail", "children": [], "durationId": "b3c77c6c-26e2-4db9-9aca-a396b2316226"}}, {"head": {"id": "f6aed655-f7a2-4c2c-8d30-db1764106570", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449427672333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f473fdaa-3871-4d74-8a8f-26e0922e160d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449427723000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7de2ce02-1d6e-4670-9daf-f94f620f0f29", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449429779666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "710fff3a-2516-4494-8e0a-be82ecfd44fd", "name": "entry : default@BuildJS cost memory 0.11226654052734375", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449430631750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c38c2a6-9169-4006-9fc3-32d9b3467675", "name": "runTaskFromQueue task cost before running: 401 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449430674916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "896fb073-36f4-4a36-8e1e-992e1ae1891d", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449429773083, "endTime": 32449430697583, "totalTime": 890625}, "additional": {"logType": "info", "children": [], "durationId": "b3c77c6c-26e2-4db9-9aca-a396b2316226"}}, {"head": {"id": "92429854-2253-4b4a-b5d3-25d55be7c34b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431575083, "endTime": 32449438812416}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "dbb436b0-b17b-49a6-b115-69b97ed4770d", "logId": "8713edd2-d6b0-4e88-a907-5478133ae9b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbb436b0-b17b-49a6-b115-69b97ed4770d", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431213041}, "additional": {"logType": "detail", "children": [], "durationId": "92429854-2253-4b4a-b5d3-25d55be7c34b"}}, {"head": {"id": "0e0c08c5-ddf1-410a-9e16-85403910e032", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431315875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b20601-7c60-446c-b51e-446e5e9a785b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431346083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c240a44-be82-4745-828e-732b78ed7e36", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431576625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77388c80-3602-4155-83ac-a7f207c7b876", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449438705083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "feb2fc4c-7558-4fa1-9d85-c57fb2a7656f", "name": "entry : default@ProcessLibs cost memory 0.534820556640625", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449438780500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8713edd2-d6b0-4e88-a907-5478133ae9b1", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449431575083, "endTime": 32449438812416}, "additional": {"logType": "info", "children": [], "durationId": "92429854-2253-4b4a-b5d3-25d55be7c34b"}}, {"head": {"id": "84e22d0d-7e4a-4c15-a1b6-a894effd71c1", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449440677416, "endTime": 32449441113250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8b99b40e-a6e8-40f8-8a87-3061b2f1fed8", "logId": "30f395e9-bfab-4a20-91d4-c85d4ca61a29"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b99b40e-a6e8-40f8-8a87-3061b2f1fed8", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449439469166}, "additional": {"logType": "detail", "children": [], "durationId": "84e22d0d-7e4a-4c15-a1b6-a894effd71c1"}}, {"head": {"id": "f6a60d0f-1cc1-44f2-b245-99baf9281f93", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449439610791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3eb38cd3-4d0b-4c1b-94bd-1b15ed24c33f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449439646083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12531eba-6672-404a-a818-3e7d3f0be2c0", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449440680958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a2fdbb6-489c-4ec3-841c-2fdd1594cf36", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449440747291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "134856d8-d00e-49fc-801e-5eb17218d7a8", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449441055083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de43a172-c636-498f-bf6b-98d354b43684", "name": "entry : default@DoNativeStrip cost memory 0.06285858154296875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449441089041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30f395e9-bfab-4a20-91d4-c85d4ca61a29", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449440677416, "endTime": 32449441113250}, "additional": {"logType": "info", "children": [], "durationId": "84e22d0d-7e4a-4c15-a1b6-a894effd71c1"}}, {"head": {"id": "51bb77ad-c347-4024-9454-ae6aca535892", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449442801625, "endTime": 32449443185375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "8a4ffa95-8e19-4a23-8bce-aa46fa40f2ac", "logId": "3a6b4385-6683-41f0-95a4-cb73ed9c2cfe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a4ffa95-8e19-4a23-8bce-aa46fa40f2ac", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449441604750}, "additional": {"logType": "detail", "children": [], "durationId": "51bb77ad-c347-4024-9454-ae6aca535892"}}, {"head": {"id": "1c33f1c1-58ea-4d60-bbe5-04da6d1f9b26", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449441796375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4325850c-494f-4cae-8048-214a8752115b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449441858458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3828ec0c-860e-4795-8b99-33d28bee60af", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449442805708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4fb688e8-d1db-447b-bb46-c95a7828d74c", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449442860250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f75eb6f-b2a4-4eb4-b50f-728feb141e3d", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449443133666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50825c77-b8cd-446d-bd79-8f175c5358d1", "name": "entry : default@CacheNativeLibs cost memory 0.0691680908203125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449443159791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6b4385-6683-41f0-95a4-cb73ed9c2cfe", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449442801625, "endTime": 32449443185375}, "additional": {"logType": "info", "children": [], "durationId": "51bb77ad-c347-4024-9454-ae6aca535892"}}, {"head": {"id": "1482870c-7719-427d-bfe2-bc119ea28478", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449627029500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dffa8aff-9273-4112-ac04-110d8558fb5e", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449627238833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6840219d-a7f8-484e-b918-3a03b469108f", "name": "default@CompileArkTS work[59] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449628540250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3da57f9-1b35-4e33-8c95-97c21bdf5800", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449628907000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd561183-b03a-49f7-a505-124f1de029fa", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449628940375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5201bf2-2f93-4a29-9b6e-bff0dfb20c8f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449628961750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5279bfed-729d-4376-93e7-843196ae1797", "name": "default@CompileArkTS work[58] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449629152041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f70a60-198d-4f20-8d9d-45e7a074e445", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32450068785625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "173c4315-3039-4ef9-bbf1-b43bf74c56a1", "name": "CopyResources is end, endTime: 32450068912875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32450068920750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2238eb62-e4c0-478e-96c5-3337b98c1bfd", "name": "default@CompileArkTS work[59] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32450069057583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2093cf6-f009-4853-8974-9d30b479bf3f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32450063900083, "endTime": 32450068641791}, "additional": {"logType": "info", "children": [], "durationId": "047008ec-445e-4eaf-884c-885c7306e868", "parent": "eb97f25f-336e-4cdd-b8ee-1c5c1834bbae"}}, {"head": {"id": "b57eda77-c148-4c63-9e23-248721ac6750", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32450069148833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0be4f121-7ab5-43d8-9376-7e65033ef419", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451359591500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "668587a0-f9b9-4b4b-8c0f-22d9ba69e0f6", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449632400166, "endTime": 32449640113291}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "d5e7ca29-949a-47b6-a249-0310f3a720f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5e7ca29-949a-47b6-a249-0310f3a720f1", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449632400166, "endTime": 32449640113291}, "additional": {"logType": "info", "children": [], "durationId": "668587a0-f9b9-4b4b-8c0f-22d9ba69e0f6", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "376e2674-9258-4d3b-9406-f85424f0fd4b", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449640171750, "endTime": 32449640233458}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "a233f77d-5d93-4d70-b77e-1188ba4c1c55"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a233f77d-5d93-4d70-b77e-1188ba4c1c55", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449640171750, "endTime": 32449640233458}, "additional": {"logType": "info", "children": [], "durationId": "376e2674-9258-4d3b-9406-f85424f0fd4b", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "d3c8304a-bb37-4402-beff-84389210281d", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449640242666, "endTime": 32449642244250}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "b817d471-4157-47b4-ab3e-fb3b5c29bcf8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b817d471-4157-47b4-ab3e-fb3b5c29bcf8", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449640242666, "endTime": 32449642244250}, "additional": {"logType": "info", "children": [], "durationId": "d3c8304a-bb37-4402-beff-84389210281d", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "2bfe363a-a644-464f-b2f6-6d1f01e176f1", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449642262250, "endTime": 32451054226708}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "fc0ee2f1-3fcf-49a3-9390-9b47b282174b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc0ee2f1-3fcf-49a3-9390-9b47b282174b", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449642262250, "endTime": 32451054226708}, "additional": {"logType": "info", "children": [], "durationId": "2bfe363a-a644-464f-b2f6-6d1f01e176f1", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451054244666, "endTime": 32451165734333}, "additional": {"children": ["a839750c-ab90-4395-b38f-ae4dd7c61a2a", "6958391f-75c5-486e-b2b4-a8ce86d8741d", "7fb15405-ead4-4f1e-84f5-072fb0d487a6"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451054244666, "endTime": 32451165734333}, "additional": {"logType": "info", "children": ["32f95ee3-a7bb-40d0-862c-c4c130affb28", "31b80696-3b16-47ae-b091-b5f2211511b5", "4e0b143f-4ec4-4888-94fd-6ab1cbcc43b7"], "durationId": "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "a839750c-ab90-4395-b38f-ae4dd7c61a2a", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451054281958, "endTime": 32451054286625}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "logId": "32f95ee3-a7bb-40d0-862c-c4c130affb28"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "32f95ee3-a7bb-40d0-862c-c4c130affb28", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451054281958, "endTime": 32451054286625}, "additional": {"logType": "info", "children": [], "durationId": "a839750c-ab90-4395-b38f-ae4dd7c61a2a", "parent": "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c"}}, {"head": {"id": "6958391f-75c5-486e-b2b4-a8ce86d8741d", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451054287291, "endTime": 32451124018583}, "additional": {"children": ["859de931-ec4f-409f-b22d-04c1dff0dff1"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "logId": "31b80696-3b16-47ae-b091-b5f2211511b5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "31b80696-3b16-47ae-b091-b5f2211511b5", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451054287291, "endTime": 32451124018583}, "additional": {"logType": "info", "children": ["029e5953-31a2-4d49-b8af-8eaa0218b0b1"], "durationId": "6958391f-75c5-486e-b2b4-a8ce86d8741d", "parent": "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c"}}, {"head": {"id": "859de931-ec4f-409f-b22d-04c1dff0dff1", "name": "module 'CityConst.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451107642458, "endTime": 32451117741041}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6958391f-75c5-486e-b2b4-a8ce86d8741d", "logId": "029e5953-31a2-4d49-b8af-8eaa0218b0b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "029e5953-31a2-4d49-b8af-8eaa0218b0b1", "name": "module 'CityConst.ets' pack", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451107642458, "endTime": 32451117741041}, "additional": {"logType": "info", "children": [], "durationId": "859de931-ec4f-409f-b22d-04c1dff0dff1", "parent": "31b80696-3b16-47ae-b091-b5f2211511b5"}}, {"head": {"id": "7fb15405-ead4-4f1e-84f5-072fb0d487a6", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451124019583, "endTime": 32451165724541}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "7fb0b74e-511c-4733-a25c-34ec2fbf237b", "logId": "4e0b143f-4ec4-4888-94fd-6ab1cbcc43b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e0b143f-4ec4-4888-94fd-6ab1cbcc43b7", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451124019583, "endTime": 32451165724541}, "additional": {"logType": "info", "children": [], "durationId": "7fb15405-ead4-4f1e-84f5-072fb0d487a6", "parent": "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c"}}, {"head": {"id": "b4a22b09-3a7d-4ee8-b6e3-cd6a889aefbc", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32451165741333, "endTime": 32451288587875}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "70b4940b-3d09-49e6-a132-f2845102c0ac", "logId": "839c1ee6-a064-4b3f-a2e0-cbaaf26c0d72"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "839c1ee6-a064-4b3f-a2e0-cbaaf26c0d72", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451165741333, "endTime": 32451288587875}, "additional": {"logType": "info", "children": [], "durationId": "b4a22b09-3a7d-4ee8-b6e3-cd6a889aefbc", "parent": "b6ef012b-729d-426b-96ac-1c7482456d9f"}}, {"head": {"id": "8949ca0b-3f79-4178-8b5f-f3639dfec010", "name": "default@CompileArkTS work[58] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451362796875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6ef012b-729d-426b-96ac-1c7482456d9f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker4", "startTime": 32449631438916, "endTime": 32451358806458}, "additional": {"logType": "info", "children": ["d5e7ca29-949a-47b6-a249-0310f3a720f1", "a233f77d-5d93-4d70-b77e-1188ba4c1c55", "b817d471-4157-47b4-ab3e-fb3b5c29bcf8", "fc0ee2f1-3fcf-49a3-9390-9b47b282174b", "a5ec1c1b-53f2-4c54-b954-452dbcabcf0c", "839c1ee6-a064-4b3f-a2e0-cbaaf26c0d72"], "durationId": "70b4940b-3d09-49e6-a132-f2845102c0ac", "parent": "eb97f25f-336e-4cdd-b8ee-1c5c1834bbae"}}, {"head": {"id": "7c34ceb7-0e77-4dd2-8d25-c02299f080df", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451362891500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb97f25f-336e-4cdd-b8ee-1c5c1834bbae", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449413132291, "endTime": 32451362967833, "totalTime": 1740937792}, "additional": {"logType": "info", "children": ["b6ef012b-729d-426b-96ac-1c7482456d9f", "f2093cf6-f009-4853-8974-9d30b479bf3f"], "durationId": "aec3a654-7b08-4372-8208-09b3aa860d21"}}, {"head": {"id": "c0ed20c9-3f02-4627-a471-dba978f49856", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365112916, "endTime": 32451365412583}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "3b91e614-4383-40da-a058-1cf95bbbdd40", "logId": "046d5334-27e1-4900-94d1-b0636a9a7d2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b91e614-4383-40da-a058-1cf95bbbdd40", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451364527666}, "additional": {"logType": "detail", "children": [], "durationId": "c0ed20c9-3f02-4627-a471-dba978f49856"}}, {"head": {"id": "547a061e-b718-4f7f-b2d4-5a9ed5878f0e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451364693541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afec57d4-8b01-4f40-899c-b84b2a59355b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451364725875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2c5f21e-b776-4304-9d38-fa4d719b429c", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365117041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb8fed2f-9745-44db-a8fc-a53179a9bc91", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365178791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "457e9b99-a6a2-485f-b867-9c5f15a475ff", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365361541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e700b9-f736-419c-8cf4-0d0b725d409f", "name": "entry : default@GeneratePkgModuleJson cost memory 0.0591583251953125", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365389291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "046d5334-27e1-4900-94d1-b0636a9a7d2d", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451365112916, "endTime": 32451365412583}, "additional": {"logType": "info", "children": [], "durationId": "c0ed20c9-3f02-4627-a471-dba978f49856"}}, {"head": {"id": "aa677e54-80f1-4f23-bb73-7b98f5c0223b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451368615916, "endTime": 32451661936750}, "additional": {"children": ["9afb523c-c774-47f3-810f-7600f255a7b8", "d874a284-e822-4e6c-b13b-ccbd32396a54", "972aec8e-c6f9-45ef-8af7-30daf0d42fe5"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets' has been changed."], "detailId": "65092605-2ad7-44cf-8929-9e9c00df7058", "logId": "1e7825b7-d4cb-411e-9122-81d884e4a599"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "65092605-2ad7-44cf-8929-9e9c00df7058", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451366147500}, "additional": {"logType": "detail", "children": [], "durationId": "aa677e54-80f1-4f23-bb73-7b98f5c0223b"}}, {"head": {"id": "204f1f84-8612-47df-8a8a-6afef9893a3e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451366249583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8c0b13d-cbb2-4f92-b005-0cbc0757653f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451366276125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d72b2d-c413-4a28-a244-c8907667a4d6", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451368621375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f380c826-16eb-43a7-b85b-a756ffdbceab", "name": "entry:default@PackageHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451371024791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2321f3-59e0-4aa9-9b97-d2950ff27f2b", "name": "Incremental task entry:default@PackageHap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451371083875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9afb523c-c774-47f3-810f-7600f255a7b8", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451371446375, "endTime": 32451371922166}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa677e54-80f1-4f23-bb73-7b98f5c0223b", "logId": "7e5ee604-8093-4cbc-af10-e3cddef67796"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c1afba1-03cc-48e8-a75a-948669e22217", "name": "Use tool [/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=utf-8',\n  '-jar',\n  '/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default',\n  '--json-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/package/default/module.json',\n  '--resources-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources',\n  '--index-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index',\n  '--pack-info-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info',\n  '--out-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap',\n  '--ets-path',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets'\n]", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451371864666}, "additional": {"logType": "debug", "children": [], "durationId": "aa677e54-80f1-4f23-bb73-7b98f5c0223b"}}, {"head": {"id": "7e5ee604-8093-4cbc-af10-e3cddef67796", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451371446375, "endTime": 32451371922166}, "additional": {"logType": "info", "children": [], "durationId": "9afb523c-c774-47f3-810f-7600f255a7b8", "parent": "1e7825b7-d4cb-411e-9122-81d884e4a599"}}, {"head": {"id": "d874a284-e822-4e6c-b13b-ccbd32396a54", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372235625, "endTime": 32451372985083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "aa677e54-80f1-4f23-bb73-7b98f5c0223b", "logId": "9747de66-96d0-4e60-9416-5984f5803318"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a51b9f9-496b-4870-a771-0fada5a5a676", "name": "default@PackageHap work[60] is submitted.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372585708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "972aec8e-c6f9-45ef-8af7-30daf0d42fe5", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32451397447208, "endTime": 32451661656375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "aa677e54-80f1-4f23-bb73-7b98f5c0223b", "logId": "5d4780a8-16ef-4b23-8174-af17feb00804"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c03178f7-68a9-46c5-97cb-60616ac7a2e0", "name": "default@PackageHap work[60] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372863666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb17c1e8-f3b4-454c-97ab-cf702047e65d", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372889791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e12200be-ae91-4cbb-9dce-5586a57d3b5a", "name": "default@PackageHap work[60] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372944083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4f4e0d-0e0a-4f26-96cc-d0dc71b3900a", "name": "default@PackageHap work[60] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372967458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9747de66-96d0-4e60-9416-5984f5803318", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451372235625, "endTime": 32451372985083}, "additional": {"logType": "info", "children": [], "durationId": "d874a284-e822-4e6c-b13b-ccbd32396a54", "parent": "1e7825b7-d4cb-411e-9122-81d884e4a599"}}, {"head": {"id": "64645177-e526-4163-abee-ed86363baf5a", "name": "entry : default@PackageHap cost memory 0.70672607421875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451374241458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc183214-e2ba-4412-ba19-1e7e122c51f3", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451661727291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc7e0c49-533e-4076-9ef5-c89cde4585d7", "name": "default@PackageHap work[60] done.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451661853166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d4780a8-16ef-4b23-8174-af17feb00804", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Worker6", "startTime": 32451397447208, "endTime": 32451661656375}, "additional": {"logType": "info", "children": [], "durationId": "972aec8e-c6f9-45ef-8af7-30daf0d42fe5", "parent": "1e7825b7-d4cb-411e-9122-81d884e4a599"}}, {"head": {"id": "59674ad9-b7a3-4e4a-a93f-0dd9bc805d86", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451661906416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e7825b7-d4cb-411e-9122-81d884e4a599", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451368615916, "endTime": 32451661936750, "totalTime": 269878834}, "additional": {"logType": "info", "children": ["7e5ee604-8093-4cbc-af10-e3cddef67796", "9747de66-96d0-4e60-9416-5984f5803318", "5d4780a8-16ef-4b23-8174-af17feb00804"], "durationId": "aa677e54-80f1-4f23-bb73-7b98f5c0223b"}}, {"head": {"id": "a142b5f5-917a-4845-a3be-34b7e16cf794", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667039000, "endTime": 32451667460291}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed."], "detailId": "7c1f1dbd-e66e-4189-9c79-036f60294c2e", "logId": "0389f6ab-5295-480b-b661-acd34cfb3667"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c1f1dbd-e66e-4189-9c79-036f60294c2e", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451664717458}, "additional": {"logType": "detail", "children": [], "durationId": "a142b5f5-917a-4845-a3be-34b7e16cf794"}}, {"head": {"id": "7a7fe060-bddc-4978-a7ed-a1bd1fb4f6fc", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451665491708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1f5fd83-e9f6-4998-967a-fe645bd75d21", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451665540291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b15c025-7c28-4ea2-a167-b3732a8020d6", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667048625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33f13f7d-e7fa-496f-b1c0-224bd0dcdc9b", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667176458}, "additional": {"logType": "warn", "children": [], "durationId": "a142b5f5-917a-4845-a3be-34b7e16cf794"}}, {"head": {"id": "71012162-3c43-4930-8c61-26b0ad50389e", "name": "entry:default@SignHap is not up-to-date, since the input file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667311125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53fc74ee-460a-4d14-a8e6-cd8fb8e82c47", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667342458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb697d08-afee-4627-a0b4-91491effb984", "name": "entry : default@SignHap cost memory 0.079803466796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667412041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5301a3ad-9126-457a-b641-eee073a063a9", "name": "runTaskFromQueue task cost before running: 2 s 637 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667442416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0389f6ab-5295-480b-b661-acd34cfb3667", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451667039000, "endTime": 32451667460291, "totalTime": 396292}, "additional": {"logType": "info", "children": [], "durationId": "a142b5f5-917a-4845-a3be-34b7e16cf794"}}, {"head": {"id": "43209392-4a51-4bc0-8216-2402a8c4a3b7", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669620166, "endTime": 32451669697208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "43224a9a-d0c8-422f-8c4f-103dec45e658", "logId": "16da599e-b795-4775-807b-276842069882"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43224a9a-d0c8-422f-8c4f-103dec45e658", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669603000}, "additional": {"logType": "detail", "children": [], "durationId": "43209392-4a51-4bc0-8216-2402a8c4a3b7"}}, {"head": {"id": "606c0adc-9774-44fd-9f99-aef1f438f850", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669622708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3da25e-16b6-4127-b9af-a9f74187c7ee", "name": "entry : assembleHap cost memory 0.01134490966796875", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669653375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03db2a64-33b8-4035-8cbb-242967f2c8b9", "name": "runTaskFromQueue task cost before running: 2 s 640 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669678458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16da599e-b795-4775-807b-276842069882", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451669620166, "endTime": 32451669697208, "totalTime": 52125}, "additional": {"logType": "info", "children": [], "durationId": "43209392-4a51-4bc0-8216-2402a8c4a3b7"}}, {"head": {"id": "de83f789-b80c-41a8-aca6-50e066311303", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670733541, "endTime": 32451670742041}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "dc91a470-fc80-4c2c-890a-6ffd90ee2afe", "logId": "3fee28d2-1119-4dc6-82c8-2633ef139bf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3fee28d2-1119-4dc6-82c8-2633ef139bf1", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670733541, "endTime": 32451670742041}, "additional": {"logType": "info", "children": [], "durationId": "de83f789-b80c-41a8-aca6-50e066311303"}}, {"head": {"id": "45b89787-6f91-409c-b069-ee72b80e48ed", "name": "BUILD SUCCESSFUL in 2 s 641 ms ", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670755083}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "077d9a11-ad08-4336-b2bc-0da075c0bc1b", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32449030571000, "endTime": 32451670821666}, "additional": {"time": {"year": 2024, "month": 12, "day": 10, "hour": 17, "minute": 33}, "completeCommand": "{\"mode\":\"module\",\"completeCommand\":\"--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon\",\"parallel\":true,\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"incremental\":true,\"_\":[\"assembleHap\"],\"daemon\":true,\"analyze\":\"normal\"};--mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=normal --parallel --incremental --daemon", "hvigorVersion": "5.13.1", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "317569a3-1a4a-4979-a44d-25ea57ea8ba2", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670838250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f515b19-9d28-49e7-ad25-47076b2a9d26", "name": "There is no need to refresh cache, since the incremental task upcloud:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670856000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15def80-815c-457e-b6a4-e7a651495518", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670871958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "207f08c6-562d-4bf6-842a-0a3feb760eb3", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670896458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c548d4-72e6-4b48-98ce-292c218408ba", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451670994291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe900a8-62d3-4abe-aa62-49960e41c308", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671310875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc0a8ccf-4551-4ec5-8ae1-d78583868228", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671416500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5028832-eef5-4fca-8ebd-d94c2c2c7549", "name": "There is no need to refresh cache, since the incremental task upcloud:default@CreateHarBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671438791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e664567c-9ad1-41ac-9ad7-d8ca5ebba033", "name": "There is no need to refresh cache, since the incremental task upcloud:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671456708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30ed5ad-91cf-45d4-aac5-84c145756a40", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671478750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3faad56-0ae5-44fa-ab51-915e06c52ef7", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451671536708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38cafdda-1a2f-4edf-a880-bdb361065bb5", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674724333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c1026b-baa4-4bdd-9a7b-a4a56c1ebdd6", "name": "Incremental task entry:default@SyscapTransform post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674817875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d1a75bf-db45-45f0-a70d-de12f74aab05", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674840541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02f70159-330a-4ed4-b377-955d6161ce31", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674858583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bfa585e-908b-4e7d-9730-c4c86c5868de", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674873625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d138d9b0-8478-4d5f-ae68-c9fb963f024b", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674891375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1432d348-3889-4795-a363-13f2c7734fd3", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674905000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b57afdd5-6028-4196-9f09-cdd4d7efe445", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674922666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b97b183-7060-47e3-966c-47d9f02fb6e3", "name": "There is no need to refresh cache, since the incremental task upcloud:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674937000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6a857b9-2db4-4613-92c2-d0ad1e4eece5", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674951875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0058ab09-0731-4288-be72-0fceffe1b2b6", "name": "There is no need to refresh cache, since the incremental task upcloud:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674966583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18663e03-2119-4126-a66a-4cb495191b75", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451674982625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c431453d-1aab-457f-9d1e-93efaa318885", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451675852625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb5bacd3-93d5-48af-bd6a-834ecaaa8966", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676093375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89bf3099-d579-4cd2-af6a-dd0f19d583b3", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676126625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3577000f-17f3-4aaa-b706-8d1d23cdaa43", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676161291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ba18db9-359e-4c98-a042-fd4087103a40", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676192458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98865474-9687-43dd-9245-fb18c8ce597a", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676220541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d2bd50-fd5e-45c3-9f00-6a3a5158fc2b", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451676249833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6823b8f1-4d1f-49ce-a20e-1a67e9f3031e", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451679636041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "684dcca2-1e09-404a-b14f-11d968640100", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451684422291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca305130-f80a-43a6-b71b-c218691a3022", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451692233416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b0994ec-cbe4-43ea-9b87-9f96952700bd", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451694934291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad22fe99-05f3-46ad-b003-aeb5d60874bf", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451695384958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6917835b-2e7c-4c65-b902-6fd673c11542", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451695420250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b79a115d-b135-4ea7-b046-abdde3327e1d", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451695450000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b51401e-1d4d-455d-9bb6-6063a45b96bf", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451695603416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e78e751-1a30-4c54-8ab9-4d8633205c1a", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696079916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee9b269-2f45-4961-924b-f1ed91b548ce", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696115333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b63ac087-0661-4068-9f01-e4c2787c1a1c", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/mock/mock-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696147000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3c73ab5-6654-4646-bff0-38473448bbb4", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696204666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7af2a8e3-1bac-4788-85f3-eb81209bfc8a", "name": "Incremental task entry:default@CompileArkTS post-execution cost:22 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696393375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ecf1cf8-8036-43f0-b53f-0b92c9aff26b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451696898000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac4d8d8d-f50b-475a-aff0-8914af784b07", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697478958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cde2554-ecf7-466e-bb79-6c087986cdca", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697529291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d1acf8c-063a-4016-8dc7-313771d590eb", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697561541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d3e070-a8d5-4227-8999-277b59058e68", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697592541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4bf0809-0fc8-4624-b722-8b075ae9ec47", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697619708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a4dbd7-3008-41f7-ae91-8bb38e714317", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697646791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f9b6e21-0619-4b03-a485-6181fa12bbf2", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451697814875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11bd8fec-1c57-44e1-ac96-27f4006fa56b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451699660125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c667b90-3229-4733-b33a-50ff314dce2a", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451699699083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37d1f367-b880-4fa2-9ac6-4bf95bfb9bb8", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451699729583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "085c457b-b161-4144-9f99-1369dfaae0f5", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700035000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b667696-09af-478d-8dd5-a662aabc53dd", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700117291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53422e65-c761-41a5-b264-3331c1dac983", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700134666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bedbb48a-a6ee-4ba9-ade6-df841975751c", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700151125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db5c7cfa-06c0-43fb-a86a-324e2d24e1e8", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700166208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f771a6-ae63-4f04-ba13-9a055a41e6d6", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700184166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a82c79ff-cdde-43f0-9d57-4a39b131fee6", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/stripped_native_libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700462458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e80e4a-a313-4f80-b337-53d139c99a3a", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700595625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebc8e66c-6bae-4702-a24c-1578b7285863", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451700628625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d925efd2-924c-452b-9a05-be3fd1084d53", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451701777583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1d582a1-78dc-4c1e-9e72-6415b9aab149", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451701811083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de69d35f-1622-45c4-b09c-c25d3db62892", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451701840875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f36d3615-aca2-4f50-af8b-2075c112c911", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451701858333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3744632c-9ade-4783-82fe-6f58a11c0854", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702070750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b84ee0-0507-4e28-bb4f-94d18635be65", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/mapping/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702102875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0430ede-f854-4f32-9ba2-cf24c3268e66", "name": "Incremental task entry:default@PackageHap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702146541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc203cbe-9020-4b3e-aa67-1d20f65c6a25", "name": "Update task entry:default@SignHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702184416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59625acf-4306-4bc5-9841-1fd8bb43cd8c", "name": "Update task entry:default@SignHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702207958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca0166a1-f9fd-414a-b1b6-d6fbede89d09", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 17242, "tid": "Main Thread", "startTime": 32451702252375}, "additional": {"logType": "debug", "children": []}}], "workLog": []}