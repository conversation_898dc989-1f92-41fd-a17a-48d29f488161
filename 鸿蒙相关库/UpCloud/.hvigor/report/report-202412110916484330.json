{"version": "2.0", "ppid": 23465, "events": [{"head": {"id": "43be45f0-9486-4fb7-830e-b8b1c38e1d71", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082862559166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082868909125, "endTime": 2083934086333}, "additional": {"children": ["8f1ad56a-2eb3-4402-89b1-cd612a2bd3e9", "796f9493-7ea2-4f2c-a86b-4ab543843812", "891b3dc7-25fe-4ba7-82af-71cd9c8da5ec", "1a1e8870-5d77-44df-882e-e6b69bdcd70f", "d662b339-1eb2-46fb-9af1-5d1c1a2945e8", "2d5f1395-1978-4023-8fee-f38c31d196e5", "c6d6d0b2-ffee-4bf0-81ab-26b1655d9d1f"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8f1ad56a-2eb3-4402-89b1-cd612a2bd3e9", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082868911041, "endTime": 2082891974875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "a90b9724-ce69-4c18-bd53-f2d2135f76d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "796f9493-7ea2-4f2c-a86b-4ab543843812", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082891986208, "endTime": 2083932492333}, "additional": {"children": ["c3560a25-46db-4998-beea-d3a38a48cc9d", "0d789c99-86b9-47b5-9e93-054dcf093969", "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "e5e321c3-3417-494d-9a19-c75169147124", "474401e3-5611-46f2-9b1c-ecc20358d5b0", "2ddaf9bd-8a67-4040-9561-e10af08c0567", "1694ae0c-7e5d-4c63-8fd1-8345644aa964", "a0040865-2e75-4a9f-a220-f018b4b476be", "f9590f24-fe87-4b0d-b381-80fe9de6b4ee"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "891b3dc7-25fe-4ba7-82af-71cd9c8da5ec", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083932508791, "endTime": 2083934074291}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "a3bc3e79-b9bb-499f-943e-ae8339ca8780"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a1e8870-5d77-44df-882e-e6b69bdcd70f", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083934077041, "endTime": 2083934082541}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "ae13e38b-f1fe-4e03-8172-2ee4192b2cab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d662b339-1eb2-46fb-9af1-5d1c1a2945e8", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082872180833, "endTime": 2082872405208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "e67f0ee4-a4cd-4037-b27a-53f324e96cdd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e67f0ee4-a4cd-4037-b27a-53f324e96cdd", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082872180833, "endTime": 2082872405208}, "additional": {"logType": "info", "children": [], "durationId": "d662b339-1eb2-46fb-9af1-5d1c1a2945e8", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "2d5f1395-1978-4023-8fee-f38c31d196e5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082884139291, "endTime": 2082884181208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "19b34191-087e-4a5e-b1de-a73571b95799"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "19b34191-087e-4a5e-b1de-a73571b95799", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082884139291, "endTime": 2082884181208}, "additional": {"logType": "info", "children": [], "durationId": "2d5f1395-1978-4023-8fee-f38c31d196e5", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "512b5c69-fed0-41b0-8f7c-79d0ae26c243", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082884562166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c75b4ee-7b60-4f30-be5c-6c886af9cad6", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082891878375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a90b9724-ce69-4c18-bd53-f2d2135f76d4", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082868911041, "endTime": 2082891974875}, "additional": {"logType": "info", "children": [], "durationId": "8f1ad56a-2eb3-4402-89b1-cd612a2bd3e9", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "c3560a25-46db-4998-beea-d3a38a48cc9d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082899054583, "endTime": 2082899093833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "1331653e-b391-4f0d-8a8c-9b619379dfe4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d789c99-86b9-47b5-9e93-054dcf093969", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082899122291, "endTime": 2082902165416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "2466bc16-04e5-4ff4-afd2-d38af18c762f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082902237125, "endTime": 2083800835291}, "additional": {"children": ["0f1e0647-c515-4a93-8f67-a03cd4904fbf", "0e261588-82fa-4dc9-80c9-ac75d92a6648", "8e1d59aa-6cb2-417d-afe9-c5b2d07476c5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "04b62deb-b9f8-4811-87c2-f9eb64c59a44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5e321c3-3417-494d-9a19-c75169147124", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800978375, "endTime": 2083857161125}, "additional": {"children": ["ebb918bd-4829-4cb4-9354-d84449406898", "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "c12f253a-f8c1-45ae-813f-7029a7d85138"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "474401e3-5611-46f2-9b1c-ecc20358d5b0", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083857189958, "endTime": 2083901772083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "ccb11c91-d8dd-4898-ac63-8d37e7539581"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ddaf9bd-8a67-4040-9561-e10af08c0567", "name": "exec before all nodes", "description": "Execute before all nodes evaluated.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082897987791, "endTime": 2083932494958}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1694ae0c-7e5d-4c63-8fd1-8345644aa964", "name": "exec after all nodes", "description": "Execute after all nodes evaluated.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082898360208, "endTime": 2083932495458}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a0040865-2e75-4a9f-a220-f018b4b476be", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083932424666, "endTime": 2083932482833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "9d69e985-f1c5-45bc-836d-247ef5633b18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1331653e-b391-4f0d-8a8c-9b619379dfe4", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082899054583, "endTime": 2082899093833}, "additional": {"logType": "info", "children": [], "durationId": "c3560a25-46db-4998-beea-d3a38a48cc9d", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "2466bc16-04e5-4ff4-afd2-d38af18c762f", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082899122291, "endTime": 2082902165416}, "additional": {"logType": "info", "children": [], "durationId": "0d789c99-86b9-47b5-9e93-054dcf093969", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "0f1e0647-c515-4a93-8f67-a03cd4904fbf", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082902789125, "endTime": 2082902814416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "logId": "063ebfae-95ae-4812-be73-388f3bdce136"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "063ebfae-95ae-4812-be73-388f3bdce136", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082902789125, "endTime": 2082902814416}, "additional": {"logType": "info", "children": [], "durationId": "0f1e0647-c515-4a93-8f67-a03cd4904fbf", "parent": "04b62deb-b9f8-4811-87c2-f9eb64c59a44"}}, {"head": {"id": "0e261588-82fa-4dc9-80c9-ac75d92a6648", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082904125291, "endTime": 2083800245541}, "additional": {"children": ["db9e037f-70d9-4d45-9dcf-cd3ca1a62414", "18253f0d-e01d-4cb3-aadc-35c5b59978ef"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "logId": "0e606469-26ef-41df-ae07-4b39e7cd16d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "db9e037f-70d9-4d45-9dcf-cd3ca1a62414", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082904125916, "endTime": 2083641530208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e261588-82fa-4dc9-80c9-ac75d92a6648", "logId": "ebcfb3f3-63e7-46e8-bd19-833930d86493"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18253f0d-e01d-4cb3-aadc-35c5b59978ef", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083641543083, "endTime": 2083800235833}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0e261588-82fa-4dc9-80c9-ac75d92a6648", "logId": "42740b46-2725-4f7d-bd20-cbb62eeddc80"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7044106c-19f2-45ad-8e65-c6d508dfba08", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082904129541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c382521-aa4d-4bf8-a076-809610b6f0ba", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083641441833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebcfb3f3-63e7-46e8-bd19-833930d86493", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082904125916, "endTime": 2083641530208}, "additional": {"logType": "info", "children": [], "durationId": "db9e037f-70d9-4d45-9dcf-cd3ca1a62414", "parent": "0e606469-26ef-41df-ae07-4b39e7cd16d7"}}, {"head": {"id": "b8a54490-30d7-477d-ae58-bef2d9922aec", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083641598500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea99da54-9d75-48f8-99b3-4dfc0a6af431", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083747958833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "263660de-4dc9-4b74-a10d-55865047f980", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083748110958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "668d6546-26f1-46c2-bcde-20531b99b24f", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083748427083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92c3c197-b063-4de1-8a5b-3e7b68ca6e4a", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083748487208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56818994-8f26-47b5-94a3-ad868703b388", "name": "Product 'default' using build option: {\n  \"debuggable\": true\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083750521541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8d40380-0712-4670-a625-84b09d8c5a96", "name": "not found resModel json file in : /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083757313333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fcb8d66-397b-48d9-a4b7-bd6d4d2e6556", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083759183666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "744cd23d-d016-47b9-83e0-798f614cec11", "name": "harmonyOS sdk scan", "description": "HarmonyOS sdk scan", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083761453208, "endTime": 2083762848708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "Other", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "726e8e48-581c-4a66-b91f-e742e7add1dd", "name": "Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083761728125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53815c1b-a9fa-45b4-8e59-233cbb550463", "name": "hmscore sdk scan", "description": "Hmscore sdk scan", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083763531583, "endTime": 2083764717916}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "Other", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8fe713b-c72e-4798-9179-28521809c5ac", "name": "Local scan or download hmscore sdk components toolchains,ets,native", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083763999458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "943c25e8-b536-4071-8571-18a2e502b006", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083767173583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e229cd-3c2b-440b-9cec-086e1fd7f23a", "name": "Sdk init in 26 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083786111333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d73c601-166f-4237-b10a-d0553c8b8de4", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083786248333}, "additional": {"time": {"year": 2024, "month": 12, "day": 11, "hour": 9, "minute": 16}, "markType": "other"}}, {"head": {"id": "b94fd13b-3c45-48ba-8fae-22df3641ba93", "name": "caseSensitive<PERSON><PERSON>ck<PERSON>ff", "description": "caseSensitive<PERSON><PERSON><PERSON> check is off", "type": "mark"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083786300666}, "additional": {"time": {"year": 2024, "month": 12, "day": 11, "hour": 9, "minute": 16}, "markType": "other"}}, {"head": {"id": "e641b3ba-9ad4-42c6-a735-736d280a8b6f", "name": "Project task initialization takes 13 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083799979958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02f58cd8-4761-46d5-a4b2-44a031e66df3", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800147916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44bddb83-4215-4445-89cb-e6b568ba80c1", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800178125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fdb23a0-1257-4cb4-900f-e314fd5a9505", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800204333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42740b46-2725-4f7d-bd20-cbb62eeddc80", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083641543083, "endTime": 2083800235833}, "additional": {"logType": "info", "children": [], "durationId": "18253f0d-e01d-4cb3-aadc-35c5b59978ef", "parent": "0e606469-26ef-41df-ae07-4b39e7cd16d7"}}, {"head": {"id": "0e606469-26ef-41df-ae07-4b39e7cd16d7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082904125291, "endTime": 2083800245541}, "additional": {"logType": "info", "children": ["ebcfb3f3-63e7-46e8-bd19-833930d86493", "42740b46-2725-4f7d-bd20-cbb62eeddc80"], "durationId": "0e261588-82fa-4dc9-80c9-ac75d92a6648", "parent": "04b62deb-b9f8-4811-87c2-f9eb64c59a44"}}, {"head": {"id": "8e1d59aa-6cb2-417d-afe9-c5b2d07476c5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800802791, "endTime": 2083800824333}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "logId": "56673f70-0d1a-43a8-8f84-c75c07edfd4a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56673f70-0d1a-43a8-8f84-c75c07edfd4a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800802791, "endTime": 2083800824333}, "additional": {"logType": "info", "children": [], "durationId": "8e1d59aa-6cb2-417d-afe9-c5b2d07476c5", "parent": "04b62deb-b9f8-4811-87c2-f9eb64c59a44"}}, {"head": {"id": "04b62deb-b9f8-4811-87c2-f9eb64c59a44", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082902237125, "endTime": 2083800835291}, "additional": {"logType": "info", "children": ["063ebfae-95ae-4812-be73-388f3bdce136", "0e606469-26ef-41df-ae07-4b39e7cd16d7", "56673f70-0d1a-43a8-8f84-c75c07edfd4a"], "durationId": "208f7d21-03b1-4c69-8e4b-6ae640426eaf", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "ebb918bd-4829-4cb4-9354-d84449406898", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083801278625, "endTime": 2083823984791}, "additional": {"children": ["88b87028-f76f-4241-b02c-8ce0ec8ea4b3", "ea755e9e-1aa0-4c1b-8d6f-a38368644f94", "dfef6bc5-b087-4a8a-be50-1f87b97afce6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5e321c3-3417-494d-9a19-c75169147124", "logId": "b890ef17-cebf-43d5-bd1a-2dc75c9ea69b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88b87028-f76f-4241-b02c-8ce0ec8ea4b3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083802993750, "endTime": 2083803003000}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebb918bd-4829-4cb4-9354-d84449406898", "logId": "5ddafa81-5b49-4333-80d1-21949b786e08"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ddafa81-5b49-4333-80d1-21949b786e08", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083802993750, "endTime": 2083803003000}, "additional": {"logType": "info", "children": [], "durationId": "88b87028-f76f-4241-b02c-8ce0ec8ea4b3", "parent": "b890ef17-cebf-43d5-bd1a-2dc75c9ea69b"}}, {"head": {"id": "ea755e9e-1aa0-4c1b-8d6f-a38368644f94", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083804194875, "endTime": 2083823163333}, "additional": {"children": ["132f2023-9a00-4944-84a3-0457db4d42b9", "93a1c499-6beb-4a5f-a8b4-fe4cacd8df8d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebb918bd-4829-4cb4-9354-d84449406898", "logId": "9b023ff8-0adb-4fd2-9445-76ff494a91e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "132f2023-9a00-4944-84a3-0457db4d42b9", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083804195166, "endTime": 2083808357166}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea755e9e-1aa0-4c1b-8d6f-a38368644f94", "logId": "8e33955e-8fe9-42e6-9d83-201e95354272"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93a1c499-6beb-4a5f-a8b4-fe4cacd8df8d", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083808365750, "endTime": 2083823156750}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea755e9e-1aa0-4c1b-8d6f-a38368644f94", "logId": "19a0591d-305f-4791-94fb-1f008bd3943e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c73e820c-ae87-49d4-adf1-b9385946770e", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083804197500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ab35c6f-1b50-405b-b149-0a388aafd7c6", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083808280458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e33955e-8fe9-42e6-9d83-201e95354272", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083804195166, "endTime": 2083808357166}, "additional": {"logType": "info", "children": [], "durationId": "132f2023-9a00-4944-84a3-0457db4d42b9", "parent": "9b023ff8-0adb-4fd2-9445-76ff494a91e0"}}, {"head": {"id": "fbdbb325-c938-4abf-bf7a-8a148c23f446", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083808424791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49db8665-c627-4b50-bc2f-972ecfb95a19", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083817895708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8d0816-644b-4478-88a2-ca844533605f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818130333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f41dcc-24f7-4c7b-bc83-a59ae7c8e940", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818354000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "330855f5-d930-427d-8b10-a44913c8c246", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818422291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c794789-764c-41ef-92a9-acc75978f258", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818449333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a376e3e-2d72-410c-bcb0-beec4db66e3e", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818469958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff6f43f-e1f6-42d0-8348-0dc2f0e67689", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083818578708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "976f880f-d95d-4c55-acc5-89d03cf04960", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823002625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14034707-7714-4d09-8ffa-6e33a7e8d521", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823086500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798b7451-d2df-4a29-b34f-5d1b4681d566", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823114000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ace1aa06-0545-4a55-aaf1-f11594b58096", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823136083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a0591d-305f-4791-94fb-1f008bd3943e", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083808365750, "endTime": 2083823156750}, "additional": {"logType": "info", "children": [], "durationId": "93a1c499-6beb-4a5f-a8b4-fe4cacd8df8d", "parent": "9b023ff8-0adb-4fd2-9445-76ff494a91e0"}}, {"head": {"id": "9b023ff8-0adb-4fd2-9445-76ff494a91e0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083804194875, "endTime": 2083823163333}, "additional": {"logType": "info", "children": ["8e33955e-8fe9-42e6-9d83-201e95354272", "19a0591d-305f-4791-94fb-1f008bd3943e"], "durationId": "ea755e9e-1aa0-4c1b-8d6f-a38368644f94", "parent": "b890ef17-cebf-43d5-bd1a-2dc75c9ea69b"}}, {"head": {"id": "dfef6bc5-b087-4a8a-be50-1f87b97afce6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823965583, "endTime": 2083823975708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ebb918bd-4829-4cb4-9354-d84449406898", "logId": "6f0c6917-08c1-4823-b320-d0f41b9b966a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f0c6917-08c1-4823-b320-d0f41b9b966a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083823965583, "endTime": 2083823975708}, "additional": {"logType": "info", "children": [], "durationId": "dfef6bc5-b087-4a8a-be50-1f87b97afce6", "parent": "b890ef17-cebf-43d5-bd1a-2dc75c9ea69b"}}, {"head": {"id": "b890ef17-cebf-43d5-bd1a-2dc75c9ea69b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083801278625, "endTime": 2083823984791}, "additional": {"logType": "info", "children": ["5ddafa81-5b49-4333-80d1-21949b786e08", "9b023ff8-0adb-4fd2-9445-76ff494a91e0", "6f0c6917-08c1-4823-b320-d0f41b9b966a"], "durationId": "ebb918bd-4829-4cb4-9354-d84449406898", "parent": "c12f253a-f8c1-45ae-813f-7029a7d85138"}}, {"head": {"id": "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083824315000, "endTime": 2083857154708}, "additional": {"children": ["052eba23-3449-49fb-8cba-cbe4633dce25", "1ecd8008-302e-4a0b-9e27-d5ff44b97dca", "8a2db70e-d5a6-4f99-9a18-3990249c02b2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5e321c3-3417-494d-9a19-c75169147124", "logId": "dbbc4a7d-a6cb-457c-8500-8d99d588ae54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "052eba23-3449-49fb-8cba-cbe4633dce25", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083825684791, "endTime": 2083825691875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9", "logId": "d326c282-d7cd-4ef1-a49c-b22e2823e119"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d326c282-d7cd-4ef1-a49c-b22e2823e119", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083825684791, "endTime": 2083825691875}, "additional": {"logType": "info", "children": [], "durationId": "052eba23-3449-49fb-8cba-cbe4633dce25", "parent": "dbbc4a7d-a6cb-457c-8500-8d99d588ae54"}}, {"head": {"id": "1ecd8008-302e-4a0b-9e27-d5ff44b97dca", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083827877541, "endTime": 2083856477833}, "additional": {"children": ["1675349c-dfe5-488f-8cce-d640aa733fd1", "b3dd0000-6304-4f1b-bf68-8875b5fab0fe"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9", "logId": "6105ac38-3538-4080-9a41-1b5a7b7fc00c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1675349c-dfe5-488f-8cce-d640aa733fd1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083827877958, "endTime": 2083833361166}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ecd8008-302e-4a0b-9e27-d5ff44b97dca", "logId": "c8333862-22a0-4aa0-b215-6bb5853ff4af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b3dd0000-6304-4f1b-bf68-8875b5fab0fe", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083833370083, "endTime": 2083856471083}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1ecd8008-302e-4a0b-9e27-d5ff44b97dca", "logId": "934171ce-2a15-407b-ad01-3e4b050a2851"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c07f69d-096d-4068-abc7-9e0c6d0799bd", "name": "hvigorfile, resolving /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083827880125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76bee7f6-ce03-4212-a48b-658435c57847", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083833290750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8333862-22a0-4aa0-b215-6bb5853ff4af", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083827877958, "endTime": 2083833361166}, "additional": {"logType": "info", "children": [], "durationId": "1675349c-dfe5-488f-8cce-d640aa733fd1", "parent": "6105ac38-3538-4080-9a41-1b5a7b7fc00c"}}, {"head": {"id": "391df78e-1a10-4efe-9b77-29dfdb00667b", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083833376875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36416655-e2b2-4edf-9cc1-ee69bc1125b9", "name": "Start initialize module-target build option map, moduleName=upcloud, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083852521500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff1bc15-0acd-4567-81bb-07f4d8aec7eb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083853825125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2a70384-5884-4ca1-9925-c00263a2e64e", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083854044791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc8e3024-26ae-4bc5-83ac-4248357f413e", "name": "Module 'upcloud' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083854140458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab1bd11-173d-495d-b5a9-9992d510b328", "name": "Module 'upcloud' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083854271333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46f17d54-f80d-4db1-ad5b-92a11eb1580f", "name": "End initialize module-target build option map, moduleName=upcloud", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083854302000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa68f22e-5000-401e-b26f-faf05b81dbba", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083854329291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5504cb00-67f0-4011-acee-e251ae27459d", "name": "Module upcloud task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083856341291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f79e575d-b992-4f16-9aba-91d7a99bdb7e", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083856404750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc635f5-5ac0-42f7-84ff-f12bcd11d13c", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083856431125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed8ccdf6-630d-44c2-b483-d2e695d2a5d5", "name": "hvigorfile, resolve finished /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083856452125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934171ce-2a15-407b-ad01-3e4b050a2851", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083833370083, "endTime": 2083856471083}, "additional": {"logType": "info", "children": [], "durationId": "b3dd0000-6304-4f1b-bf68-8875b5fab0fe", "parent": "6105ac38-3538-4080-9a41-1b5a7b7fc00c"}}, {"head": {"id": "6105ac38-3538-4080-9a41-1b5a7b7fc00c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083827877541, "endTime": 2083856477833}, "additional": {"logType": "info", "children": ["c8333862-22a0-4aa0-b215-6bb5853ff4af", "934171ce-2a15-407b-ad01-3e4b050a2851"], "durationId": "1ecd8008-302e-4a0b-9e27-d5ff44b97dca", "parent": "dbbc4a7d-a6cb-457c-8500-8d99d588ae54"}}, {"head": {"id": "8a2db70e-d5a6-4f99-9a18-3990249c02b2", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083857141833, "endTime": 2083857147750}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9", "logId": "f4657f21-942d-4275-b863-0923c1cdd05a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4657f21-942d-4275-b863-0923c1cdd05a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083857141833, "endTime": 2083857147750}, "additional": {"logType": "info", "children": [], "durationId": "8a2db70e-d5a6-4f99-9a18-3990249c02b2", "parent": "dbbc4a7d-a6cb-457c-8500-8d99d588ae54"}}, {"head": {"id": "dbbc4a7d-a6cb-457c-8500-8d99d588ae54", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083824315000, "endTime": 2083857154708}, "additional": {"logType": "info", "children": ["d326c282-d7cd-4ef1-a49c-b22e2823e119", "6105ac38-3538-4080-9a41-1b5a7b7fc00c", "f4657f21-942d-4275-b863-0923c1cdd05a"], "durationId": "b0cf49e5-64e4-4b8e-bdb5-1508083e03f9", "parent": "c12f253a-f8c1-45ae-813f-7029a7d85138"}}, {"head": {"id": "c12f253a-f8c1-45ae-813f-7029a7d85138", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083800978375, "endTime": 2083857161125}, "additional": {"logType": "info", "children": ["b890ef17-cebf-43d5-bd1a-2dc75c9ea69b", "dbbc4a7d-a6cb-457c-8500-8d99d588ae54"], "durationId": "e5e321c3-3417-494d-9a19-c75169147124", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "eec3ef47-85dd-4eae-b6c4-474891a54f54", "name": "watch files: [\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/hvigorfile.ts',\n  '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/hvigorfile.ts',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Applications/DevEco-Studio.app/Contents/tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1846 more items\n]", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083871747083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1ef731-6b1f-4bbe-9a2e-003d8b81a18c", "name": "hvigorfile, resolve hvigorfile dependencies in 45 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083901690208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb11c91-d8dd-4898-ac63-8d37e7539581", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083857189958, "endTime": 2083901772083}, "additional": {"logType": "info", "children": [], "durationId": "474401e3-5611-46f2-9b1c-ecc20358d5b0", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "f9590f24-fe87-4b0d-b381-80fe9de6b4ee", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083902475625, "endTime": 2083902830208}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "796f9493-7ea2-4f2c-a86b-4ab543843812", "logId": "b4de1185-1172-4a5b-9ab3-782ccbd965fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be225ebd-e2c3-4b3a-af80-6484d112a818", "name": "project has submodules:entry,upcloud", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083902579458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdf83a57-c2b6-4fd5-89ff-81ff18f0a2e1", "name": "module:upcloud no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083902798750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4de1185-1172-4a5b-9ab3-782ccbd965fc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083902475625, "endTime": 2083902830208}, "additional": {"logType": "info", "children": [], "durationId": "f9590f24-fe87-4b0d-b381-80fe9de6b4ee", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "2e60ffff-ab1d-4791-aca2-f7d612dedfd8", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083903959541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ca763a-45eb-42d5-8fe9-59373e951bdb", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083907916583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e8b692e-3884-4c16-9a76-a313697c946e", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083916742583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5e4b0c24-3f68-4e6a-97e4-3808f4afd264", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083917006000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d727d6c8-fc7a-4e96-9205-7a6ed0c631d2", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083917213333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "dee2a843-ed8e-49f6-a5bf-8fc75c025a79", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083917422083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "104660c6-5af4-4883-a236-68b5ebb824ff", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083917630958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "af318422-f869-43a4-a4d7-a581d3cbac82", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083917833375}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cfd9a728-eceb-44a7-80b9-09d19b7675bc", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083918032583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a7610c9a-61cb-49f5-9302-628dee6aa071", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083918228333}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e8144e5e-a7fb-4f61-86b1-a48eb715646e", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083918426833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d8b8be3f-3865-456b-a80e-23e550ea33ef", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083918631916}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c4a257aa-2ea7-47fd-8760-efba53006457", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083918834916}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8bf0f86a-0b05-4669-8c23-d58190d1e8f2", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083919046583}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "dfcf43c5-2c23-4443-93b7-199bdaa749a8", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083919280416}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "00f26cae-8c45-4aac-ba0b-c3fbb7c2ccd6", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083919479666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "40e32190-cabe-4153-aed7-4f4b6ac099cb", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083919672666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "625e3659-8cf3-45e7-85c1-a24f370a6440", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083920260541}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d3560e36-362e-4cff-8c81-9440ae554779", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083920472625}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "4eaf9180-4250-4c0f-98c5-c6bea0651754", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083920664000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e289ff25-a32c-40e4-bcc8-46f5d3148051", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083920858791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c1e93d7b-c16c-4a83-8f2c-6697e5a8b0d0", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083921062500}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "98912ac2-b992-4a73-b3ba-850c0ca7583b", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083921263791}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "bd8ab9da-e5f2-4eb0-949c-0805b85ec2a9", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083921467083}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b638d442-a87e-48f3-8913-863c8030ba1d", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083921670166}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "3c14018f-013b-48e4-840e-3dc91f050555", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083921877708}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d2650bf7-c17c-49ef-8048-4f6c97cb0310", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083922088000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0ffd2570-92de-406a-ae56-5501c8d104cc", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083922290291}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "53abfc7a-428e-4bc0-a057-dac164a25953", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083922496958}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "3b8e35f1-e7a0-4900-a8e4-125e20ea3a45", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083922698541}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "7fb514d2-6ba6-4eee-9333-de11813149c5", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083922895916}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "782e0d75-b24f-48ff-ac5c-66215d3098bc", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083923085750}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "238b2112-0065-4bce-8a04-e159199ad67f", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083923632041}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c6eefadf-b96e-417f-99d5-44611e2284ce", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083923874000}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "36b7f9d4-0da7-45cf-a5e6-37018160585b", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083924095750}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "4a86833d-4387-4898-9879-e76cef75b2dd", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083924312291}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "fdda6c2b-55a0-4c7d-9d51-b38bcb7ca8c7", "name": "The current module 'UpCloud' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083924522125}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "79a99a6b-b0b2-4db5-a8af-9d1cf4586d64", "name": "Module UpCloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083924691333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0d25711-1b86-421c-8f57-f43ca6bcf5af", "name": "Module UpCloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083924717583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d8e7175-62d5-41e7-b9a2-6d1e7c3e8e7c", "name": "Module entry Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083927225541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b492fb4-3359-4f71-b569-3bc780769305", "name": "Module entry's total dependency: 9", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083927260666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42bfe49d-7107-42c4-9886-6e6cce75662f", "name": "Module upcloud Collected Dependency: /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/class-transformer@0.5.1/oh_modules/class-transformer,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/reflect-metadata@0.1.13/oh_modules/reflect-metadata,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/pako@1.0.2/oh_modules/pako,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js,/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/base64-js@1.5.1/oh_modules/base64-js", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083931046833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "015aaa21-79a0-466f-a685-8b42bdbc8989", "name": "Module upcloud's total dependency: 8", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083931090666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3e5bb4-2e7c-4c05-99bf-9d381178c8b8", "name": "Configuration phase cost:1 s 34 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083932455333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d69e985-f1c5-45bc-836d-247ef5633b18", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083932424666, "endTime": 2083932482833}, "additional": {"logType": "info", "children": [], "durationId": "a0040865-2e75-4a9f-a220-f018b4b476be", "parent": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2"}}, {"head": {"id": "f9d29d38-cc0c-41e2-809f-bd1981c66ac2", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082891986208, "endTime": 2083932492333}, "additional": {"logType": "info", "children": ["1331653e-b391-4f0d-8a8c-9b619379dfe4", "2466bc16-04e5-4ff4-afd2-d38af18c762f", "04b62deb-b9f8-4811-87c2-f9eb64c59a44", "c12f253a-f8c1-45ae-813f-7029a7d85138", "ccb11c91-d8dd-4898-ac63-8d37e7539581", "9d69e985-f1c5-45bc-836d-247ef5633b18", "b4de1185-1172-4a5b-9ab3-782ccbd965fc"], "durationId": "796f9493-7ea2-4f2c-a86b-4ab543843812", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "c6d6d0b2-ffee-4bf0-81ab-26b1655d9d1f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083934044541, "endTime": 2083934067708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "766f4c40-fd54-4f65-b642-0bdc67fb05b6", "logId": "a8348a42-d2f2-4ad6-8b0a-6d6539e39714"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8348a42-d2f2-4ad6-8b0a-6d6539e39714", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083934044541, "endTime": 2083934067708}, "additional": {"logType": "info", "children": [], "durationId": "c6d6d0b2-ffee-4bf0-81ab-26b1655d9d1f", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "a3bc3e79-b9bb-499f-943e-ae8339ca8780", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083932508791, "endTime": 2083934074291}, "additional": {"logType": "info", "children": [], "durationId": "891b3dc7-25fe-4ba7-82af-71cd9c8da5ec", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "ae13e38b-f1fe-4e03-8172-2ee4192b2cab", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083934077041, "endTime": 2083934082541}, "additional": {"logType": "info", "children": [], "durationId": "1a1e8870-5d77-44df-882e-e6b69bdcd70f", "parent": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493"}}, {"head": {"id": "184e4206-bb08-4e3b-9ee8-dfa7fd35d493", "name": "init", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082868909125, "endTime": 2083934086333}, "additional": {"logType": "info", "children": ["a90b9724-ce69-4c18-bd53-f2d2135f76d4", "f9d29d38-cc0c-41e2-809f-bd1981c66ac2", "a3bc3e79-b9bb-499f-943e-ae8339ca8780", "ae13e38b-f1fe-4e03-8172-2ee4192b2cab", "e67f0ee4-a4cd-4037-b27a-53f324e96cdd", "19b34191-087e-4a5e-b1de-a73571b95799", "a8348a42-d2f2-4ad6-8b0a-6d6539e39714"], "durationId": "766f4c40-fd54-4f65-b642-0bdc67fb05b6"}}, {"head": {"id": "21549e38-8fdf-4f07-a4d1-5b57a917fba3", "name": "Configuration task cost before running: 1 s 68 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083934457625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdc87c47-dbbf-4b2d-a655-680cc4f9890a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083937583583, "endTime": 2083941599958}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "41ae7bfe-b77f-4cb9-ba26-b80d44bab9a6", "logId": "64c7518f-90c6-4fd9-8e50-5fadae3feac1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "41ae7bfe-b77f-4cb9-ba26-b80d44bab9a6", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083935436291}, "additional": {"logType": "detail", "children": [], "durationId": "cdc87c47-dbbf-4b2d-a655-680cc4f9890a"}}, {"head": {"id": "d71be9e8-6a1f-456a-baf1-0b8a81cf8afe", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083935774375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2ebb290-f6d3-4880-823b-eb9979af3ee1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083935807208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cba47fcb-f0c8-4d1f-b9a4-cc14b12c1d68", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083937601041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4b06ddc-d023-43a9-b40c-56e5072344a5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083941461666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01b01c8d-29ff-4e18-9e12-826cb0360f01", "name": "entry : default@PreBuild cost memory 0.24308013916015625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083941532083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c7518f-90c6-4fd9-8e50-5fadae3feac1", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083937583583, "endTime": 2083941599958}, "additional": {"logType": "info", "children": [], "durationId": "cdc87c47-dbbf-4b2d-a655-680cc4f9890a"}}, {"head": {"id": "9835c641-8b8b-4038-9ce1-17238860edff", "name": "upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943941750, "endTime": 2083945474375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Verification", "taskRunReasons": [], "detailId": "4dc8b3ea-abd4-47f8-ac6c-f41698915209", "logId": "d53cb692-2cc3-43e6-b2fd-fa574a7ff8be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dc8b3ea-abd4-47f8-ac6c-f41698915209", "name": "create upcloud:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083942923250}, "additional": {"logType": "detail", "children": [], "durationId": "9835c641-8b8b-4038-9ce1-17238860edff"}}, {"head": {"id": "d4a6d0fe-4380-488a-9dd4-23b3fdb3d4eb", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943075958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92b87881-2b4a-48e4-a11a-9dee2933bef0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943102875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d062ad8f-cab0-495c-9012-f8461d67780f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943204541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46cd872c-e4bb-43f6-870a-b2ab9cc3c8a4", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943226083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61981115-43ad-4ce7-919d-74a441f21ed2", "name": "Executing task :upcloud:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943946333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d94c1f4-3ea9-485e-afaf-584e0c19c391", "name": "Incremental task upcloud:default@PreBuild pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083945403541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f6adfdf-75d6-4794-96b6-fd74cec3d2cc", "name": "upcloud : default@PreBuild cost memory 0.18186187744140625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083945446583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d53cb692-2cc3-43e6-b2fd-fa574a7ff8be", "name": "UP-TO-DATE :upcloud:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083943941750, "endTime": 2083945474375}, "additional": {"logType": "info", "children": [], "durationId": "9835c641-8b8b-4038-9ce1-17238860edff"}}, {"head": {"id": "877e37d0-2369-4a62-acab-3e5d0eebebb0", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083947535458, "endTime": 2083948445125}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "49464158-c3e7-4563-afe1-ea067b5059b7", "logId": "24c470d0-12e9-48bf-84aa-fe7ec9dc41ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "49464158-c3e7-4563-afe1-ea067b5059b7", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083946899000}, "additional": {"logType": "detail", "children": [], "durationId": "877e37d0-2369-4a62-acab-3e5d0eebebb0"}}, {"head": {"id": "12e500a2-4996-4eba-aef6-1bceda281f3b", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083947090583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7771fb87-0abe-409e-954f-f1212cdeafa4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083947126750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97da213a-f4a4-4e71-87ba-f27441b21946", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083947539500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8bbe59-b8e9-4049-befc-48c8322ffc72", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083948113458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75c88a0c-3c96-4ff8-9f7a-8b8f58d2521b", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083948382083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe6faf3e-80dc-4053-9cef-70bfe99e70bc", "name": "entry : default@GenerateMetadata cost memory 0.08757781982421875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083948420708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c470d0-12e9-48bf-84aa-fe7ec9dc41ce", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083947535458, "endTime": 2083948445125}, "additional": {"logType": "info", "children": [], "durationId": "877e37d0-2369-4a62-acab-3e5d0eebebb0"}}, {"head": {"id": "cbc10fa1-f7a3-4e49-b3a0-eb82c6207daf", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949775375, "endTime": 2083950010375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "b4e8a808-914a-467d-8849-787bb1c21f08", "logId": "45d9bbb7-198b-4605-a0dc-1bfe002b3869"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4e8a808-914a-467d-8849-787bb1c21f08", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949189250}, "additional": {"logType": "detail", "children": [], "durationId": "cbc10fa1-f7a3-4e49-b3a0-eb82c6207daf"}}, {"head": {"id": "a5c2e493-b02e-4c0b-898c-84ee06691c28", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949365708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c7dec1-66f2-4c5e-9152-68a59623d769", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949388375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425b3e46-7fce-4c85-8fc8-4b46ccb8a095", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949778750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f8d538-0a0f-48ff-8300-bb72203c219f", "name": "entry : default@PreCheckSyscap cost memory 0.01329803466796875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949916208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cb25251-d5d7-412f-ad8b-0eea1eb9db6e", "name": "runTaskFromQueue task cost before running: 1 s 84 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949952208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45d9bbb7-198b-4605-a0dc-1bfe002b3869", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083949775375, "endTime": 2083950010375, "totalTime": 164041}, "additional": {"logType": "info", "children": [], "durationId": "cbc10fa1-f7a3-4e49-b3a0-eb82c6207daf"}}, {"head": {"id": "d1d71bb8-ca98-4430-b076-7f671d51bdf4", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955606166, "endTime": 2083956011041}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ec507fc2-d55c-429e-887c-d17ff413deff", "logId": "35981a6a-52a3-41c7-8b23-65b0035a8fb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ec507fc2-d55c-429e-887c-d17ff413deff", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083950878041}, "additional": {"logType": "detail", "children": [], "durationId": "d1d71bb8-ca98-4430-b076-7f671d51bdf4"}}, {"head": {"id": "ad035cc2-827e-4e48-9725-3bcdbf5fc811", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083951053583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c09a60c-f18a-4058-8d31-2bf72bee3b22", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083951075916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "698a7c76-9f89-4326-b714-0ec075901f3c", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955620291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a51397-9cc2-4fa8-a489-2cb6752908d1", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955803166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "412576cf-3d3f-4e58-beb2-6470b2c85ea5", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0566253662109375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955950375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f12de5cc-f90b-4245-b02c-7b33f6f1044c", "name": "runTaskFromQueue task cost before running: 1 s 90 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955990208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35981a6a-52a3-41c7-8b23-65b0035a8fb5", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083955606166, "endTime": 2083956011041, "totalTime": 378458}, "additional": {"logType": "info", "children": [], "durationId": "d1d71bb8-ca98-4430-b076-7f671d51bdf4"}}, {"head": {"id": "a54c65e5-c3ec-4805-84bc-4b05c87eeba9", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083959895875, "endTime": 2083961188458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "f07f11cf-5b80-42c3-bbbe-ff121d70dd44", "logId": "de14c23e-4e8d-4bf2-a5cb-d980b2758e81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f07f11cf-5b80-42c3-bbbe-ff121d70dd44", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083958782166}, "additional": {"logType": "detail", "children": [], "durationId": "a54c65e5-c3ec-4805-84bc-4b05c87eeba9"}}, {"head": {"id": "908efdf3-d029-4483-ae67-74f58e0eafa7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083959002250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f706cc59-6500-404b-ba8c-71e08f4c2c76", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083959043916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5c2a6d3-bdc8-4de4-aeb2-fada78b9cd97", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083959901416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "141aec40-8956-4316-aaeb-f98dce86d210", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083960793291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "133f910e-4a05-4a88-8237-c42f94aea4a6", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083960872583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b165ab28-dbf4-43f8-892c-f839e19a851d", "name": "entry : default@ProcessIntegratedHsp cost memory 0.08673858642578125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083961084166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd6dcce-6ebf-417c-a582-c611c2df1cdb", "name": "runTaskFromQueue task cost before running: 1 s 95 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083961161625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de14c23e-4e8d-4bf2-a5cb-d980b2758e81", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083959895875, "endTime": 2083961188458, "totalTime": 1244125}, "additional": {"logType": "info", "children": [], "durationId": "a54c65e5-c3ec-4805-84bc-4b05c87eeba9"}}, {"head": {"id": "d53775f6-5285-4a90-b965-33c5dd9dd8ab", "name": "upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964607083, "endTime": 2083965156708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Generate", "taskRunReasons": [], "detailId": "daf3bb1f-770d-42f8-9be1-7bde8420fde3", "logId": "bd006381-3b4a-480f-b7b2-4db6574229d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daf3bb1f-770d-42f8-9be1-7bde8420fde3", "name": "create upcloud:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083963807500}, "additional": {"logType": "detail", "children": [], "durationId": "d53775f6-5285-4a90-b965-33c5dd9dd8ab"}}, {"head": {"id": "e67abeae-a22f-45e5-83e5-d31e042d6405", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964102708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2041e19-c268-48fd-bd12-2485251635a0", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964135000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b52b7c-5f4c-4046-bd2f-5a2421af5a74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964151125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75851f6e-b8d4-47a7-9b89-454dbd52b085", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964171583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dead8318-ab09-479b-a1fc-a6be3eff7791", "name": "Executing task :upcloud:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964612083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bd6d27b-4137-40bd-8c7b-ad14017180bc", "name": "Task 'upcloud:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964826541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ca345e0-2379-45b9-8be8-30cb50996e8d", "name": "Incremental task upcloud:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083965098291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dbf0d6e-b21a-4a50-b929-4ebe17ce4a73", "name": "upcloud : default@CreateHarBuildProfile cost memory 0.07975006103515625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083965130833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd006381-3b4a-480f-b7b2-4db6574229d9", "name": "UP-TO-DATE :upcloud:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083964607083, "endTime": 2083965156708}, "additional": {"logType": "info", "children": [], "durationId": "d53775f6-5285-4a90-b965-33c5dd9dd8ab"}}, {"head": {"id": "74e70119-45ff-48e5-b36e-ffd2fc60a4b6", "name": "upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083967690208, "endTime": 2083967797041}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "a66da8fb-560a-4592-9e0f-95f12f0b6140", "logId": "10987724-73eb-4be9-ba2c-822f2b3c19fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a66da8fb-560a-4592-9e0f-95f12f0b6140", "name": "create upcloud:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083965971708}, "additional": {"logType": "detail", "children": [], "durationId": "74e70119-45ff-48e5-b36e-ffd2fc60a4b6"}}, {"head": {"id": "ae672b51-0d90-4514-9c15-8562a499209e", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083966233500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13db68f9-92b4-4103-a158-2d2e0946f005", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083966258000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef6bc80-f7ca-483d-b56d-726716eb0451", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083966274000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b914f6-0cd4-4195-bcb3-2f26926ae4e1", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083966292958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c42f39-f8b7-4956-af34-782bf0d84920", "name": "Executing task :upcloud:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083967693958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a14a99a6-3bd1-4856-927b-8361f261b813", "name": "upcloud : default@ConfigureCmake cost memory 0.01438140869140625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083967747000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fe30155-fb6a-4e52-a205-97a13b98cad3", "name": "runTaskFromQueue task cost before running: 1 s 102 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083967779166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10987724-73eb-4be9-ba2c-822f2b3c19fe", "name": "Finished :upcloud:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083967690208, "endTime": 2083967797041, "totalTime": 80333}, "additional": {"logType": "info", "children": [], "durationId": "74e70119-45ff-48e5-b36e-ffd2fc60a4b6"}}, {"head": {"id": "def73fff-ac5e-4d06-a201-0798a02533c2", "name": "upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083970643000, "endTime": 2083971380666}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Config", "taskRunReasons": [], "detailId": "831dbfda-7d1a-4445-b955-02385d992b72", "logId": "e7228cc0-6fad-4648-a60f-42d95df59d54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "831dbfda-7d1a-4445-b955-02385d992b72", "name": "create upcloud:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083968713041}, "additional": {"logType": "detail", "children": [], "durationId": "def73fff-ac5e-4d06-a201-0798a02533c2"}}, {"head": {"id": "d1f81380-6a23-4a77-b866-fc27efd86216", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083968911041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7599797d-eb99-4832-b897-19babdad2f28", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083968938875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76241fcd-0682-4994-86a1-87d9977bd5e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083968956583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3165ff5-763f-4c13-b495-e6ce8c5ea058", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083968972875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405ecf98-49f6-49d3-8900-4176bc1c697b", "name": "Executing task :upcloud:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083970648416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fcc9d4e-23f1-40c0-b88b-d6446165529f", "name": "Incremental task upcloud:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083971319250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8ec04c-058c-4a7f-9999-53bf4ce0079d", "name": "upcloud : default@MergeProfile cost memory 0.11067962646484375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083971353916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7228cc0-6fad-4648-a60f-42d95df59d54", "name": "UP-TO-DATE :upcloud:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083970643000, "endTime": 2083971380666}, "additional": {"logType": "info", "children": [], "durationId": "def73fff-ac5e-4d06-a201-0798a02533c2"}}, {"head": {"id": "b24c2fd7-d23d-43b8-960d-4b65c6bfe688", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972970375, "endTime": 2083981134375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "15350dd0-90e6-4a6b-9e2d-1c60953a888b", "logId": "3695d4d4-d146-46c4-a622-dea5fb139df4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15350dd0-90e6-4a6b-9e2d-1c60953a888b", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972196875}, "additional": {"logType": "detail", "children": [], "durationId": "b24c2fd7-d23d-43b8-960d-4b65c6bfe688"}}, {"head": {"id": "0ba8e389-fa03-42e3-8a34-23d027a1f875", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972414083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80be3bf6-51f3-41f6-bc99-9fcb82ea988a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972449416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d01c287-d080-4a92-b366-303a59b8fd5b", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972977791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb7e2ccc-8069-4aff-a62e-64c7ce387f77", "name": "File: '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083973025125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db97049e-712c-4cce-8839-2545b07668d6", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083973206208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e680887a-a86d-4314-90b4-bf2b5317e742", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083980837291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5b727c-6685-454c-ba45-d2810804e994", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083980917916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f0c503-5073-4736-b408-4489411ec4d8", "name": "entry : default@SyscapTransform cost memory 0.11000823974609375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083981063666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b114b4b6-e6cc-4d3e-945d-be49b9aa406f", "name": "runTaskFromQueue task cost before running: 1 s 115 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083981104166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3695d4d4-d146-46c4-a622-dea5fb139df4", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083972970375, "endTime": 2083981134375, "totalTime": 8121125}, "additional": {"logType": "info", "children": [], "durationId": "b24c2fd7-d23d-43b8-960d-4b65c6bfe688"}}, {"head": {"id": "c0ad04dd-98f4-41ed-bb40-a66ed29d8875", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083983528916, "endTime": 2083985330625}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b6a57bf3-76b1-4a65-a4e8-182aa8ef6add", "logId": "5d598424-5940-4b43-b1aa-583ad6c12064"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6a57bf3-76b1-4a65-a4e8-182aa8ef6add", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083982204291}, "additional": {"logType": "detail", "children": [], "durationId": "c0ad04dd-98f4-41ed-bb40-a66ed29d8875"}}, {"head": {"id": "1f3e362c-f57e-452a-b772-0b1a8aaec890", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083982417083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18f690b2-e702-4ab3-b28d-32692a73bea1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083982453541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61e92dd1-1fce-4345-90c6-290b523e9b7c", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083983534208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2813203-04d7-43bc-8fc9-228630ad27ea", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083985215583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6da5c64b-bf4c-462e-b044-2c0d1d254ea8", "name": "entry : default@ProcessRouterMap cost memory 0.19628143310546875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083985295750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d598424-5940-4b43-b1aa-583ad6c12064", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083983528916, "endTime": 2083985330625}, "additional": {"logType": "info", "children": [], "durationId": "c0ad04dd-98f4-41ed-bb40-a66ed29d8875"}}, {"head": {"id": "445bd11a-f795-4188-bc3c-5c5035026f2f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987147750, "endTime": 2083987847166}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "eb4f2494-39f9-42ee-8846-2cf194e156fb", "logId": "f38a5f38-11a0-4eea-a7ce-e3037c119e10"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eb4f2494-39f9-42ee-8846-2cf194e156fb", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083986548666}, "additional": {"logType": "detail", "children": [], "durationId": "445bd11a-f795-4188-bc3c-5c5035026f2f"}}, {"head": {"id": "92c20adb-4ee2-412a-85fe-8eec41b71215", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083986741500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f12c50c8-300a-46fc-af19-7b85d43bc811", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083986767666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a16a857f-94f3-417d-9ae9-185f6c06a78c", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987152375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bfe1a86-94c6-40de-8eb3-e84e15b96575", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987463541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bcc2b47-d581-43dd-9f2e-e1932687a70a", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987781000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36919623-d00c-4e93-aaa0-6b21dcfffa9a", "name": "entry : default@CreateBuildProfile cost memory 0.09326934814453125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987814833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f38a5f38-11a0-4eea-a7ce-e3037c119e10", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083987147750, "endTime": 2083987847166}, "additional": {"logType": "info", "children": [], "durationId": "445bd11a-f795-4188-bc3c-5c5035026f2f"}}, {"head": {"id": "0275ddc3-0650-4d9e-ab2e-cd353dbfed98", "name": "upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083989257916, "endTime": 2083989432375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "a344962b-d7e3-4646-87fc-b4be591dc009", "logId": "eaf5d8bd-75d5-409a-aba1-bca54cd35e88"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a344962b-d7e3-4646-87fc-b4be591dc009", "name": "create upcloud:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083988548083}, "additional": {"logType": "detail", "children": [], "durationId": "0275ddc3-0650-4d9e-ab2e-cd353dbfed98"}}, {"head": {"id": "5c6a7aaa-4077-425b-9f6c-8f9f84d5cd2c", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083988798541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cc354ac-e750-4e79-a5f1-a441d42de2ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083988835708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653f270a-e37b-433a-877c-604aefa549d9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083988854083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48de8500-808b-47a7-a8d9-76ac7679ed01", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083988871541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e972076-548a-4485-a5e3-6203f0425df8", "name": "Executing task :upcloud:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083989263958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09494611-4018-46fd-8e20-23b393d48721", "name": "upcloud : default@BuildNativeWithCmake cost memory 0.01433563232421875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083989334291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c4dc8a2-5eab-4ed1-8bc9-5d074ebcbeed", "name": "runTaskFromQueue task cost before running: 1 s 123 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083989404125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf5d8bd-75d5-409a-aba1-bca54cd35e88", "name": "Finished :upcloud:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083989257916, "endTime": 2083989432375, "totalTime": 103875}, "additional": {"logType": "info", "children": [], "durationId": "0275ddc3-0650-4d9e-ab2e-cd353dbfed98"}}, {"head": {"id": "b5b87d99-9346-45ea-bdb1-1ebecfba83ec", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083992120291, "endTime": 2083992682416}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "70a09368-d495-4749-bb15-866ed6d1b6fb", "logId": "a3670a0c-4ab6-4af8-9cd0-58b5c28eaac3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70a09368-d495-4749-bb15-866ed6d1b6fb", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083990176916}, "additional": {"logType": "detail", "children": [], "durationId": "b5b87d99-9346-45ea-bdb1-1ebecfba83ec"}}, {"head": {"id": "ba73c7b5-3133-4f3d-b47a-ff418afbf9e7", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083990444375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5454338f-4727-4367-b3a7-b18e971e7658", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083990467958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e605075a-7444-4f45-b074-d343b02cf7cc", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083992126833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14fda1f-3c28-4fcd-a910-db260ad2c554", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083992623833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b10309-c9c6-40e5-8b0f-b21b5bf68b7a", "name": "entry : default@MergeProfile cost memory 0.124847412109375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083992658416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3670a0c-4ab6-4af8-9cd0-58b5c28eaac3", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083992120291, "endTime": 2083992682416}, "additional": {"logType": "info", "children": [], "durationId": "b5b87d99-9346-45ea-bdb1-1ebecfba83ec"}}, {"head": {"id": "529bb5f4-d698-40cd-ae76-6910156dc971", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083997842375, "endTime": 2084007549750}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dba64aa6-afce-4322-bf0a-4c84847a1888", "logId": "dfd37d6b-3a9c-48a7-9930-f9270519f109"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dba64aa6-afce-4322-bf0a-4c84847a1888", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083993613583}, "additional": {"logType": "detail", "children": [], "durationId": "529bb5f4-d698-40cd-ae76-6910156dc971"}}, {"head": {"id": "73fd5b13-64f7-4ed5-a047-507b58badfbe", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083993806291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbe2bdd9-45d3-43d1-ac1f-83879ae7b423", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083993858750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "638e9985-9cce-40bb-bc95-e039d2b1c294", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083994252958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b84c8963-940c-4c49-b5a3-a3148b84c70e", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083997849625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3218147f-8d8f-48cf-9a7f-c7e5d2be34b6", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083998691375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3780111f-7b00-4041-9225-882f9a5e4800", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084001258916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bd1739-e6f9-4a09-bdb2-75dcc37a5cf3", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084003333458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b12cf5d-ea4b-44c5-a3e7-e12bba878e7b", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084004543416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28c589b4-efe5-4e99-93ad-fd2e2824bbb2", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084006404041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0edda6a-1c7d-4645-8407-6cb785821a7a", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084007455916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8ea8bfc-cd9b-435d-ad0e-7894c9d1fe32", "name": "entry : default@GenerateLoaderJson cost memory 0.835235595703125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084007516250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfd37d6b-3a9c-48a7-9930-f9270519f109", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2083997842375, "endTime": 2084007549750}, "additional": {"logType": "info", "children": [], "durationId": "529bb5f4-d698-40cd-ae76-6910156dc971"}}, {"head": {"id": "42b9b151-6188-4d24-8b09-9b2996fa7065", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084008340208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7674261e-18f6-4814-a2ae-8f3bac0fd054", "name": "Module 'upcloud' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084009440875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8417bf27-5f3e-4fac-a877-73b7e36e883f", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084011534291, "endTime": 2084011660791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0e0d6ef9-69e1-4b4b-934b-79a6a870d87d", "logId": "5065585d-5cc0-4e24-8c5c-cbf889029233"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e0d6ef9-69e1-4b4b-934b-79a6a870d87d", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084010733750}, "additional": {"logType": "detail", "children": [], "durationId": "8417bf27-5f3e-4fac-a877-73b7e36e883f"}}, {"head": {"id": "ca3af2a7-4aa9-4075-9b31-0727cd506cbc", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084010925875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65a67208-8437-49e8-b3da-49466b11e3fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084010953208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e4e2666-df05-41e6-9699-6d54c90458a0", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084011538791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d387e0-dac4-490f-a86f-eae16752d081", "name": "entry : default@ConfigureCmake cost memory 0.013275146484375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084011602000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53b76fdf-4cbb-462b-9c3c-e05f1ac62c2e", "name": "runTaskFromQueue task cost before running: 1 s 146 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084011640958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5065585d-5cc0-4e24-8c5c-cbf889029233", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084011534291, "endTime": 2084011660791, "totalTime": 92875}, "additional": {"logType": "info", "children": [], "durationId": "8417bf27-5f3e-4fac-a877-73b7e36e883f"}}, {"head": {"id": "50e97440-8560-4d25-9a43-2f857e020cb2", "name": "upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012976166, "endTime": 2084013452708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "5325b440-044d-4a0b-8e22-1d9db6a217f0", "logId": "8c762822-9250-4e97-b2fb-37ca3cf8b2f1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5325b440-044d-4a0b-8e22-1d9db6a217f0", "name": "create upcloud:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012366500}, "additional": {"logType": "detail", "children": [], "durationId": "50e97440-8560-4d25-9a43-2f857e020cb2"}}, {"head": {"id": "6a7d262d-a7a9-4c6e-97c9-3c3eda5a72d4", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012569250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e3862d6-4132-42ae-960e-a20601fbbe13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012601958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19bc150a-af32-4a40-9e18-f125caa89162", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012618083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "155c7b16-59ab-4f69-a922-01dee2280ba7", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012634833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5142df72-8ad6-4a93-9b99-af6f42a6b308", "name": "Executing task :upcloud:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012983500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22dd83dc-359b-4986-bbf0-53fd7d2580b6", "name": "upcloud : default@BuildNativeWithNinja cost memory 0.02973175048828125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084013396208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e844e288-fb90-4036-982f-853ef0bef265", "name": "runTaskFromQueue task cost before running: 1 s 147 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084013434291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c762822-9250-4e97-b2fb-37ca3cf8b2f1", "name": "Finished :upcloud:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084012976166, "endTime": 2084013452708, "totalTime": 447708}, "additional": {"logType": "info", "children": [], "durationId": "50e97440-8560-4d25-9a43-2f857e020cb2"}}, {"head": {"id": "d2a8e6d1-50ed-4fac-a4db-d9150c83e8b9", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014727208, "endTime": 2084016575791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "98687d9a-2dc9-43a5-a856-b91762455597", "logId": "51c746c8-1ccf-4720-9050-1ed1818269d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98687d9a-2dc9-43a5-a856-b91762455597", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014183458}, "additional": {"logType": "detail", "children": [], "durationId": "d2a8e6d1-50ed-4fac-a4db-d9150c83e8b9"}}, {"head": {"id": "74895dd1-10ed-4278-b814-be5357444a5f", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014368333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbc807b2-f59f-4489-b1db-5e7439f067dd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014392500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35bb63a-a3c2-4d5d-a34a-6386f8492585", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014731166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47eae1eb-0427-4d49-94e8-41dc826b2cbc", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084016495583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f0f60b4-992c-491a-be43-62f160230134", "name": "entry : default@MakePackInfo cost memory 0.1364898681640625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084016544583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51c746c8-1ccf-4720-9050-1ed1818269d2", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084014727208, "endTime": 2084016575791}, "additional": {"logType": "info", "children": [], "durationId": "d2a8e6d1-50ed-4fac-a4db-d9150c83e8b9"}}, {"head": {"id": "f9a3e615-d7ac-43be-ac83-4d383745fc10", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084018748166, "endTime": 2084019083625}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "87e2d9df-be2e-4bd8-9027-d53b4bf1fc07", "logId": "b3d428e6-3c8b-42b5-bc54-21946cadbe86"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87e2d9df-be2e-4bd8-9027-d53b4bf1fc07", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084017844791}, "additional": {"logType": "detail", "children": [], "durationId": "f9a3e615-d7ac-43be-ac83-4d383745fc10"}}, {"head": {"id": "69442651-be03-4634-b333-60565c3ad51e", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084018038916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95bb5f5f-2d2c-4845-9914-44573a34db01", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084018067541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0974114c-bdca-492c-938c-352fdd86e28a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084018768500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c1a65a4-cf49-48fa-a1ee-ea61c37c0922", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084019013000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b68d8a9-d0f6-40bf-af2c-782304cc8077", "name": "entry : default@ProcessProfile cost memory 0.0518341064453125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084019047125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d428e6-3c8b-42b5-bc54-21946cadbe86", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084018748166, "endTime": 2084019083625}, "additional": {"logType": "info", "children": [], "durationId": "f9a3e615-d7ac-43be-ac83-4d383745fc10"}}, {"head": {"id": "c7a475a2-fdaa-4cb4-abb5-a47e63b3a7bc", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084020159125, "endTime": 2084020261875}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "816238d7-fbd4-4050-8dc6-780b57abf9fa", "logId": "ab2a21ad-21d3-47ee-a347-f98dfff03fb3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "816238d7-fbd4-4050-8dc6-780b57abf9fa", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084019669625}, "additional": {"logType": "detail", "children": [], "durationId": "c7a475a2-fdaa-4cb4-abb5-a47e63b3a7bc"}}, {"head": {"id": "80e9c3e9-cd54-4511-81f3-e9476f4fd942", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084019828125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cac7f2a-eb70-4656-bf4c-e88070f04fab", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084019868250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0993efcf-fe5e-488b-96a0-74e3c2447538", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084020161625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65e62115-8424-4206-8137-820ec7154888", "name": "entry : default@BuildNativeWithCmake cost memory 0.01406097412109375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084020203458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df56d90e-5881-4d2f-b06d-da2263674a9e", "name": "runTaskFromQueue task cost before running: 1 s 154 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084020241875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2a21ad-21d3-47ee-a347-f98dfff03fb3", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084020159125, "endTime": 2084020261875, "totalTime": 66417}, "additional": {"logType": "info", "children": [], "durationId": "c7a475a2-fdaa-4cb4-abb5-a47e63b3a7bc"}}, {"head": {"id": "3e376697-73d4-4c38-b475-6fb80d89bd7f", "name": "upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021661083, "endTime": 2084023076208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Resources", "taskRunReasons": [], "detailId": "1aae0400-2e00-471d-a48a-c81cd15aa937", "logId": "d4785790-4352-43a0-953e-02c770f7d583"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1aae0400-2e00-471d-a48a-c81cd15aa937", "name": "create upcloud:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021114583}, "additional": {"logType": "detail", "children": [], "durationId": "3e376697-73d4-4c38-b475-6fb80d89bd7f"}}, {"head": {"id": "dc0dfb98-80c1-49ba-b75b-56d13e6a5fd2", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021308333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a65779c7-47e0-41be-a39e-c5c11fd9d078", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021342500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f3de19d-dafd-4484-aa80-d09fcb2c2236", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021358708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da4f6d18-8167-46fc-ad55-4e388a1efa64", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021375166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31dd556a-59e1-4e81-983a-9b6425c00748", "name": "Executing task :upcloud:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021664375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d724612-76a2-43bd-9d98-88c0f72096d4", "name": "Incremental task upcloud:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084023018458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06256075-5bf6-4d22-8628-85a2dbe51549", "name": "upcloud : default@ProcessLibs cost memory 0.108001708984375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084023051541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4785790-4352-43a0-953e-02c770f7d583", "name": "UP-TO-DATE :upcloud:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084021661083, "endTime": 2084023076208}, "additional": {"logType": "info", "children": [], "durationId": "3e376697-73d4-4c38-b475-6fb80d89bd7f"}}, {"head": {"id": "f54cc25c-6966-428e-9997-e8c7ed9a5e68", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084026333541, "endTime": 2084028361166}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4409d44e-4d15-45af-8ec3-01b326d1c65d", "logId": "c510aaab-6a25-4402-b715-8824880aae33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4409d44e-4d15-45af-8ec3-01b326d1c65d", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084023833333}, "additional": {"logType": "detail", "children": [], "durationId": "f54cc25c-6966-428e-9997-e8c7ed9a5e68"}}, {"head": {"id": "ddeca251-32d0-44d3-bb77-de2f5da82680", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084024023166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a2d837c-be33-4f1b-96dc-7d3e3996ae1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084024051625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c14f37-4851-43e7-9297-4421c046e80b", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084024769333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0c7faa4-a9f2-499f-9657-457953de0063", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084027404625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba5f4c12-4c3b-4937-b73a-6c0e01337b09", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084027660916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a903b051-1d8c-4bfb-b595-a66320189fa9", "name": "entry : default@ProcessResource cost memory 0.10956573486328125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084027798583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c510aaab-6a25-4402-b715-8824880aae33", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084026333541, "endTime": 2084028361166}, "additional": {"logType": "info", "children": [], "durationId": "f54cc25c-6966-428e-9997-e8c7ed9a5e68"}}, {"head": {"id": "12516675-97cc-42ac-ad27-841c6fa7ff3f", "name": "upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084030862458, "endTime": 2084031511708}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "upcloud", "category": "Native", "taskRunReasons": [], "detailId": "2a213ae0-bcb3-41b6-9368-37e6ff291d54", "logId": "f20fb4d4-d3cb-4a68-949e-57ac82a10de1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a213ae0-bcb3-41b6-9368-37e6ff291d54", "name": "create upcloud:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084029496000}, "additional": {"logType": "detail", "children": [], "durationId": "12516675-97cc-42ac-ad27-841c6fa7ff3f"}}, {"head": {"id": "840454fe-e04a-4acb-a758-e6de28f14000", "name": "jsonObjWithoutParam {\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084029672666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56e88366-4277-4bf1-8a90-a76f1e685108", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084029710750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e9ed26-15be-4afc-9cd5-f3130a138006", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084029728416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec16431a-9b0d-412a-bca2-c770ee2f8b73", "name": "jsonObjWithoutParam {\"name\":\"@uplus/upcloud\",\"version\":\"0.1.0\",\"description\":\"基础网络请求库\",\"main\":\"Index.ets\",\"author\":\"haier\",\"license\":\"Apache-2.0\",\"dependencies\":{\"@ohos/httpclient\":\"^2.0.1-rc.7\",\"class-transformer\":\"^0.5.1\",\"reflect-metadata\":\"^0.1.13\",\"@yunkss/eftool\":\"1.2.3\",\"@ohos/axios\":\"2.2.0\"},\"devDependencies\":{},\"dynamicDependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084029748583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a599c7d-a62e-4bce-8b91-81d401b8e924", "name": "Executing task :upcloud:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084030865708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b52914c1-c6e5-4a0f-9041-4b2611894725", "name": "Task 'upcloud:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084030924291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68023c6-d78c-4566-bc6c-e41a553ef256", "name": "Incremental task upcloud:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084031448833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "122d7b14-434c-48f6-b57a-e211b0783517", "name": "upcloud : default@DoNativeStrip cost memory 0.06591796875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084031486083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f20fb4d4-d3cb-4a68-949e-57ac82a10de1", "name": "UP-TO-DATE :upcloud:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084030862458, "endTime": 2084031511708}, "additional": {"logType": "info", "children": [], "durationId": "12516675-97cc-42ac-ad27-841c6fa7ff3f"}}, {"head": {"id": "338d6db9-050e-40f1-8fac-2c15bf95adf7", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084034836750, "endTime": 2084050916208}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "2001e4dd-480a-4f24-b8ca-98e87a40ae0f", "logId": "0a175623-eab0-4472-a334-49053e903252"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2001e4dd-480a-4f24-b8ca-98e87a40ae0f", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084032426750}, "additional": {"logType": "detail", "children": [], "durationId": "338d6db9-050e-40f1-8fac-2c15bf95adf7"}}, {"head": {"id": "b32f8f7a-f4fa-401e-9e4c-234f35d06bbb", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084032590125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d538cfb3-13a1-45b1-a20d-44b4248e5b78", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084032617833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef35235b-fffb-4529-9ddc-9e9553750bc2", "name": "restool module names: entry,upcloud; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084032916375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e967354-b307-4160-85f8-10912944a1e7", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084034878833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3042ab8c-f755-421d-869f-f7204a34d0bb", "name": "Incremental task entry:default@CompileResource pre-execution cost: 16 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084050770375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d163b1-2caa-4259-b928-bfc08b0aa0d2", "name": "entry : default@CompileResource cost memory 0.9869537353515625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084050868666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a175623-eab0-4472-a334-49053e903252", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084034836750, "endTime": 2084050916208}, "additional": {"logType": "info", "children": [], "durationId": "338d6db9-050e-40f1-8fac-2c15bf95adf7"}}, {"head": {"id": "5404dca3-6288-481d-a0c9-f93a01a693d9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052586666, "endTime": 2084053044500}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "931bcfbe-b47b-4d39-b43d-36e1820b1d55", "logId": "c6ab2f3c-0bf4-48c8-bac9-23a538014366"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "931bcfbe-b47b-4d39-b43d-36e1820b1d55", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052110083}, "additional": {"logType": "detail", "children": [], "durationId": "5404dca3-6288-481d-a0c9-f93a01a693d9"}}, {"head": {"id": "2289d7cb-ba03-48b2-b83c-754c19dde8cf", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052285583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d81b16b7-dfe3-4936-9f2a-e4e38d499084", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052313125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2743cfed-8530-4a91-97c0-c55babf02488", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052589833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2a7dcf-f3a0-4a23-be15-6798f750eeec", "name": "entry : default@BuildNativeWithNinja cost memory 0.029541015625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052971750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d2e59a1-30e3-46f3-9208-3c04988743c6", "name": "runTaskFromQueue task cost before running: 1 s 187 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084053008833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6ab2f3c-0bf4-48c8-bac9-23a538014366", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084052586666, "endTime": 2084053044500, "totalTime": 411208}, "additional": {"logType": "info", "children": [], "durationId": "5404dca3-6288-481d-a0c9-f93a01a693d9"}}, {"head": {"id": "f5ecaf27-d355-4dc2-81d0-8942c85b52d8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084056040958, "endTime": 2084087581333}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "82c96a2e-e43c-4e04-a375-b5c0c28519ad", "logId": "53a5aafd-90ed-4e58-acfd-da26806cf409"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82c96a2e-e43c-4e04-a375-b5c0c28519ad", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084053838125}, "additional": {"logType": "detail", "children": [], "durationId": "f5ecaf27-d355-4dc2-81d0-8942c85b52d8"}}, {"head": {"id": "0b093035-118d-4041-964f-af173ee3d892", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084053996000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca5d41a7-72c4-4e3c-befe-5bb6ed941222", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084054022500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daf0cbba-e6eb-4d17-b4b5-2ba29842b8de", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084056047083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf357ee-cc54-4a13-be0c-463442155ed9", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 28 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084087445375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e45223d3-6e71-4813-8d74-b534a48398f9", "name": "entry : default@CompileArkTS cost memory -7.943397521972656", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084087547583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a5aafd-90ed-4e58-acfd-da26806cf409", "name": "UP-TO-DATE :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084056040958, "endTime": 2084087581333}, "additional": {"logType": "info", "children": [], "durationId": "f5ecaf27-d355-4dc2-81d0-8942c85b52d8"}}, {"head": {"id": "aeabc6a8-868e-407c-a717-7d7f609cf6e7", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084091634541, "endTime": 2084093161750}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "ce8f701b-b560-4675-95d9-5ea63af01f42", "logId": "df1e7803-d1d8-4686-ad34-8610deb35862"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce8f701b-b560-4675-95d9-5ea63af01f42", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084089774791}, "additional": {"logType": "detail", "children": [], "durationId": "aeabc6a8-868e-407c-a717-7d7f609cf6e7"}}, {"head": {"id": "e62dbbb1-6c17-4d8d-8394-428cae33e511", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084089971458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9938c4d-b8fc-4d84-b507-344c443ebd59", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084090008000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37079d87-15ae-4a6c-ad5d-9ad2f9bbbc68", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084091638958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc6e416e-6d3e-40a3-a98b-bf55d21ff944", "name": "entry : default@BuildJS cost memory 0.1129913330078125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084093053208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61c899c2-d9de-44f8-8c58-45c93c607544", "name": "runTaskFromQueue task cost before running: 1 s 227 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084093135583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df1e7803-d1d8-4686-ad34-8610deb35862", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084091634541, "endTime": 2084093161750, "totalTime": 1486250}, "additional": {"logType": "info", "children": [], "durationId": "aeabc6a8-868e-407c-a717-7d7f609cf6e7"}}, {"head": {"id": "3a365ba3-d2fe-4d82-86fe-a3b5c33e3e7b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094614458, "endTime": 2084099568250}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "50f7c209-ebd2-4d98-95f1-663518fb21aa", "logId": "7b885137-169e-4a29-9011-61eea960eb6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "50f7c209-ebd2-4d98-95f1-663518fb21aa", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094027500}, "additional": {"logType": "detail", "children": [], "durationId": "3a365ba3-d2fe-4d82-86fe-a3b5c33e3e7b"}}, {"head": {"id": "1f540cac-c7f6-4c6d-ada0-42a69a1074b9", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094185291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3d47282-bad8-4d75-8397-fe59727e206c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094223500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443a6481-46cb-4020-886f-505087fdd150", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094618666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1407e4db-1926-4f7f-9e2b-6f98d5e6e84d", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084099470833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36144616-7f0d-4e5c-9ee5-98bdccfee200", "name": "entry : default@ProcessLibs cost memory 0.7425079345703125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084099535875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b885137-169e-4a29-9011-61eea960eb6b", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084094614458, "endTime": 2084099568250}, "additional": {"logType": "info", "children": [], "durationId": "3a365ba3-d2fe-4d82-86fe-a3b5c33e3e7b"}}, {"head": {"id": "46c71e20-8a34-4348-a8c4-bff3a80febd3", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084101541291, "endTime": 2084102094416}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b4faced4-deee-40da-a087-ad7e24a83342", "logId": "aee944a7-a06f-4fe7-8ae9-b56f93699f98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b4faced4-deee-40da-a087-ad7e24a83342", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084100262500}, "additional": {"logType": "detail", "children": [], "durationId": "46c71e20-8a34-4348-a8c4-bff3a80febd3"}}, {"head": {"id": "59c7bb5c-2d29-4b85-96aa-869e17316da5", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084100425583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96966749-764e-4cce-9f61-5f4fe0ebfec7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084100452458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e3d97a5-122b-4f66-bc10-fa03ce38cb56", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084101544541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94a77858-d20d-4ad4-ae53-33c8a27b7593", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084101607458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76300698-15d0-4a77-9a8d-eef4190cf209", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084102032458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd93396-0218-4d62-ace8-5542be5538df", "name": "entry : default@DoNativeStrip cost memory 0.06325531005859375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084102070458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aee944a7-a06f-4fe7-8ae9-b56f93699f98", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084101541291, "endTime": 2084102094416}, "additional": {"logType": "info", "children": [], "durationId": "46c71e20-8a34-4348-a8c4-bff3a80febd3"}}, {"head": {"id": "96bdd5e9-eae2-4131-a6d3-4a529f7fa698", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084104500166, "endTime": 2084105306041}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "0f2bc0d2-be8c-4f70-9422-8539132e3137", "logId": "51af1676-4f03-4317-9722-d1a389ce5c84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f2bc0d2-be8c-4f70-9422-8539132e3137", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084102888083}, "additional": {"logType": "detail", "children": [], "durationId": "96bdd5e9-eae2-4131-a6d3-4a529f7fa698"}}, {"head": {"id": "65d64e6f-5a89-4bf7-9f82-68b6e3ed3fa2", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084103116875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4013f0-f010-45e4-bcde-0880007407da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084103142458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "274576f9-239a-4e31-b0a7-bf7986c6d018", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084104508000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be962d32-8e97-453e-a490-47383bffcf78", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084104631666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cb823d2-0f1b-4e52-9de0-9cec886a139c", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084105226000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "475a2792-0730-4267-a2a6-1165de507b6a", "name": "entry : default@CacheNativeLibs cost memory 0.07163238525390625", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084105276041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51af1676-4f03-4317-9722-d1a389ce5c84", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084104500166, "endTime": 2084105306041}, "additional": {"logType": "info", "children": [], "durationId": "96bdd5e9-eae2-4131-a6d3-4a529f7fa698"}}, {"head": {"id": "695d55c6-63ad-4b7a-91e5-4206609f12ef", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106778416, "endTime": 2084107170791}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "f4d9a0ee-32a9-440d-9765-f4dc6d5c2049", "logId": "b39423f7-a2a7-490c-8a9d-28213b83de93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4d9a0ee-32a9-440d-9765-f4dc6d5c2049", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106095125}, "additional": {"logType": "detail", "children": [], "durationId": "695d55c6-63ad-4b7a-91e5-4206609f12ef"}}, {"head": {"id": "27de231f-a50c-4c21-98c6-03ad797ddcb0", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106276666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6af8048b-e8e4-480a-8d2e-0bdac10b8a18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106316083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9ce5bf-19c5-44c4-85f0-13f952360bb6", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106783041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af11028-f495-4716-a37d-73b9f16c63cc", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106936750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6140135-46ae-40f5-866a-41a921350675", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084107114541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8921b754-41df-47a6-aae4-58e81fd83de3", "name": "entry : default@GeneratePkgModuleJson cost memory 0.06365966796875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084107147041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b39423f7-a2a7-490c-8a9d-28213b83de93", "name": "UP-TO-DATE :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084106778416, "endTime": 2084107170791}, "additional": {"logType": "info", "children": [], "durationId": "695d55c6-63ad-4b7a-91e5-4206609f12ef"}}, {"head": {"id": "a7a14f39-d1d4-43dd-8f76-3f943a21b269", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084111683333, "endTime": 2084116660458}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": [], "detailId": "c6f79f14-19b4-4e9e-9c73-faedad9f15a0", "logId": "368f71d8-5134-475f-bee6-10406f055140"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c6f79f14-19b4-4e9e-9c73-faedad9f15a0", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084108144666}, "additional": {"logType": "detail", "children": [], "durationId": "a7a14f39-d1d4-43dd-8f76-3f943a21b269"}}, {"head": {"id": "55071860-0d69-455d-b70b-3fc80b8ec365", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084108300375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97371c81-9fe7-4b49-8816-a02104a4dabf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084108322916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f130c38-e435-4ab9-9fed-f9e53f3a3136", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084111689000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01e39a8e-852b-40b3-9862-d5425bfb6374", "name": "Incremental task entry:default@PackageHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084116548375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cfc1c04-2050-4ed5-bbe3-6694d34a1f61", "name": "entry : default@PackageHap cost memory 0.371826171875", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084116625750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "368f71d8-5134-475f-bee6-10406f055140", "name": "UP-TO-DATE :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084111683333, "endTime": 2084116660458}, "additional": {"logType": "info", "children": [], "durationId": "a7a14f39-d1d4-43dd-8f76-3f943a21b269"}}, {"head": {"id": "d693e475-c849-4041-b36c-f688bac7fc82", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120105666, "endTime": 2084121138666}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap' does not exist."], "detailId": "0e49e64d-54e1-4291-a315-00970dd3003d", "logId": "6052e83c-62fc-4f4b-be0d-def9d93dc4e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e49e64d-54e1-4291-a315-00970dd3003d", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084118357583}, "additional": {"logType": "detail", "children": [], "durationId": "d693e475-c849-4041-b36c-f688bac7fc82"}}, {"head": {"id": "b03adeb2-f5eb-429f-afe2-f38d0f73c1ee", "name": "jsonObjWithoutParam {\"upcloud\":\"../upcloud\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084118595791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a799e5d-f01a-4e9f-a8c6-9c0831c64a20", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"upcloud\":\"../upcloud\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084118647625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1adb5655-5c3f-444f-9d58-cd8a7f742de0", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120115666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc9f0c8-dffb-4173-8f98-53dd1b998a09", "name": "Will skip sign 'hos_hap'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in /Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120377583}, "additional": {"logType": "warn", "children": [], "durationId": "d693e475-c849-4041-b36c-f688bac7fc82"}}, {"head": {"id": "23a6717a-e84c-459a-9359-29f37bbbd3a7", "name": "entry:default@SignHap is not up-to-date, since the output file '/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap' does not exist.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120533791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b6fc582-e278-463d-bfad-8c17e107ef0e", "name": "Incremental task entry:default@SignHap pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120580708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ba982c4-5a3d-43a5-819f-f85a405007b5", "name": "entry : default@SignHap cost memory 0.24915313720703125", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084121052833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0a8fb70-b453-45ed-a0b2-3d862aedbdb5", "name": "runTaskFromQueue task cost before running: 1 s 255 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084121112666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6052e83c-62fc-4f4b-be0d-def9d93dc4e0", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084120105666, "endTime": 2084121138666, "totalTime": 990250}, "additional": {"logType": "info", "children": [], "durationId": "d693e475-c849-4041-b36c-f688bac7fc82"}}, {"head": {"id": "7bda4d4e-4f08-4cd1-8cec-27f2b15f3ee2", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122145833, "endTime": 2084122276708}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "a8ff89cc-c505-4e96-a262-6d2a8cd4cbe9", "logId": "56baa338-127b-4174-a140-f96053dfe5c4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a8ff89cc-c505-4e96-a262-6d2a8cd4cbe9", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122117958}, "additional": {"logType": "detail", "children": [], "durationId": "7bda4d4e-4f08-4cd1-8cec-27f2b15f3ee2"}}, {"head": {"id": "b6d2d6d2-a9bc-4807-9ef7-b2f8daef5f51", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122149458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0da192-2af2-411d-9624-eb04b60ef469", "name": "entry : assembleHap cost memory 0.0120849609375", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122215208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e2f9f47-4dde-427f-963d-3484cc6345ea", "name": "runTaskFromQueue task cost before running: 1 s 256 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122254291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56baa338-127b-4174-a140-f96053dfe5c4", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084122145833, "endTime": 2084122276708, "totalTime": 95250}, "additional": {"logType": "info", "children": [], "durationId": "7bda4d4e-4f08-4cd1-8cec-27f2b15f3ee2"}}, {"head": {"id": "1bb9f242-33d2-4959-bd55-9690673d3c2b", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123548833, "endTime": 2084123627041}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd0e0115-6e85-4c3a-9568-fbbe183bb248", "logId": "ed87a886-6d51-4bc4-83cf-48e58b80e236"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed87a886-6d51-4bc4-83cf-48e58b80e236", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123548833, "endTime": 2084123627041}, "additional": {"logType": "info", "children": [], "durationId": "1bb9f242-33d2-4959-bd55-9690673d3c2b"}}, {"head": {"id": "2f3e639b-7bd6-42f7-8c9f-633e82e81a91", "name": "BUILD SUCCESSFUL in 1 s 258 ms ", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123660125}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "9883df94-849c-4ed4-a967-4a6a87ee98fd", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2082866586750, "endTime": 2084123816875}, "additional": {"time": {"year": 2024, "month": 12, "day": 11, "hour": 9, "minute": 16}, "completeCommand": "{\"prop\":[\"module=entry@default\",\"product=default\",\"requiredDeviceType=phone\"],\"mode\":\"module\",\"parallel\":true,\"incremental\":true,\"daemon\":true,\"_\":[\"assembleHap\"],\"analyze\":\"normal\"};assembleHap", "hvigorVersion": "5.13.1", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "6c68dc81-3de6-4521-ae0f-fa7db0be7b86", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123886333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061edab9-031a-4192-8775-4e2b550c1d2c", "name": "There is no need to refresh cache, since the incremental task upcloud:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123909750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8e2e23-0af5-4d06-aa2c-73c07fdff8f9", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123930291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c33a401-cada-46ac-90e1-bb8ff4f9ffa9", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084123983708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e38d0fa-c57b-4105-9708-6a5a23cdaa7f", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124108416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2025ad89-20ff-40a6-aa06-3a4e3d5c16c9", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124344625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "051be13f-c39d-49b8-987d-21721d449b77", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124391791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae45dd57-e8cc-4ed8-bcaa-e947db1ecf75", "name": "There is no need to refresh cache, since the incremental task upcloud:default@CreateHarBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124414750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3889cc3d-0f2e-4724-9876-5114ca465840", "name": "There is no need to refresh cache, since the incremental task upcloud:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124434875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73fdd912-f0df-415d-8775-ee39de572999", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124472833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71801b11-3488-4db8-876c-cf6a72fb212f", "name": "Update task entry:default@SyscapTransform input file:/Applications/DevEco-Studio.app/Contents/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124553958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b9517a9-c4ff-417e-8dd3-a0f074ca1098", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124805291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6cddfb4-f676-48c5-891a-d744e5c0df48", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124866541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a647363-2bd7-4805-88fd-982d1674a2dd", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124885125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b59cf36-5bb8-4550-90e9-484846dcbe62", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124909875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95a5b652-f1af-4193-90b1-8541057d8e41", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124927250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa453d1c-2a34-4439-95aa-fca0027fa020", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124948333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1efba7b-6689-4d51-902e-47b0f75085bf", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124964625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "484370ec-90fd-4b2d-9ef9-e25e98c03537", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084124980958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "067cadf2-0bf1-467e-b638-8b8f0f517a19", "name": "There is no need to refresh cache, since the incremental task upcloud:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125000750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaae5806-21b1-47df-b30e-7609d7a95540", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125017291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "202073d5-3eb9-41ed-9467-d8940e4abafc", "name": "There is no need to refresh cache, since the incremental task upcloud:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125037375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5080fe55-2011-4609-ba47-61e31620667e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125164416}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6daf1222-e4bb-4b53-8e2f-9333bf8d306e", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileArkTS is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125188750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e0c3984-6d3d-4fc1-a554-bc2de5277e39", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125676875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4a1b958-01f1-48f5-8055-d7a5bf184a73", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/upcloud/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125884916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825b3fc1-8b56-4df5-a46e-30f6f70a6ab4", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+httpclient@2.0.1-rc.7/oh_modules/@ohos/httpclient/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125925791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "951208d5-029e-48ba-a885-77ac5891f2b4", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@yunkss+eftool@1.2.3/oh_modules/@yunkss/eftool/index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084125997333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6dcc461-10b7-41e2-9469-a6329447a229", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+axios@2.2.0/oh_modules/@ohos/axios/index.js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126039208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db050e5e-9308-4afd-b696-c7361919a077", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/index.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126077208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa24451f-66e8-4289-b978-ee69130e444a", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/oh_modules/.ohpm/@ohos+crypto-js@2.0.3/oh_modules/@ohos/crypto-js/src/main/js cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126110875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6752bc-9134-4fa6-99b8-f39d74b96aa2", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126316708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9b6756d-95f9-465b-b513-aa8e4879de69", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126733666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7009c616-b3f2-4a68-886f-06b7767604ce", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126773333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f85438c3-90fe-4340-8ff3-e29337bc8607", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126811916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bbaee2c-1620-40eb-8d8d-3f2c22b909aa", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084126977625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a405b9-27b3-4929-b4e5-b4f4101c7ca2", "name": "Incremental task entry:default@BuildJS post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127054625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "702cd5b8-7177-4099-a32e-82029ec1fb0b", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127075291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce8b24d-4601-43f8-8754-0e8378f6c2c3", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127093958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1764d3b-057c-4957-bd4f-f7f506ba1a36", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127117166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3b49561-a1fc-4412-b34a-1c808f5bf777", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgModuleJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127132875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e49d6bc0-9b0b-4d26-be49-8151e6bbe8e1", "name": "There is no need to refresh cache, since the incremental task entry:default@PackageHap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127148125}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0d94442-12ac-4b16-a8da-66fc8e10f065", "name": "Update task entry:default@SignHap input file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-unsigned.hap cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127190583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "256298cd-d054-411f-a24e-f57f147d08ac", "name": "Update task entry:default@SignHap output file:/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127221791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c1e5716-1eb7-4aaf-a6f6-abde2640974c", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 23466, "tid": "Main Thread", "startTime": 2084127262500}, "additional": {"logType": "debug", "children": []}}]}