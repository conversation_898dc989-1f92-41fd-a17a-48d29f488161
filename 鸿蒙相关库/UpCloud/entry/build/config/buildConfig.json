{"compileConfig": {"deviceType": "default", "PATH": "/Applications/DevEco-Studio.app/Contents/tools/node/bin/", "localPropertiesPath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/local.properties", "note": "false", "aceProfilePath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile", "hapMode": "false", "buildMode": "debug", "img2bin": "true", "projectProfilePath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/build-profile.json5", "compilerType": "ark", "appResource": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/ResourceTable.txt", "watchMode": "false", "logLevel": "3", "aceBuildJson": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/loader.json", "aceModuleRoot": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/ets", "aceSoPath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/nativeDependencies.txt", "cachePath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/cache/default/default@CompileArkTS/esmodule/debug", "aceModuleBuild": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default/ets", "aceSuperVisualPath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/src/main/supervisual", "aceModuleJsonPath": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/module.json"}, "patchConfig": {"changedFileList": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/patch/default/changedFileList.json"}}