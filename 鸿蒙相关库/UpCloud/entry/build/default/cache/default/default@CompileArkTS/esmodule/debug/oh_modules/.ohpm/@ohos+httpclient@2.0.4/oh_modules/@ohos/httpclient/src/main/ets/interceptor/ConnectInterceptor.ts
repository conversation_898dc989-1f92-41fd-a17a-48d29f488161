import type { Chain, Interceptor } from '../Interceptor';
import type { Response } from '../response/Response';
import type { RealInterceptorChain } from './RealInterceptorChain';
export class ConnectInterceptor implements Interceptor {
    intercept(chain: Chain): Promise<Response> {
        chain = chain as RealInterceptorChain;
        const userRequest = chain.requestI();
        const connectResponse = chain.proceedI(userRequest);
        return connectResponse;
    }
}
