/**
 * <AUTHOR>
 * @DateTime 2023/12/29 20:07
 * @TODO ObjectUtil  对象工具类
 */
export class ObjectUtil {
    /**
     * 判断两个传入的数值或者是字符串是否相等
     * @param source
     * @param target
     * @returns
     */
    static equal(source: string | number, target: string | number): boolean {
        return source === target;
    }
    /**
     * 判断两个传入的数值或者是字符串是否不相等
     * @param source
     * @param target
     * @returns
     */
    static notEqual(source: string | number, target: string | number): boolean {
        return false == ObjectUtil.equal(source, target);
    }
}
