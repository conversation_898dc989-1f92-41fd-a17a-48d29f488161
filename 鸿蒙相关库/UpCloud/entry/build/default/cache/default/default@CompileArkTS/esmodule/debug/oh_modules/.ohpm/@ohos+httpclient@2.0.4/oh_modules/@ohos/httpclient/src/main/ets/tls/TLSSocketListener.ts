import type { BusinessError } from "@ohos:base";
export class TLSSocketListener {
    onBind(err: BusinessError, data: string) {
    }
    onMessage(err: BusinessError, data: object) {
    }
    onConnect(err: BusinessError, data: string) {
    }
    onClose(err: BusinessError, data: string) {
    }
    onError(err: BusinessError, data: string) {
    }
    offConnect(err: BusinessError, data: string) {
    }
    offClose(err: BusinessError, data: string) {
    }
    offMessage(err: BusinessError, data: string) {
    }
    offError(err: BusinessError, data: string) {
    }
    onSend(err: BusinessError, data: string) {
    }
    setExtraOptions(err: BusinessError, data: string) {
    }
    onVerify(verifyName: string, err: string, data: string) {
    }
}
