import type ArrayList from "@ohos:util.ArrayList";
import type Authenticator from './Authenticator';
import type Challenge from './Challenge';
import type Request from '../Request';
import Credentials from "@normalized:N&&&@ohos/httpclient/src/main/ets/authenticator/Credentials&2.0.4";
import StringUtil from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/StringUtil&2.0.4";
import HttpHeaders from "@normalized:N&&&@ohos/httpclient/src/main/ets/core/HttpHeaders&2.0.4";
import type { Response } from '../response/Response';
export default class NetAuthenticator implements Authenticator {
    userName: string;
    password: string;
    basicStr: string = 'Basic';
    credentials: string;
    constructor(userName: string, password: string) {
        if (StringUtil.isEmpty(userName)) {
            throw Error('Authorization need userName');
        }
        if (StringUtil.isEmpty(password)) {
            throw Error('Authorization need password');
        }
        this.userName = userName;
        this.password = password;
    }
    setCredentials(credentials): NetAuthenticator {
        this.credentials = credentials;
        return this;
    }
    authenticate(request: Request, response: Response): Request {
        if (StringUtil.isEmpty(this.userName) || StringUtil.isEmpty(this.password)) {
            return request;
        }
        let challenges: ArrayList<Challenge> = HttpHeaders.challenges(response);
        let credentials: string = (StringUtil.isEmpty(this.credentials) ? Credentials.basic(this.userName, this.password) : this.credentials);
        for (let challenge of challenges) {
            if (this.basicStr.toLowerCase() != (challenge.getScheme().toLowerCase()))
                continue;
            switch (response.responseCode) {
                case 401:
                    if (request.headers.hasOwnProperty('Authorization')) {
                        request.headers['Authorization'] = credentials;
                    }
                    else {
                        request.addHeader('Authorization', credentials);
                    }
                    break;
                case 407:
                    if (request.headers.hasOwnProperty('Proxy-Authorization')) {
                        request.headers['Proxy-Authorization'] = credentials;
                    }
                    else {
                        request.addHeader('Proxy-Authorization', credentials);
                    }
                    break;
            }
            break;
        }
        return request;
    }
}
