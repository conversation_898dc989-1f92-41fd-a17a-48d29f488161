if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface ImgPreviewUtil_Params {
    defController?: CustomDialogController;
    imgWidth?: number;
    beis?: number;
    url?: ResourceStr;
    showPopup?: boolean;
    filePath?: string;
    saveButtonOptions?: SaveButtonOptions;
    transtion0bj?: obj;
    defaultobj?: obj;
    isScale?: boolean;
}
import promptAction from "@ohos:promptAction";
import photoAccessHelper from "@ohos:file.photoAccessHelper";
import request from "@ohos:request";
import type { BusinessError } from "@ohos:base";
import util from "@ohos:util";
export class ImgPreviewUtil extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.defController = undefined;
        this.__imgWidth = new ObservedPropertySimplePU(300 //图片宽度
        , this, "imgWidth");
        this.__beis = new ObservedPropertySimplePU(1 //缩放倍数
        , this, "beis");
        this.__url = new ObservedPropertyObjectPU('' //图片url
        , this, "url");
        this.__showPopup = new ObservedPropertySimplePU(false //控制保存按钮显示
        , this, "showPopup");
        this.__filePath = new ObservedPropertySimplePU('' //下载路径  沙箱路径
        // 设置安全控件按钮属性
        , this, "filePath");
        this.__saveButtonOptions = new ObservedPropertyObjectPU({
            icon: SaveIconStyle.FULL_FILLED,
            text: SaveDescription.SAVE_IMAGE,
            buttonType: ButtonType.Capsule
        } // 设置安全控件按钮属性
        , this, "saveButtonOptions");
        this.__transtion0bj = new ObservedPropertyObjectPU(new obj(), this, "transtion0bj");
        this.__defaultobj = new ObservedPropertyObjectPU(new obj(), this, "defaultobj");
        this.__isScale = new ObservedPropertySimplePU(false //是否缩放
        , this, "isScale");
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: ImgPreviewUtil_Params) {
        if (params.defController !== undefined) {
            this.defController = params.defController;
        }
        if (params.imgWidth !== undefined) {
            this.imgWidth = params.imgWidth;
        }
        if (params.beis !== undefined) {
            this.beis = params.beis;
        }
        if (params.url !== undefined) {
            this.url = params.url;
        }
        if (params.showPopup !== undefined) {
            this.showPopup = params.showPopup;
        }
        if (params.filePath !== undefined) {
            this.filePath = params.filePath;
        }
        if (params.saveButtonOptions !== undefined) {
            this.saveButtonOptions = params.saveButtonOptions;
        }
        if (params.transtion0bj !== undefined) {
            this.transtion0bj = params.transtion0bj;
        }
        if (params.defaultobj !== undefined) {
            this.defaultobj = params.defaultobj;
        }
        if (params.isScale !== undefined) {
            this.isScale = params.isScale;
        }
    }
    updateStateVars(params: ImgPreviewUtil_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__imgWidth.purgeDependencyOnElmtId(rmElmtId);
        this.__beis.purgeDependencyOnElmtId(rmElmtId);
        this.__url.purgeDependencyOnElmtId(rmElmtId);
        this.__showPopup.purgeDependencyOnElmtId(rmElmtId);
        this.__filePath.purgeDependencyOnElmtId(rmElmtId);
        this.__saveButtonOptions.purgeDependencyOnElmtId(rmElmtId);
        this.__transtion0bj.purgeDependencyOnElmtId(rmElmtId);
        this.__defaultobj.purgeDependencyOnElmtId(rmElmtId);
        this.__isScale.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__imgWidth.aboutToBeDeleted();
        this.__beis.aboutToBeDeleted();
        this.__url.aboutToBeDeleted();
        this.__showPopup.aboutToBeDeleted();
        this.__filePath.aboutToBeDeleted();
        this.__saveButtonOptions.aboutToBeDeleted();
        this.__transtion0bj.aboutToBeDeleted();
        this.__defaultobj.aboutToBeDeleted();
        this.__isScale.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private defController: CustomDialogController;
    setController(ctr: CustomDialogController) {
        this.defController = ctr;
    }
    private __imgWidth: ObservedPropertySimplePU<number>; //图片宽度
    get imgWidth() {
        return this.__imgWidth.get();
    }
    set imgWidth(newValue: number) {
        this.__imgWidth.set(newValue);
    }
    private __beis: ObservedPropertySimplePU<number>; //缩放倍数
    get beis() {
        return this.__beis.get();
    }
    set beis(newValue: number) {
        this.__beis.set(newValue);
    }
    private __url: ObservedPropertyObjectPU<ResourceStr>; //图片url
    get url() {
        return this.__url.get();
    }
    set url(newValue: ResourceStr) {
        this.__url.set(newValue);
    }
    private __showPopup: ObservedPropertySimplePU<boolean>; //控制保存按钮显示
    get showPopup() {
        return this.__showPopup.get();
    }
    set showPopup(newValue: boolean) {
        this.__showPopup.set(newValue);
    }
    private __filePath: ObservedPropertySimplePU<string>; //下载路径  沙箱路径
    get filePath() {
        return this.__filePath.get();
    }
    set filePath(newValue: string) {
        this.__filePath.set(newValue);
    }
    // 设置安全控件按钮属性
    private __saveButtonOptions: ObservedPropertyObjectPU<SaveButtonOptions>; // 设置安全控件按钮属性
    get saveButtonOptions() {
        return this.__saveButtonOptions.get();
    }
    set saveButtonOptions(newValue: SaveButtonOptions) {
        this.__saveButtonOptions.set(newValue);
    }
    private __transtion0bj: ObservedPropertyObjectPU<obj>;
    get transtion0bj() {
        return this.__transtion0bj.get();
    }
    set transtion0bj(newValue: obj) {
        this.__transtion0bj.set(newValue);
    }
    private __defaultobj: ObservedPropertyObjectPU<obj>;
    get defaultobj() {
        return this.__defaultobj.get();
    }
    set defaultobj(newValue: obj) {
        this.__defaultobj.set(newValue);
    }
    private __isScale: ObservedPropertySimplePU<boolean>; //是否缩放
    get isScale() {
        return this.__isScale.get();
    }
    set isScale(newValue: boolean) {
        this.__isScale.set(newValue);
    }
    aboutToAppear(): void {
        //页面一加载就缓存图片
        this.downLoadImg(this.url);
    }
    //缓存图片
    downLoadImg(url: ResourceStr) {
        let context = getContext(this);
        let filesDir = context.filesDir;
        let uuid = util.generateRandomUUID(true);
        this.filePath = filesDir + '/' + uuid + '.jpg';
        console.info('ImgToUp_filename: ' + this.filePath);
        try {
            request.downloadFile(context, {
                url: url.toString(),
                filePath: this.filePath
            }).then((downloadTask: request.DownloadTask) => {
                downloadTask.on('complete', () => {
                    console.info('ImgToUp_downLoadImg file success');
                });
            }).catch((err: BusinessError) => {
                console.error(`ImgToUp_downLoadImg downloadTask failed, code is ${err.code}, message is ${err.message}`);
            });
        }
        catch (error) {
            let err: BusinessError = error as BusinessError;
            console.error(`ImgToUp_downLoadImg downloadFile failed, code is ${err.code}, message is ${err.message}`);
        }
    }
    //保存图片至相册
    async saveImg(res: SaveButtonOnClickResult) {
        if (res == SaveButtonOnClickResult.SUCCESS) {
            try {
                //拷贝文件
                let context = getContext();
                let phAccessHelper = photoAccessHelper.getPhotoAccessHelper(context);
                let assetChangeRequest: photoAccessHelper.MediaAssetChangeRequest = photoAccessHelper.MediaAssetChangeRequest.createImageAssetRequest(context, this.filePath);
                await phAccessHelper.applyChanges(assetChangeRequest);
                console.info('ImgToUp_saveImg success, uri: ' + assetChangeRequest.getAsset().uri);
                promptAction.showToast({ message: '保存成功' });
            }
            catch (err) {
                console.error(`ImgToUp_saveImg failed, error: ${err.code}, ${err.message}`);
                promptAction.showToast({ message: '保存失败' });
            }
        }
        else {
            promptAction.showToast({ message: '保存失败' });
            console.error('ImgToUp_SaveButtonOnClickResult create asset failed');
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 10 });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            SaveButton.create(this.saveButtonOptions);
            SaveButton.onClick((event, result: SaveButtonOnClickResult) => {
                this.saveImg(result);
            });
        }, SaveButton);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create(this.url);
            Image.width(this.imgWidth);
            Image.scale({ x: this.beis, y: this.beis });
            Image.aspectRatio(1);
            Image.onClick(() => {
                this.isScale = false;
                this.defController.close();
            });
            Image.translate({
                x: this.isScale ? this.transtion0bj.x : 0,
                y: this.isScale ? this.transtion0bj.y : 0
            });
            Gesture.create(GesturePriority.Low);
            //捏合事件
            PinchGesture.create({ fingers: 2 });
            //捏合事件
            PinchGesture.onActionStart((event: GestureEvent) => {
                this.isScale = true;
                this.beis = event.scale;
                console.info('ImgToUp_PinchGesture-onActionStart： scale：' + event.scale);
            });
            //捏合事件
            PinchGesture.onActionUpdate((event: GestureEvent) => {
                this.beis = event.scale;
                console.info('ImgToUp_PinchGesture-onActionUpdate： scale：' + event.scale);
            });
            //捏合事件
            PinchGesture.onActionEnd((event: GestureEvent) => {
                this.beis = event.scale;
                console.info('ImgToUp_PinchGesture-onActionEnd： scale：' + event.scale);
            });
            //捏合事件
            PinchGesture.pop();
            Gesture.pop();
            Gesture.create(GesturePriority.Low);
            //平移事件
            PanGesture.create();
            //平移事件
            PanGesture.onActionStart((event: GestureEvent) => {
                this.defaultobj.x = this.transtion0bj.x;
                this.defaultobj.y = this.transtion0bj.y;
            });
            //平移事件
            PanGesture.onActionUpdate((event: GestureEvent) => {
                this.transtion0bj.x = event.offsetX + this.defaultobj.x;
                this.transtion0bj.y = event.offsetY + this.defaultobj.y;
            });
            //平移事件
            PanGesture.pop();
            Gesture.pop();
            Image.width('100%');
            Image.height('100%');
        }, Image);
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export class obj {
    x: number = 0;
    y: number = 0;
}
