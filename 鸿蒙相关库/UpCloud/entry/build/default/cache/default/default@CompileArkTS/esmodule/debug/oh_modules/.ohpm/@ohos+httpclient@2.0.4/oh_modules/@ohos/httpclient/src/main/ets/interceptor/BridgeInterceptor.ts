import type { Chain, Interceptor } from '../Interceptor';
import type { Response } from '../response/Response';
import type { RealInterceptorChain } from './RealInterceptorChain';
import gZipUtil from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/gZipUtil&2.0.4";
import { Logger } from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/Logger&2.0.4";
import buffer from "@ohos:buffer";
import ConstantManager from "@normalized:N&&&@ohos/httpclient/src/main/ets/ConstantManager&2.0.4";
import hilog from "@ohos:hilog";
export class BridgeInterceptor implements Interceptor {
    intercept(chain: Chain): Promise<Response> {
        Logger.info("BridgeInterceptor intercept");
        chain = chain as RealInterceptorChain;
        const userRequest = chain.requestI();
        var header = userRequest.headers;
        var requestJSON = JSON.parse(JSON.stringify(header));
        Logger.info('BridgeInterceptor:requestJSON:' + JSON.stringify(header));
        var encodingFormat = requestJSON[ConstantManager.ACCEPT_ENCODING];
        encodingFormat = (encodingFormat == undefined) ? requestJSON[ConstantManager.ACCEPT_ENCODING.toLowerCase()] : encodingFormat;
        encodingFormat = (encodingFormat == undefined) ? requestJSON[ConstantManager.ACCEPT_ENCODING.toUpperCase()] : encodingFormat;
        Logger.info('BridgeInterceptor:encodingFormat:' + encodingFormat);
        if (encodingFormat == undefined || encodingFormat == null || encodingFormat == '') {
            const bridgeResponse = chain.proceedI(userRequest);
            return bridgeResponse;
        }
        if (encodingFormat.toString().toLowerCase() == 'gzip') {
            try {
                if (userRequest.body != null) {
                    let compressed = gZipUtil.gZipString(userRequest.body.content);
                    if (userRequest.gzipBodyNeedBuffer == true) {
                        let bufferContent = buffer.from(compressed as Uint8Array);
                        userRequest.body.content = bufferContent;
                    }
                    else {
                        const arrayBuffer = compressed.buffer;
                        userRequest.body.content = arrayBuffer;
                    }
                }
            }
            catch (error) {
                hilog.info(0x0001, "gInterceptors: Request error", error.message);
            }
        }
        const bridgeResponse = chain.proceedI(userRequest);
        return bridgeResponse;
    }
}
