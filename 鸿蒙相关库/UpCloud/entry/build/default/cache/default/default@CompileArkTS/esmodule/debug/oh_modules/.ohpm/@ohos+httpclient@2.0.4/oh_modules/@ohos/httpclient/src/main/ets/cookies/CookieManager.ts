import { CookiePolicy } from "@normalized:N&&&@ohos/httpclient/src/main/ets/cookies/httpcookieutils&2.0.4";
function CookieManager() {
    var cookiePolicy = CookiePolicy.ACCEPT_ORIGINAL_SERVER;
}
CookieManager.prototype.setCookiePolicy = function setCookiePolicy(policy) {
    this.cookiePolicy = policy || CookiePolicy.ACCEPT_ORIGINAL_SERVER;
};
CookieManager.prototype.toString = function toString() {
    return "CookiePolicy:" + this.cookiePolicy;
};
export default CookieManager;
