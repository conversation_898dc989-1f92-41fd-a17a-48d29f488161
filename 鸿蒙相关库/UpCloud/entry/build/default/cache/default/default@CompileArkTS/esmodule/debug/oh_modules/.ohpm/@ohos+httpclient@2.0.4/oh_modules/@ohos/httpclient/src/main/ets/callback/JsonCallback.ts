import { Logger } from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/Logger&2.0.4";
import JsonConvert from "@normalized:N&&&@ohos/httpclient/src/main/ets/callback/JsonConvert&2.0.4";
import AbsCallback from "@normalized:N&&&@ohos/httpclient/src/main/ets/callback/AbsCallback&2.0.4";
class JsonCallback extends AbsCallback {
    constructor() {
        super();
        Logger.info("httpclient- JsonCallback constructor");
    }
    convertResponse(response) {
        Logger.info("httpclient- JsonCallback convertResponse: " + response);
        var convert = new JsonConvert();
        return convert.convertResponse(response);
    }
}
export default JsonCallback;
