import type { RealWebSocket } from './RealWebSocket';
export abstract class WebSocketListener {
    abstract onOpen(webSocket: RealWebSocket, response: string): void;
    abstract onMessage(webSocket: RealWebSocket, text: string | ArrayBuffer): void;
    abstract onClosing(webSocket: RealWebSocket, code: number, reason?: string): void;
    abstract onClosed(webSocket: RealWebSocket, code: number, reason: string): void;
    abstract onFailure(webSocket: RealWebSocket, e: Error, response?: string): void;
}
