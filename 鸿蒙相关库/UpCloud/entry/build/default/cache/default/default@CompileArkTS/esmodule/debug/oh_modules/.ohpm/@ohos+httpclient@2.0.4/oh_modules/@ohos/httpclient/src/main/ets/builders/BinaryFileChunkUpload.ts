import ChunkUploadDispatcher from "@normalized:N&&&@ohos/httpclient/src/main/ets/dispatcher/ChunkUploadDispatcher&2.0.4";
var _data = Symbol();
var _uploadProgress = Symbol();
var _chunkUploadDispatcher = Symbol();
var _uploadCallback = Symbol();
class BinaryFileChunkUpload {
    constructor(build) {
        if (!arguments.length) {
            build = new BinaryFileChunkUpload.Builder();
        }
        this[_data] = build[_data];
        this[_uploadProgress] = build[_uploadProgress];
        this[_chunkUploadDispatcher] = build[_chunkUploadDispatcher];
        this[_uploadCallback] = build[_uploadCallback];
    }
    static get Builder() {
        class Builder {
            constructor() {
                this[_data] = [];
                this[_uploadProgress] = null;
                this[_uploadCallback] = null;
                this[_chunkUploadDispatcher] = new ChunkUploadDispatcher();
            }
            addBinaryFile(abilityContext, chunkUploadOptions: ChunkUploadOptions) {
                this[_chunkUploadDispatcher].chunkFile(abilityContext, chunkUploadOptions);
                return this;
            }
            addData(fname, fvalue) {
                let dataObject = { name: fname, value: fvalue };
                this[_data].push(dataObject);
                return this;
            }
            addUploadProgress(uploadProgressCallback: (uploadedSize: number, totalSize: number) => {}) {
                this[_uploadProgress] = uploadProgressCallback;
                return this;
            }
            addUploadCallback(uploadCallback: (stat: string, errQueue?) => {}) {
                this[_uploadCallback] = uploadCallback;
                return this;
            }
            build() {
                return new BinaryFileChunkUpload(this);
            }
        }
        return Builder;
    }
    getData() {
        if (this[_data]) {
            return this[_data];
        }
        return [];
    }
    getUploadProgress() {
        if (this[_uploadProgress]) {
            return this[_uploadProgress];
        }
        return undefined;
    }
    getChunkUploadDispatcher() {
        if (this[_chunkUploadDispatcher]) {
            return this[_chunkUploadDispatcher];
        }
        return undefined;
    }
    getUploadCallback() {
        if (this[_uploadCallback]) {
            return this[_uploadCallback];
        }
        return undefined;
    }
}
export default BinaryFileChunkUpload;
export interface ChunkUploadOptions {
    fileData?: ArrayBuffer;
    filePath: string;
    fileName: string;
    chunkSize?: number;
    name?: string;
}
