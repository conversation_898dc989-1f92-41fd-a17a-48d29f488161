if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface TypeWritingUtil_Params {
    text?: string;
    curIndex?: number;
    placeholderText?: string;
    inputText?: string;
    inputTextHanzi?: string[];
    enChina?: boolean;
    isUp?: 0 | 1 | 2;
    showPopup?: boolean;
    popText?: string;
    showNum?: boolean;
    //键盘隐藏事件
    hideClick?: () => void;
}
export class TypeWritingUtil extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__text = new SynchedPropertySimpleTwoWayPU(params.text, this, "text");
        this.__curIndex = new ObservedPropertySimplePU(0, this, "curIndex");
        this.__placeholderText = new ObservedPropertySimplePU('请输入值', this, "placeholderText");
        this.__inputText = new ObservedPropertySimplePU('' //字母输入区
        , this, "inputText");
        this.__inputTextHanzi = new ObservedPropertyObjectPU([] //汉字候选区
        , this, "inputTextHanzi");
        this.__enChina = new ObservedPropertySimplePU(false //是否中英文false = 英文，true=中文
        , this, "enChina");
        this.__isUp = new ObservedPropertySimplePU(0 //是否大小写  0=小写，1=短时大写，2=长时大写
        , this, "isUp");
        this.__showPopup = new ObservedPropertySimplePU(false //气泡控制
        , this, "showPopup");
        this.__popText = new ObservedPropertySimplePU('' //气泡内容
        , this, "popText");
        this.__showNum = new ObservedPropertySimplePU(false //气泡内容
        //键盘隐藏事件
        , this, "showNum");
        this.hideClick = () => {
        };
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: TypeWritingUtil_Params) {
        if (params.curIndex !== undefined) {
            this.curIndex = params.curIndex;
        }
        if (params.placeholderText !== undefined) {
            this.placeholderText = params.placeholderText;
        }
        if (params.inputText !== undefined) {
            this.inputText = params.inputText;
        }
        if (params.inputTextHanzi !== undefined) {
            this.inputTextHanzi = params.inputTextHanzi;
        }
        if (params.enChina !== undefined) {
            this.enChina = params.enChina;
        }
        if (params.isUp !== undefined) {
            this.isUp = params.isUp;
        }
        if (params.showPopup !== undefined) {
            this.showPopup = params.showPopup;
        }
        if (params.popText !== undefined) {
            this.popText = params.popText;
        }
        if (params.showNum !== undefined) {
            this.showNum = params.showNum;
        }
        if (params.hideClick !== undefined) {
            this.hideClick = params.hideClick;
        }
    }
    updateStateVars(params: TypeWritingUtil_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__text.purgeDependencyOnElmtId(rmElmtId);
        this.__curIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__placeholderText.purgeDependencyOnElmtId(rmElmtId);
        this.__inputText.purgeDependencyOnElmtId(rmElmtId);
        this.__inputTextHanzi.purgeDependencyOnElmtId(rmElmtId);
        this.__enChina.purgeDependencyOnElmtId(rmElmtId);
        this.__isUp.purgeDependencyOnElmtId(rmElmtId);
        this.__showPopup.purgeDependencyOnElmtId(rmElmtId);
        this.__popText.purgeDependencyOnElmtId(rmElmtId);
        this.__showNum.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__text.aboutToBeDeleted();
        this.__curIndex.aboutToBeDeleted();
        this.__placeholderText.aboutToBeDeleted();
        this.__inputText.aboutToBeDeleted();
        this.__inputTextHanzi.aboutToBeDeleted();
        this.__enChina.aboutToBeDeleted();
        this.__isUp.aboutToBeDeleted();
        this.__showPopup.aboutToBeDeleted();
        this.__popText.aboutToBeDeleted();
        this.__showNum.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __text: SynchedPropertySimpleTwoWayPU<string>;
    get text() {
        return this.__text.get();
    }
    set text(newValue: string) {
        this.__text.set(newValue);
    }
    private __curIndex: ObservedPropertySimplePU<number>;
    get curIndex() {
        return this.__curIndex.get();
    }
    set curIndex(newValue: number) {
        this.__curIndex.set(newValue);
    }
    private __placeholderText: ObservedPropertySimplePU<string>;
    get placeholderText() {
        return this.__placeholderText.get();
    }
    set placeholderText(newValue: string) {
        this.__placeholderText.set(newValue);
    }
    private __inputText: ObservedPropertySimplePU<string>; //字母输入区
    get inputText() {
        return this.__inputText.get();
    }
    set inputText(newValue: string) {
        this.__inputText.set(newValue);
    }
    private __inputTextHanzi: ObservedPropertyObjectPU<string[]>; //汉字候选区
    get inputTextHanzi() {
        return this.__inputTextHanzi.get();
    }
    set inputTextHanzi(newValue: string[]) {
        this.__inputTextHanzi.set(newValue);
    }
    private __enChina: ObservedPropertySimplePU<boolean>; //是否中英文false = 英文，true=中文
    get enChina() {
        return this.__enChina.get();
    }
    set enChina(newValue: boolean) {
        this.__enChina.set(newValue);
    }
    private __isUp: ObservedPropertySimplePU<0 | 1 | 2>; //是否大小写  0=小写，1=短时大写，2=长时大写
    get isUp() {
        return this.__isUp.get();
    }
    set isUp(newValue: 0 | 1 | 2) {
        this.__isUp.set(newValue);
    }
    private __showPopup: ObservedPropertySimplePU<boolean>; //气泡控制
    get showPopup() {
        return this.__showPopup.get();
    }
    set showPopup(newValue: boolean) {
        this.__showPopup.set(newValue);
    }
    private __popText: ObservedPropertySimplePU<string>; //气泡内容
    get popText() {
        return this.__popText.get();
    }
    set popText(newValue: string) {
        this.__popText.set(newValue);
    }
    private __showNum: ObservedPropertySimplePU<boolean>; //气泡内容
    get showNum() {
        return this.__showNum.get();
    }
    set showNum(newValue: boolean) {
        this.__showNum.set(newValue);
    }
    //键盘隐藏事件
    private hideClick: () => void;
    //拼字事件
    pinyinAdd(input: string) {
        console.log('89757输入框:' + input);
        if (this.enChina && this.isUp === 0) {
            this.inputText += input;
            for (let i = lexicon.ciku.length - 1; i >= 0; i--) {
                if (lexicon.ciku[i][0] == this.inputText) {
                    let tmp: string[] = lexicon.ciku[i];
                    for (let j = 1; j < tmp.length; j++) {
                        this.inputTextHanzi.push(tmp[j]);
                    }
                    break;
                }
            }
        }
        else {
            if (this.isUp === 1) {
                this.text += input.toUpperCase();
                this.isUp = 0;
            }
            else if (this.isUp === 2) {
                this.text += input.toUpperCase();
            }
            else {
                this.text += input;
            }
        }
        console.log('89757候选词:' + this.inputTextHanzi);
    }
    //删除事件
    pinyinDel(input: string) {
        console.log('89757输入框:' + input);
        let tmp: string[] = [];
        for (let i = lexicon.ciku.length - 1; i >= 0; i--) {
            if (lexicon.ciku[i][0] == this.inputText) {
                let tmp: string[] = lexicon.ciku[i];
                for (let j = 1; j < 6; j++) {
                    tmp.push(tmp[j]);
                }
                break;
            }
        }
        this.inputTextHanzi = tmp;
        console.log('89757候选词:' + this.inputTextHanzi);
    }
    //候选区词语点击事件
    hxClick(item: string) {
        this.text += item;
        this.inputText = '';
        this.inputTextHanzi = [];
    }
    //隐藏按钮点击事件
    ycClick() {
        //this.inputController.stopEditing()
        this.hideClick();
        this.inputText = '';
        this.inputTextHanzi = [];
    }
    //大小写点击事件
    upClick() {
        if (this.isUp === 0) {
            this.isUp = 1;
            this.showNum = false;
        }
        else if (this.isUp === 1) {
            this.isUp = 2;
            this.showNum = false;
        }
        else {
            this.isUp = 0;
        }
    }
    //中英文切换点击事件
    enChinaClick() {
        if (this.inputText.length !== 0) {
            this.text += this.inputText;
        }
        this.inputText = '';
        this.inputTextHanzi = [];
        this.isUp = 0;
        this.showNum = false;
        this.enChina = !this.enChina;
    }
    //删除点击事件
    delClick() {
        if (this.inputText.length > 0) {
            this.inputText = this.inputText.substring(0, this.inputText.length - 1);
            this.pinyinDel(this.inputText);
        }
        else {
            this.text = this.text.substring(0, this.text.length - 1);
        }
    }
    //删除长按事件
    delChaClick() {
        this.text = '';
        this.inputTextHanzi = [];
        this.inputText = '';
    }
    //空格点击事件
    kgClick() {
        this.text += ' ';
    }
    //符号+123 点击事件
    fuhao123Click() {
        this.isUp = 0;
        this.enChina = false;
        this.showNum = !this.showNum;
    }
    //气泡
    popRow(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.visibility(Visibility.None);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Normal });
            Button.width('9%');
            Button.backgroundColor('#FFFFFF');
            Button.borderRadius(6);
            Button.bindPopup(this.showPopup, {
                message: this.popText,
                popupColor: Color.Grey,
                placementOnTop: true,
                backgroundBlurStyle: BlurStyle.NONE,
                onStateChange: (event) => {
                    // 当手指点了其他位置 关闭状态
                    this.showPopup = event.isVisible;
                }
            });
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 5 });
            Column.width(10);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('');
            Text.fontSize(15);
            Text.fontColor('#d0d0d0');
        }, Text);
        Text.pop();
        Column.pop();
        Button.pop();
        Row.pop();
    }
    //单个字母按键
    comButton(item: keyValue, parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild({ type: ButtonType.Normal });
            Button.width('9%');
            Button.backgroundColor('#FFFFFF');
            Button.borderRadius(6);
            Button.onClick(() => {
                if (this.showNum) {
                    this.pinyinAdd(item.value.toString());
                }
                else {
                    this.pinyinAdd(item.key.toString());
                }
            });
            Gesture.create(GesturePriority.Low);
            LongPressGesture.create();
            LongPressGesture.onAction(() => {
                if (this.showNum) {
                    this.popText = item.key.toString();
                }
                else {
                    this.popText = item.value.toString();
                }
                this.showPopup = true;
            });
            LongPressGesture.onActionEnd(() => {
                this.text += this.popText;
                this.showPopup = false;
            });
            LongPressGesture.pop();
            Gesture.pop();
        }, Button);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 5 });
            Column.width(10);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isUp !== 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.key.toString().toUpperCase());
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontSize(18);
                        Text.fontColor('#000000');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.value.toString().toUpperCase());
                        Text.fontSize(15);
                        Text.fontColor('#d0d0d0');
                    }, Text);
                    Text.pop();
                });
            }
            else if (this.showNum) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.value);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontSize(18);
                        Text.fontColor('#000000');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.key);
                        Text.fontSize(15);
                        Text.fontColor('#d0d0d0');
                    }, Text);
                    Text.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.key);
                        Text.fontWeight(FontWeight.Bold);
                        Text.fontSize(18);
                        Text.fontColor('#000000');
                    }, Text);
                    Text.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create(item.value);
                        Text.fontSize(15);
                        Text.fontColor('#d0d0d0');
                    }, Text);
                    Text.pop();
                });
            }
        }, If);
        If.pop();
        Column.pop();
        Button.pop();
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 10 });
            Column.height('35%');
            Column.width('100%');
            Column.backgroundColor('#d0d0d0');
            Column.padding(10);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //上功能区-候选区
            Row.create();
            //上功能区-候选区
            Row.width('100%');
            //上功能区-候选区
            Row.height(50);
            //上功能区-候选区
            Row.justifyContent(FlexAlign.SpaceBetween);
            //上功能区-候选区
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create({ space: 5 });
            Column.layoutWeight(1);
            Column.height('100%');
            Column.alignItems(HorizontalAlign.Start);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.height('50%');
            Row.onClick(() => {
                this.hxClick(this.inputText);
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //拼音区
            Text.create(this.inputText);
            //拼音区
            Text.fontSize(18);
            //拼音区
            Text.fontColor('#000000');
            //拼音区
            Text.visibility(Visibility.Visible);
        }, Text);
        //拼音区
        Text.pop();
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //汉字候选区
            Scroll.create();
            //汉字候选区
            Scroll.width('100%');
            //汉字候选区
            Scroll.height('50%');
            //汉字候选区
            Scroll.scrollable(ScrollDirection.Horizontal);
            //汉字候选区
            Scroll.padding({ bottom: 5 });
        }, Scroll);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create();
            Text.textAlign(TextAlign.Start);
        }, Text);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Span.create(item + '  ');
                    Span.fontSize(18);
                    Span.fontColor('#000000');
                    Span.visibility(Visibility.Hidden);
                    Span.onClick(() => {
                        this.hxClick(item);
                    });
                }, Span);
            };
            this.forEachUpdateFunction(elmtId, this.inputTextHanzi, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        Text.pop();
        Row.pop();
        //汉字候选区
        Scroll.pop();
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //隐藏按钮
            Column.create();
            //隐藏按钮
            Column.justifyContent(FlexAlign.Center);
            //隐藏按钮
            Column.alignItems(HorizontalAlign.Center);
            //隐藏按钮
            Column.width('10%');
            //隐藏按钮
            Column.height('100%');
            //隐藏按钮
            Column.border({
                width: {
                    left: 1,
                },
                color: '#FFFFFF'
            });
            //隐藏按钮
            Column.onClick(() => {
                this.ycClick();
            });
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Image.create({ "id": 16777232, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" });
            Image.width(20);
            Image.aspectRatio(1);
        }, Image);
        //隐藏按钮
        Column.pop();
        //上功能区-候选区
        Row.pop();
        //气泡占位
        this.popRow.bind(this)();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //第一行
            Row.create();
            //第一行
            Row.justifyContent(FlexAlign.SpaceBetween);
            //第一行
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.comButton.bind(this)(item);
            };
            this.forEachUpdateFunction(elmtId, lexicon.dataList1, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        //第一行
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //第二行
            Row.create();
            //第二行
            Row.justifyContent(FlexAlign.SpaceBetween);
            //第二行
            Row.width('100%');
            //第二行
            Row.padding({
                left: 15,
                right: 15,
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.comButton.bind(this)(item);
            };
            this.forEachUpdateFunction(elmtId, lexicon.dataList2, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        //第二行
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //第三行
            Row.create();
            //第三行
            Row.justifyContent(FlexAlign.SpaceBetween);
            //第三行
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (item.key === 'top') {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Button.createWithChild({ type: ButtonType.Normal });
                                Button.width('13%');
                                Button.height('15%');
                                Button.backgroundColor('#FFFFFF');
                                Button.borderRadius(6);
                                Button.onClick(() => {
                                    this.upClick();
                                });
                            }, Button);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (this.isUp === 0) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Image.create(item.value);
                                            Image.height(20);
                                            Image.aspectRatio(1);
                                            Image.fillColor('#000000');
                                        }, Image);
                                    });
                                }
                                else if (this.isUp === 1) {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Image.create(item.value);
                                            Image.height(20);
                                            Image.aspectRatio(1);
                                            Image.fillColor('#84C1FF');
                                        }, Image);
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(2, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Image.create({ "id": 16777231, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" });
                                            Image.height(20);
                                            Image.aspectRatio(1);
                                            Image.fillColor('#84C1FF');
                                        }, Image);
                                    });
                                }
                            }, If);
                            If.pop();
                            Button.pop();
                        });
                    }
                    else if (item.key === 'delete') {
                        this.ifElseBranchUpdateFunction(1, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Button.createWithChild({ type: ButtonType.Normal });
                                Button.width('13%');
                                Button.height('15%');
                                Button.backgroundColor('#FFFFFF');
                                Button.borderRadius(6);
                                Button.onClick(() => {
                                    this.delClick();
                                });
                                Gesture.create(GesturePriority.Low);
                                LongPressGesture.create();
                                LongPressGesture.onAction((event: GestureEvent) => {
                                    if (event) {
                                        this.delChaClick();
                                    }
                                });
                                LongPressGesture.pop();
                                Gesture.pop();
                            }, Button);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(item.value);
                                Image.height(20);
                                Image.aspectRatio(1);
                                Image.fillColor('#000000');
                                Gesture.create(GesturePriority.Low);
                                LongPressGesture.create();
                                LongPressGesture.onAction((event: GestureEvent) => {
                                    if (event) {
                                        this.delChaClick();
                                    }
                                });
                                LongPressGesture.pop();
                                Gesture.pop();
                            }, Image);
                            Button.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(2, () => {
                            this.comButton.bind(this)(item);
                        });
                    }
                }, If);
                If.pop();
            };
            this.forEachUpdateFunction(elmtId, lexicon.dataList3, forEachItemGenFunction);
        }, ForEach);
        ForEach.pop();
        //第三行
        Row.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            //第四行
            Row.create();
            //第四行
            Row.justifyContent(FlexAlign.SpaceBetween);
            //第四行
            Row.width('100%');
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    If.create();
                    if (item.key === 'huiche') {
                        this.ifElseBranchUpdateFunction(0, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Button.createWithChild({ type: ButtonType.Normal });
                                Button.width('11%');
                                Button.height('15%');
                                Button.backgroundColor('#FFFFFF');
                                Button.borderRadius(6);
                            }, Button);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(item.value);
                                Image.height(20);
                                Image.aspectRatio(1);
                                Image.fillColor('#000000');
                                Image.onClick(() => {
                                    this.text += '\n';
                                });
                            }, Image);
                            Button.pop();
                        });
                    }
                    else if (item.key === 'kongge') {
                        this.ifElseBranchUpdateFunction(1, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Button.createWithChild({ type: ButtonType.Normal });
                                Button.width('20%');
                                Button.height('15%');
                                Button.backgroundColor('#FFFFFF');
                                Button.borderRadius(6);
                                Button.onClick(() => {
                                    this.kgClick();
                                });
                            }, Button);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Image.create(item.value);
                                Image.height(20);
                                Image.width('60%');
                                Image.fillColor('#000000');
                            }, Image);
                            Button.pop();
                        });
                    }
                    else if (index === 5) {
                        this.ifElseBranchUpdateFunction(2, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Button.createWithChild({ type: ButtonType.Normal });
                                Button.width('11%');
                                Button.height('15%');
                                Button.backgroundColor('#FFFFFF');
                                Button.borderRadius(6);
                                Button.borderRadius(6);
                                Button.onClick(() => {
                                    this.enChinaClick();
                                });
                            }, Button);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                Column.create({ space: 5 });
                            }, Column);
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (!this.enChina) {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('英');
                                            Text.fontWeight(FontWeight.Bold);
                                            Text.fontSize(18);
                                            Text.fontColor('#000000');
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('/中');
                                            Text.fontSize(15);
                                            Text.fontWeight(FontWeight.Bold);
                                            Text.fontColor('#d0d0d0');
                                        }, Text);
                                        Text.pop();
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('中');
                                            Text.fontWeight(FontWeight.Bold);
                                            Text.fontSize(18);
                                            Text.fontColor('#84C1FF');
                                        }, Text);
                                        Text.pop();
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create('/英');
                                            Text.fontSize(15);
                                            Text.fontWeight(FontWeight.Bold);
                                            Text.fontColor('#d0d0d0');
                                        }, Text);
                                        Text.pop();
                                    });
                                }
                            }, If);
                            If.pop();
                            Column.pop();
                            Button.pop();
                        });
                    }
                    else {
                        this.ifElseBranchUpdateFunction(3, () => {
                            this.observeComponentCreation2((elmtId, isInitialRender) => {
                                If.create();
                                if (item.key === '123' || item.key === 'fuhao') {
                                    this.ifElseBranchUpdateFunction(0, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Button.createWithChild({ type: ButtonType.Normal });
                                            Button.width('11%');
                                            Button.height('15%');
                                            Button.backgroundColor('#FFFFFF');
                                            Button.borderRadius(6);
                                            Button.onClick(() => {
                                                this.fuhao123Click();
                                            });
                                        }, Button);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.value);
                                            Text.fontSize(15);
                                            Text.fontWeight(this.showNum ? FontWeight.Bold : FontWeight.Normal);
                                            Text.fontColor(this.showNum ? '#000000' : '#d0d0d0');
                                        }, Text);
                                        Text.pop();
                                        Button.pop();
                                    });
                                }
                                else {
                                    this.ifElseBranchUpdateFunction(1, () => {
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Button.createWithChild({ type: ButtonType.Normal });
                                            Button.width('11%');
                                            Button.height('15%');
                                            Button.backgroundColor('#FFFFFF');
                                            Button.borderRadius(6);
                                        }, Button);
                                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                                            Text.create(item.value);
                                            Text.fontSize(15);
                                            Text.fontWeight(FontWeight.Bold);
                                            Text.fontColor('#d0d0d0');
                                            Text.onClick(() => {
                                                this.text += item.value;
                                            });
                                        }, Text);
                                        Text.pop();
                                        Button.pop();
                                    });
                                }
                            }, If);
                            If.pop();
                        });
                    }
                }, If);
                If.pop();
            };
            this.forEachUpdateFunction(elmtId, lexicon.dataList4, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        //第四行
        Row.pop();
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
export class lexicon {
    static dataList1: keyValue[] = [
        {
            key: 'q',
            value: '1'
        },
        {
            key: 'w',
            value: '2'
        },
        {
            key: 'e',
            value: '3'
        },
        {
            key: 'r',
            value: '4'
        },
        {
            key: 't',
            value: '5'
        },
        {
            key: 'y',
            value: '6'
        },
        {
            key: 'u',
            value: '7'
        },
        {
            key: 'i',
            value: '8'
        },
        {
            key: 'o',
            value: '9'
        },
        {
            key: 'p',
            value: '0'
        }
    ];
    static dataList2: keyValue[] = [
        {
            key: 'a',
            value: '~'
        },
        {
            key: 's',
            value: '!'
        },
        {
            key: 'd',
            value: '@'
        },
        {
            key: 'f',
            value: '#'
        },
        {
            key: 'g',
            value: '%'
        },
        {
            key: 'h',
            value: '^'
        },
        {
            key: 'j',
            value: '&'
        },
        {
            key: 'k',
            value: '*'
        },
        {
            key: 'l',
            value: '?'
        },
    ];
    static dataList3: keyValue[] = [
        {
            key: 'top',
            value: { "id": 16777231, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" }
        },
        {
            key: 'z',
            value: '('
        },
        {
            key: 'x',
            value: ')'
        },
        {
            key: 'c',
            value: '-'
        },
        {
            key: 'v',
            value: '_'
        },
        {
            key: 'b',
            value: ':'
        },
        {
            key: 'n',
            value: ';'
        },
        {
            key: 'm',
            value: '/'
        },
        {
            key: 'delete',
            value: { "id": 16777229, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" }
        }
    ];
    static dataList4: keyValue[] = [
        {
            key: 'fuhao',
            value: '!?#'
        },
        {
            key: '123',
            value: '123'
        },
        {
            key: '',
            value: ','
        },
        {
            key: 'kongge',
            value: { "id": 16777234, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" }
        },
        {
            key: '',
            value: '.'
        },
        {
            key: '英/',
            value: '中'
        },
        {
            key: 'huiche',
            value: { "id": 16777233, "type": 20000, params: [], "bundleName": "com.haier.uhome.upcloud", "moduleName": "entry" }
        }
    ];
    static ciku: string[][] = [
        ["a", "啊", "阿"],
        ["ai", "爱", "艾", "哎", "埃", "挨", "唉", "哀", "皑", "蔼", "矮", "碍", "隘", "癌"],
        ["an", "安", "俺", "按", "暗", "岸", "胺", "案", "鞍", "氨",],
        ["ang", "肮", "昂", "盎"],
        ["ao", "嗷", "凹", "敖", "熬", "翱", "袄", "傲", "奥", "懊", "澳"],
        ["b", "爸", "八", "叭", "吧", "笆", "八", "疤", "巴", "拔", "跋", "靶", "把", "坝", "霸", "罢", "爸", "扒", "耙"],
        ["ba", "芭", "捌", "叭", "吧", "笆", "八", "疤", "巴", "拔", "跋", "靶", "把", "坝", "霸", "罢", "爸", "扒", "耙"],
        ["bai", "白", "摆", "佰", "败", "拜", "柏", "百", "稗", "伯"],
        ["ban", "斑", "班", "搬", "扳", "颁", "板", "版", "扮", "拌", "伴", "瓣", "半", "办", "绊", "般"],
        ["bang", "邦", "帮", "梆", "榜", "绑", "棒", "镑", "傍", "谤", "膀", "磅", "蚌"],
        ["bao", "苞", "胞", "包", "褒", "雹", "保", "饱", "宝", "抱", "报", "豹", "鲍", "爆", "剥", "薄", "暴", "刨", "炮",
            "曝", "瀑", "堡"],
        ["bei", "杯", "碑", "悲", "卑", "北", "辈", "背", "贝", "钡", "倍", "狈", "备", "惫", "焙", "被"],
        ["ben", "奔", "苯", "本", "笨"],
        ["beng", "崩", "绷", "甭", "泵", "蹦", "迸", "蚌"],
        ["bi", "逼", "鼻", "比", "鄙", "笔", "彼", "碧", "蓖", "蔽", "毕", "毙", "毖", "币", "庇", "痹", "闭", "敝", "弊",
            "必", "壁", "避", "陛", "辟", "臂", "秘"],
        ["bian", "鞭", "边", "编", "贬", "变", "卞", "辨", "辩", "辫", "扁", "便", "遍"],
        ["biao", "标", "彪", "膘", "表"],
        ["bie", "鳖", "憋", "别", "瘪"],
        ["bin", "彬", "斌", "濒", "滨", "宾", "摈"],
        ["bing", "兵", "冰", "柄", "丙", "秉", "饼", "炳", "病", "并", "屏"],
        ["bo", "玻", "菠", "播", "拨", "钵", "博", "勃", "搏", "铂", "箔", "帛", "舶", "脖", "膊", "渤", "驳", "柏", "剥",
            "薄", "波", "泊", "卜", "般", "伯"],
        ["bu", "捕", "哺", "补", "埠", "布", "步", "簿", "部", "怖", "卜", "不", "埔", "堡"],
        ["ca", "擦"],
        ["cai", "猜", "裁", "材", "才", "财", "睬", "踩", "采", "彩", "菜", "蔡"],
        ["can", "餐", "残", "惭", "惨", "灿", "蚕", "参", "掺"],
        ["cang", "苍", "舱", "仓", "沧", "藏"],
        ["cao", "操", "糙", "槽", "曹", "草"],
        ["ce", "策", "册", "测", "厕", "侧"],
        ["cen", "参"],
        ["ceng", "层", "蹭", "曾"],
        ["cha", "插", "叉", "茶", "碴", "搽", "察", "岔", "诧", "茬", "查", "刹", "喳", "差"],
        ["chai", "柴", "豺", "拆", "差"],
        ["chan", "搀", "蝉", "馋", "谗", "缠", "铲", "产", "阐", "颤", "掺", "单"],
        ["chang", "昌", "猖", "场", "尝", "常", "偿", "肠", "厂", "畅", "唱", "倡", "长", "敞", "裳"],
        ["chao", "超", "抄", "钞", "潮", "巢", "吵", "炒", "朝", "嘲", "绰", "剿"],
        ["che", "扯", "撤", "掣", "彻", "澈", "车"],
        ["chen", "郴", "臣", "辰", "尘", "晨", "忱", "沉", "陈", "趁", "衬", "橙", "沈", "称", "秤"],
        ["cheng", "撑", "城", "成", "呈", "程", "惩", "诚", "承", "逞", "骋", "橙", "乘", "澄", "盛", "称", "秤"],
        ["chi", "痴", "持", "池", "迟", "弛", "驰", "耻", "齿", "侈", "赤", "翅", "斥", "炽", "吃", "匙", "尺"],
        ["chong", "充", "冲", "崇", "宠", "虫", "重"],
        ["chou", "抽", "酬", "畴", "踌", "稠", "愁", "筹", "仇", "绸", "瞅", "丑", "臭"],
        ["chu", "初", "出", "橱", "厨", "躇", "锄", "雏", "滁", "除", "楚", "础", "储", "矗", "搐", "触", "处", "畜"],
        ["chuai", "揣"],
        ["chuan", "川", "穿", "椽", "船", "喘", "串", "传"],
        ["chuang", "疮", "窗", "床", "闯", "创"],
        ["chui", "吹", "炊", "捶", "锤", "垂", "椎"],
        ["chun", "春", "椿", "醇", "唇", "淳", "纯", "蠢"],
        ["chuo", "戳", "绰"],
        ["ci", "疵", "茨", "磁", "雌", "辞", "慈", "瓷", "词", "此", "刺", "赐", "次", "伺", "兹", "差"],
        ["cong", "聪", "葱", "囱", "匆", "从", "丛"],
        ["cou", "凑"],
        ["cu", "粗", "醋", "簇", "促", "卒"],
        ["cuan", "蹿", "篡", "窜", "攒"],
        ["cui", "摧", "崔", "催", "脆", "瘁", "粹", "淬", "翠"],
        ["cun", "村", "存", "寸"],
        ["cuo", "磋", "搓", "措", "挫", "错", "撮"],
        ["da", "搭", "达", "答", "瘩", "打", "大"],
        ["dai", "歹", "傣", "戴", "带", "殆", "代", "贷", "袋", "待", "逮", "怠", "大", "呆"],
        ["dan", "耽", "担", "丹", "郸", "胆", "旦", "氮", "但", "惮", "淡", "诞", "蛋", "掸", "弹", "石", "单"],
        ["dang", "当", "挡", "党", "荡", "档"],
        ["dao", "刀", "捣", "蹈", "倒", "岛", "祷", "导", "到", "稻", "悼", "道", "盗"],
        ["de", "德", "得", "的", "地"],
        ["dei"],
        ["deng", "蹬", "灯", "登", "等", "瞪", "凳", "邓", "澄"],
        ["di", "低", "滴", "迪", "敌", "笛", "狄", "涤", "嫡", "抵", "蒂", "第", "帝", "弟", "递", "缔", "的", "堤", "翟",
            "底", "地", "提"],
        ["dian", "颠", "掂", "滇", "碘", "点", "典", "靛", "垫", "电", "甸", "店", "惦", "奠", "淀", "殿", "佃"],
        ["diao", "碉", "叼", "雕", "凋", "刁", "掉", "吊", "钓", "调"],
        ["die", "跌", "爹", "碟", "蝶", "迭", "谍", "叠"],
        ["ding", "盯", "叮", "钉", "顶", "鼎", "锭", "定", "订", "丁"],
        ["diu", "丢"],
        ["dong", "东", "冬", "董", "懂", "动", "栋", "冻", "洞", "侗", "恫"],
        ["dou", "兜", "抖", "斗", "陡", "豆", "逗", "痘", "都"],
        ["du", "督", "毒", "犊", "独", "堵", "睹", "赌", "杜", "镀", "肚", "渡", "妒", "都", "读", "度"],
        ["duan", "端", "短", "锻", "段", "断", "缎"],
        ["dui", "兑", "队", "对", "堆"],
        ["dun", "墩", "吨", "钝", "遁", "蹲", "敦", "顿", "囤", "盾"],
        ["duo", "掇", "哆", "多", "夺", "垛", "躲", "朵", "跺", "剁", "惰", "度", "舵", "堕"],
        ["e", "峨", "鹅", "俄", "额", "讹", "娥", "厄", "扼", "遏", "鄂", "饿", "阿", "蛾", "恶", "哦"],
        ["en", "恩"],
        ["er", "而", "耳", "尔", "饵", "洱", "二", "贰", "儿"],
        ["fa", "发", "罚", "筏", "伐", "乏", "阀", "法", "珐"],
        ["fan", "藩", "帆", "翻", "樊", "矾", "钒", "凡", "烦", "反", "返", "范", "贩", "犯", "饭", "泛", "番", "繁"],
        ["fang", "坊", "芳", "方", "肪", "房", "防", "妨", "仿", "访", "纺", "放"],
        ["fei", "菲", "非", "啡", "飞", "肥", "匪", "诽", "吠", "肺", "废", "沸", "费"],
        ["fen", "芬", "酚", "吩", "氛", "分", "纷", "坟", "焚", "汾", "粉", "奋", "份", "忿", "愤", "粪"],
        ["feng", "丰", "封", "枫", "蜂", "峰", "锋", "风", "疯", "烽", "逢", "缝", "讽", "奉", "凤", "冯"],
        ["fo", "佛"],
        ["fou", "否"],
        ["fu", "夫", "敷", "肤", "孵", "扶", "辐", "幅", "氟", "符", "伏", "俘", "服", "浮", "涪", "福", "袱", "弗", "甫",
            "抚", "辅", "俯", "釜", "斧", "腑", "府", "腐", "赴", "副", "覆", "赋", "复", "傅", "付", "阜", "父", "腹", "负",
            "富", "讣", "附", "妇", "缚", "咐", "佛", "拂", "脯"],
        ["ga", "噶", "嘎", "夹", "咖"],
        ["gai", "该", "改", "概", "钙", "溉", "盖", "芥"],
        ["gan", "干", "甘", "杆", "柑", "竿", "肝", "赶", "感", "秆", "敢", "赣", "乾"],
        ["gang", "冈", "刚", "钢", "缸", "肛", "纲", "岗", "港", "杠", "扛"],
        ["gao", "篙", "皋", "高", "膏", "羔", "糕", "搞", "稿", "镐", "告"],
        ["ge", "哥", "歌", "搁", "戈", "鸽", "疙", "割", "葛", "格", "阁", "隔", "铬", "个", "各", "胳", "革", "蛤", "咯"],
        ["gei", "给"],
        ["gen", "根", "跟"],
        ["geng", "耕", "更", "庚", "羹", "埂", "耿", "梗", "粳", "颈"],
        ["gong", "工", "攻", "功", "恭", "龚", "供", "躬", "公", "宫", "弓", "巩", "拱", "贡", "共", "汞"],
        ["gou", "钩", "勾", "沟", "苟", "狗", "垢", "构", "购", "够"],
        ["gu", "辜", "菇", "咕", "箍", "估", "沽", "孤", "姑", "古", "蛊", "骨", "股", "故", "顾", "固", "雇", "鼓", "谷",
            "贾"],
        ["gua", "刮", "瓜", "剐", "寡", "挂", "褂"],
        ["guai", "乖", "拐", "怪"],
        ["guan", "棺", "关", "官", "冠", "观", "管", "馆", "罐", "惯", "灌", "贯", "纶"],
        ["guang", "光", "逛", "广"],
        ["gui", "瑰", "规", "圭", "归", "闺", "轨", "鬼", "诡", "癸", "桂", "柜", "跪", "贵", "刽", "硅", "傀", "炔", "龟"],
        ["gun", "辊", "滚", "棍"],
        ["guo", "锅", "郭", "国", "果", "裹", "过", "涡"],
        ["ha", "蛤", "哈"],
        ["hai", "骸", "孩", "海", "氦", "亥", "害", "骇", "还", "咳"],
        ["han", "酣", "憨", "邯", "韩", "含", "涵", "寒", "函", "喊", "罕", "翰", "撼", "捍", "旱", "憾", "悍", "焊", "汗",
            "汉"],
        ["hang", "杭", "航", "夯", "吭", "巷", "行"],
        ["hao", "壕", "嚎", "豪", "毫", "郝", "好", "耗", "号", "浩", "镐", "貉"],
        ["he", "喝", "荷", "菏", "禾", "何", "盒", "阂", "河", "赫", "褐", "鹤", "贺", "核", "合", "涸", "吓", "呵", "貉",
            "和"],
        ["hei", "黑", "嘿"],
        ["hen", "痕", "很", "狠", "恨"],
        ["heng", "亨", "横", "衡", "恒", "哼", "行"],
        ["hong", "轰", "哄", "烘", "虹", "鸿", "洪", "宏", "弘", "红"],
        ["hou", "喉", "侯", "猴", "吼", "厚", "候", "后"],
        ["hu", "呼", "乎", "忽", "瑚", "壶", "葫", "胡", "蝴", "狐", "糊", "湖", "弧", "虎", "护", "互", "沪", "户", "唬",
            "和"],
        ["hua", "花", "华", "猾", "画", "化", "话", "哗", "滑", "划"],
        ["huai", "槐", "怀", "淮", "徊", "坏"],
        ["huan", "欢", "环", "桓", "缓", "换", "患", "唤", "痪", "豢", "焕", "涣", "宦", "幻", "还"],
        ["huang", "荒", "慌", "黄", "磺", "蝗", "簧", "皇", "凰", "惶", "煌", "晃", "幌", "恍", "谎"],
        ["hui", "灰", "挥", "辉", "徽", "恢", "蛔", "回", "毁", "悔", "慧", "卉", "惠", "晦", "贿", "秽", "烩", "汇", "讳",
            "诲", "绘", "会"],
        ["hun", "昏", "婚", "魂", "浑", "混", "荤"],
        ["huo", "活", "伙", "火", "获", "或", "惑", "霍", "货", "祸", "豁", "和"],
        ["ji", "击", "圾", "基", "机", "畸", "积", "箕", "肌", "饥", "迹", "激", "讥", "鸡", "姬", "绩", "吉", "极", "棘",
            "辑", "籍", "集", "及", "急", "疾", "汲", "即", "嫉", "级", "挤", "几", "脊", "己", "蓟", "技", "冀", "季", "伎",
            "剂", "悸", "济", "寄", "寂", "计", "记", "既", "忌", "际", "妓", "继", "纪", "给", "稽", "缉", "祭", "藉", "期",
            "奇", "齐", "系"],
        ["jia", "嘉", "枷", "佳", "加", "荚", "颊", "甲", "钾", "假", "稼", "架", "驾", "嫁", "夹", "贾", "价", "搅", "茄",
            "缴", "家"],
        ["jian", "歼", "监", "坚", "尖", "笺", "间", "煎", "兼", "肩", "艰", "奸", "缄", "茧", "检", "柬", "碱", "拣", "捡",
            "简", "俭", "剪", "减", "荐", "鉴", "践", "贱", "键", "箭", "件", "健", "舰", "剑", "饯", "渐", "溅", "涧", "建",
            "槛", "见", "浅"],
        ["jiang", "僵", "姜", "浆", "江", "疆", "蒋", "桨", "奖", "讲", "匠", "酱", "将", "降", "强"],
        ["jiao", "椒", "礁", "焦", "胶", "交", "郊", "浇", "骄", "娇", "脚", "教", "轿", "较", "叫", "窖", "蕉", "嚼", "搅",
            "铰", "狡", "饺", "绞", "酵", "觉", "校", "矫", "侥", "角", "缴", "剿"],
        ["jie", "揭", "接", "皆", "秸", "街", "阶", "截", "劫", "节", "杰", "捷", "睫", "竭", "洁", "结", "姐", "戒", "界",
            "借", "介", "疥", "诫", "届", "桔", "解", "藉", "芥"],
        ["jin", "巾", "筋", "斤", "金", "今", "津", "襟", "紧", "锦", "仅", "谨", "进", "靳", "晋", "禁", "近", "烬", "浸",
            "尽", "劲"],
        ["jing", "荆", "兢", "茎", "睛", "晶", "鲸", "京", "惊", "精", "经", "井", "警", "静", "境", "敬", "镜", "径", "痉",
            "靖", "竟", "竞", "净", "劲", "粳", "景", "颈"],
        ["jiong", "炯", "窘"],
        ["jiu", "揪", "究", "纠", "玖", "韭", "久", "灸", "九", "酒", "厩", "救", "旧", "臼", "舅", "咎", "就", "疚"],
        ["ju", "鞠", "拘", "狙", "疽", "驹", "菊", "局", "矩", "举", "沮", "聚", "拒", "据", "巨", "具", "距", "踞", "锯",
            "俱", "惧", "炬", "剧", "车", "桔", "居", "咀", "句", "蛆", "足"],
        ["juan", "捐", "鹃", "娟", "倦", "眷", "绢", "卷", "圈"],
        ["jue", "撅", "攫", "抉", "掘", "倔", "爵", "决", "诀", "绝", "嚼", "觉", "角"],
        ["jun", "菌", "钧", "军", "君", "峻", "俊", "竣", "郡", "骏", "均", "浚"],
        ["ka", "喀", "咖", "卡", "咯"],
        ["kai", "开", "揩", "凯", "慨", "楷"],
        ["kan", "刊", "堪", "勘", "坎", "砍", "看", "槛", "嵌"],
        ["kang", "康", "慷", "糠", "抗", "亢", "炕", "扛"],
        ["kao", "考", "拷", "烤", "靠"],
        ["ke", "坷", "苛", "柯", "棵", "磕", "颗", "科", "可", "渴", "克", "刻", "客", "课", "壳", "呵", "咳"],
        ["ken", "肯", "啃", "垦", "恳"],
        ["keng", "坑", "吭"],
        ["kong", "空", "恐", "孔", "控"],
        ["kou", "抠", "口", "扣", "寇"],
        ["ku", "枯", "哭", "窟", "苦", "酷", "库", "裤"],
        ["kua", "夸", "垮", "挎", "跨", "胯"],
        ["kuai", "块", "筷", "侩", "快", "会"],
        ["kuan", "宽", "款"],
        ["kuang", "匡", "筐", "狂", "框", "矿", "眶", "旷", "况"],
        ["kui", "亏", "盔", "岿", "窥", "葵", "奎", "魁", "馈", "愧", "傀", "溃"],
        ["kun", "坤", "昆", "捆", "困"],
        ["kuo", "扩", "廓", "阔", "括"],
        ["la", "垃", "拉", "喇", "辣", "啦", "蜡", "腊", "落"],
        ["lai", "莱", "来", "赖"],
        ["lan", "婪", "栏", "拦", "篮", "阑", "兰", "澜", "谰", "揽", "览", "懒", "缆", "烂", "滥", "蓝"],
        ["lang", "琅", "榔", "狼", "廊", "郎", "朗", "浪"],
        ["lao", "捞", "劳", "牢", "老", "佬", "涝", "姥", "酪", "烙", "潦", "落"],
        ["le", "勒", "乐", "肋", "了"],
        ["lei", "雷", "镭", "蕾", "磊", "累", "儡", "垒", "擂", "类", "泪", "勒", "肋"],
        ["leng", "楞", "冷", "棱"],
        ["li", "厘", "梨", "犁", "黎", "篱", "狸", "离", "漓", "理", "李", "里", "鲤", "礼", "莉", "荔", "吏", "栗", "丽",
            "厉", "励", "砾", "历", "利", "例", "俐", "痢", "立", "粒", "沥", "隶", "力", "璃", "哩"],
        ["lia"],
        ["lian", "联", "莲", "连", "镰", "廉", "涟", "帘", "敛", "脸", "链", "恋", "炼", "练", "怜"],
        ["liang", "粮", "凉", "梁", "粱", "良", "两", "辆", "量", "晾", "亮", "谅", "俩"],
        ["liao", "撩", "聊", "僚", "疗", "燎", "寥", "辽", "撂", "镣", "廖", "料", "潦", "了"],
        ["lie", "列", "裂", "烈", "劣", "猎"],
        ["lin", "琳", "林", "磷", "霖", "临", "邻", "鳞", "淋", "凛", "赁", "吝", "拎"],
        ["ling", "玲", "菱", "零", "龄", "铃", "伶", "羚", "凌", "灵", "陵", "岭", "领", "另", "令", "棱", "怜"],
        ["liu", "溜", "琉", "榴", "硫", "馏", "留", "刘", "瘤", "流", "柳", "六", "陆"],
        ["long", "龙", "聋", "咙", "笼", "窿", "隆", "垄", "拢", "陇", "弄"],
        ["lou", "楼", "娄", "搂", "篓", "漏", "陋", "露"],
        ["lu", "芦", "卢", "颅", "庐", "炉", "掳", "卤", "虏", "鲁", "麓", "路", "赂", "鹿", "潞", "禄", "录", "戮", "吕",
            "六", "碌", "露", "陆", "绿"],
        ["lv", "驴", "铝", "侣", "旅", "履", "屡", "缕", "虑", "氯", "律", "滤", "绿", "率"],
        ["lve", "掠", "略"],
        ["luan", "峦", "挛", "孪", "滦", "卵", "乱"],
        ["lun", "抡", "轮", "伦", "仑", "沦", "论", "纶"],
        ["luo", "萝", "螺", "罗", "逻", "锣", "箩", "骡", "裸", "洛", "骆", "烙", "络", "落", "咯"],
        ["ma", "妈", "麻", "玛", "码", "蚂", "马", "骂", "嘛", "吗", "摩", "抹", "么"],
        ["mai", "买", "麦", "卖", "迈", "埋", "脉"],
        ["man", "瞒", "馒", "蛮", "满", "曼", "慢", "漫", "谩", "埋", "蔓"],
        ["mang", "茫", "盲", "氓", "忙", "莽", "芒"],
        ["mao", "猫", "茅", "锚", "毛", "矛", "铆", "卯", "茂", "帽", "貌", "贸", "冒"],
        ["me", "么"],
        ["mei", "玫", "枚", "梅", "酶", "霉", "煤", "眉", "媒", "镁", "每", "美", "昧", "寐", "妹", "媚", "没", "糜"],
        ["men", "门", "闷", "们"],
        ["meng", "萌", "蒙", "檬", "锰", "猛", "梦", "孟", "盟"],
        ["mi", "眯", "醚", "靡", "迷", "弥", "米", "觅", "蜜", "密", "幂", "糜", "谜", "泌", "秘"],
        ["mian", "棉", "眠", "绵", "冕", "免", "勉", "缅", "面", "娩"],
        ["miao", "苗", "描", "瞄", "藐", "秒", "渺", "庙", "妙"],
        ["mie", "蔑", "灭"],
        ["min", "民", "抿", "皿", "敏", "悯", "闽"],
        ["ming", "明", "螟", "鸣", "铭", "名", "命"],
        ["miu", "谬"],
        ["mo", "摸", "摹", "蘑", "膜", "磨", "魔", "末", "莫", "墨", "默", "沫", "漠", "寞", "陌", "脉", "没", "模", "摩",
            "抹"],
        ["mou", "谋", "某", "牟"],
        ["mu", "拇", "牡", "亩", "姆", "母", "墓", "暮", "幕", "募", "慕", "木", "目", "睦", "牧", "穆", "姥", "模", "牟"],
        ["na", "拿", "钠", "纳", "呐", "那", "娜", "哪"],
        ["nai", "氖", "乃", "奶", "耐", "奈", "哪"],
        ["nan", "南", "男", "难"],
        ["nang", "囊"],
        ["nao", "挠", "脑", "恼", "闹", "淖"],
        ["ne", "呢", "哪"],
        ["nei", "馁", "内", "那", "哪"],
        ["nen", "嫩"],
        ["neng", "能"],
        ["ni", "妮", "霓", "倪", "泥", "尼", "拟", "你", "匿", "腻", "逆", "溺", "呢"],
        ["nian", "蔫", "拈", "年", "碾", "撵", "捻", "念", "粘"],
        ["niang", "娘", "酿"],
        ["niao", "鸟", "尿"],
        ["nie", "捏", "聂", "孽", "啮", "镊", "镍", "涅"],
        ["nin", "您"],
        ["ning", "柠", "狞", "凝", "宁", "拧", "泞"],
        ["niu", "牛", "扭", "钮", "纽"],
        ["nong", "脓", "浓", "农", "弄"],
        ["nu", "奴", "怒", "努"],
        ["nv", "女"],
        ["nve", "虐", "疟"],
        ["nuan", "暖"],
        ["nuo", "挪", "懦", "糯", "诺", "娜"],
        ["o", "哦"],
        ["ou", "欧", "鸥", "殴", "藕", "呕", "偶", "沤", "区"],
        ["pa", "啪", "趴", "爬", "帕", "怕", "扒", "耙", "琶"],
        ["pai", "拍", "排", "牌", "徘", "湃", "派", "迫"],
        ["pan", "攀", "潘", "盘", "磐", "盼", "畔", "判", "叛", "番", "胖", "般"],
        ["pang", "乓", "庞", "耪", "膀", "磅", "旁", "胖"],
        ["pao", "抛", "咆", "袍", "跑", "泡", "刨", "炮"],
        ["pei", "呸", "胚", "培", "裴", "赔", "陪", "配", "佩", "沛", "坏"],
        ["pen", "喷", "盆"],
        ["peng", "砰", "抨", "烹", "澎", "彭", "蓬", "棚", "硼", "篷", "膨", "朋", "鹏", "捧", "碰"],
        ["pi", "坯", "砒", "霹", "批", "披", "劈", "琵", "毗", "啤", "脾", "疲", "皮", "痞", "僻", "屁", "譬", "辟", "否",
            "匹", "坏"],
        ["pian", "篇", "偏", "片", "骗", "扁", "便"],
        ["piao", "飘", "漂", "瓢", "票", "朴"],
        ["pie", "撇", "瞥"],
        ["pin", "拼", "频", "贫", "品", "聘"],
        ["ping", "乒", "坪", "萍", "平", "凭", "瓶", "评", "苹", "屏"],
        ["po", "坡", "泼", "颇", "婆", "破", "粕", "泊", "迫", "魄", "朴"],
        ["pou", "剖"],
        ["pu", "扑", "铺", "仆", "莆", "葡", "菩", "蒲", "圃", "普", "浦", "谱", "脯", "埔", "曝", "瀑", "堡", "朴"],
        ["qi", "欺", "戚", "妻", "七", "凄", "柒", "沏", "棋", "歧", "崎", "脐", "旗", "祈", "祁", "骑", "起", "岂", "乞",
            "企", "启", "器", "气", "迄", "弃", "汽", "讫", "稽", "缉", "期", "栖", "其", "奇", "畦", "齐", "砌", "泣", "漆",
            "契"],
        ["qia", "掐", "卡", "洽"],
        ["qian", "牵", "扦", "钎", "千", "迁", "签", "仟", "谦", "黔", "钱", "钳", "前", "潜", "遣", "谴", "堑", "欠", "歉",
            "铅", "乾", "浅", "嵌", "纤"],
        ["qiang", "枪", "呛", "腔", "羌", "墙", "蔷", "抢", "强"],
        ["qiao", "锹", "敲", "悄", "桥", "乔", "侨", "巧", "撬", "翘", "峭", "俏", "窍", "壳", "橇", "瞧", "鞘", "雀"],
        ["qie", "切", "窃", "砌", "茄", "且", "怯"],
        ["qin", "钦", "侵", "秦", "琴", "勤", "芹", "擒", "禽", "寝", "亲", "沁"],
        ["qing", "青", "轻", "氢", "倾", "卿", "清", "擎", "晴", "氰", "情", "顷", "请", "庆", "亲"],
        ["qiong", "琼", "穷"],
        ["qiu", "秋", "丘", "邱", "球", "求", "囚", "酋", "泅"],
        ["qu", "趋", "曲", "躯", "屈", "驱", "渠", "取", "娶", "龋", "去", "区", "蛆", "趣"],
        ["quan", "颧", "权", "醛", "泉", "全", "痊", "拳", "犬", "券", "劝", "卷", "圈"],
        ["que", "缺", "瘸", "却", "鹊", "榷", "确", "炔", "雀"],
        ["qun", "裙", "群"],
        ["ran", "然", "燃", "冉", "染"],
        ["rang", "瓤", "壤", "攘", "嚷", "让"],
        ["rao", "饶", "扰", "绕"],
        ["re", "惹", "热"],
        ["ren", "壬", "仁", "人", "忍", "韧", "任", "认", "刃", "妊", "纫"],
        ["reng", "扔", "仍"],
        ["ri", "日"],
        ["rong", "戎", "茸", "蓉", "荣", "融", "熔", "溶", "容", "绒", "冗"],
        ["rou", "揉", "柔", "肉"],
        ["ru", "茹", "儒", "孺", "如", "辱", "乳", "汝", "入", "褥", "蠕"],
        ["ruan", "软", "阮"],
        ["rui", "蕊", "瑞", "锐"],
        ["run", "闰", "润"],
        ["ruo", "弱", "若"],
        ["sa", "撒", "洒", "萨"],
        ["sai", "腮", "鳃", "赛", "塞"],
        ["san", "三", "叁", "伞", "散"],
        ["sang", "桑", "嗓", "丧"],
        ["sao", "搔", "骚", "扫", "嫂", "梢"],
        ["se", "瑟", "涩", "塞", "色"],
        ["sen", "森"],
        ["seng", "僧"],
        ["sha", "砂", "杀", "沙", "纱", "傻", "啥", "煞", "莎", "刹", "杉", "厦"],
        ["shai", "筛", "晒", "色"],
        ["shan", "珊", "苫", "山", "删", "煽", "衫", "闪", "陕", "擅", "赡", "膳", "善", "汕", "扇", "缮", "杉", "栅", "掺",
            "单"],
        ["shang", "墒", "伤", "商", "赏", "晌", "上", "尚", "裳", "汤"],
        ["shao", "捎", "稍", "烧", "芍", "勺", "韶", "少", "哨", "邵", "绍", "鞘", "梢", "召"],
        ["she", "奢", "赊", "舌", "舍", "赦", "摄", "慑", "涉", "社", "设", "蛇", "拾", "折", "射"],
        ["shei"],
        ["shen", "砷", "申", "呻", "伸", "身", "深", "绅", "神", "审", "婶", "肾", "慎", "渗", "沈", "甚", "参", "娠",
            "什"],
        ["sheng", "声", "生", "甥", "牲", "升", "绳", "剩", "胜", "圣", "乘", "省", "盛"],
        ["shi", "师", "失", "狮", "施", "湿", "诗", "尸", "虱", "十", "时", "蚀", "实", "史", "矢", "使", "屎", "驶", "始",
            "式", "示", "士", "世", "柿", "事", "拭", "誓", "逝", "势", "是", "嗜", "噬", "适", "仕", "侍", "释", "饰", "市",
            "恃", "室", "视", "试", "匙", "石", "拾", "食", "识", "氏", "似", "嘘", "殖", "峙", "什"],
        ["shou", "收", "手", "首", "守", "寿", "授", "售", "受", "瘦", "兽", "熟"],
        ["shu", "蔬", "枢", "梳", "殊", "抒", "输", "叔", "舒", "淑", "疏", "书", "赎", "孰", "薯", "暑", "曙", "署", "蜀",
            "黍", "鼠", "述", "树", "束", "戍", "竖", "墅", "庶", "漱", "恕", "熟", "属", "术", "数"],
        ["shua", "刷", "耍"],
        ["shuai", "摔", "甩", "帅", "衰", "率"],
        ["shuan", "栓", "拴"],
        ["shuang", "霜", "双", "爽"],
        ["shui", "水", "睡", "税", "谁", "说"],
        ["shun", "吮", "瞬", "顺", "舜"],
        ["shuo", "硕", "朔", "烁", "数", "说"],
        ["si", "斯", "撕", "嘶", "私", "司", "丝", "死", "肆", "寺", "嗣", "四", "饲", "巳", "食", "思", "伺", "似"],
        ["song", "松", "耸", "怂", "颂", "送", "宋", "讼", "诵"],
        ["sou", "搜", "擞", "嗽", "艘"],
        ["su", "苏", "酥", "俗", "素", "速", "粟", "僳", "塑", "溯", "诉", "肃", "宿", "缩"],
        ["suan", "酸", "蒜", "算"],
        ["sui", "虽", "隋", "随", "绥", "髓", "碎", "岁", "穗", "遂", "隧", "祟", "尿"],
        ["sun", "孙", "损", "笋"],
        ["suo", "蓑", "梭", "唆", "琐", "索", "锁", "所", "莎", "缩"],
        ["ta", "塌", "他", "它", "她", "獭", "挞", "蹋", "踏", "塔", "拓"],
        ["tai", "胎", "苔", "抬", "台", "泰", "酞", "太", "态", "汰"],
        ["tan", "坍", "摊", "贪", "瘫", "滩", "坛", "檀", "痰", "潭", "谭", "谈", "坦", "毯", "袒", "碳", "探", "叹", "炭",
            "弹"],
        ["tang", "塘", "搪", "堂", "棠", "膛", "唐", "糖", "躺", "淌", "趟", "烫", "敞", "汤", "倘"],
        ["tao", "掏", "涛", "滔", "绦", "萄", "桃", "逃", "淘", "讨", "套", "陶"],
        ["te", "特"],
        ["teng", "藤", "腾", "疼", "誊"],
        ["ti", "梯", "剔", "踢", "锑", "题", "蹄", "啼", "体", "替", "嚏", "惕", "涕", "剃", "屉", "提"],
        ["tian", "天", "添", "填", "田", "甜", "恬", "舔", "腆", "蚕"],
        ["tiao", "挑", "条", "迢", "眺", "跳", "调"],
        ["tie", "贴", "铁", "帖"],
        ["ting", "厅", "烃", "汀", "廷", "停", "亭", "庭", "挺", "艇", "听"],
        ["tong", "通", "桐", "酮", "瞳", "同", "铜", "彤", "童", "桶", "捅", "筒", "统", "痛", "侗", "恫"],
        ["tou", "偷", "投", "头", "透"],
        ["tu", "秃", "突", "图", "徒", "途", "涂", "屠", "土", "吐", "兔", "凸", "余"],
        ["tuan", "湍", "团"],
        ["tui", "推", "颓", "腿", "蜕", "退", "褪"],
        ["tun", "吞", "屯", "臀", "囤"],
        ["tuo", "拖", "托", "脱", "鸵", "陀", "驼", "椭", "妥", "唾", "驮", "拓"],
        ["wa", "挖", "哇", "蛙", "洼", "娃", "瓦", "袜"],
        ["wai", "歪", "外"],
        ["wan", "豌", "弯", "湾", "玩", "顽", "丸", "烷", "完", "碗", "挽", "晚", "惋", "婉", "腕", "蔓", "皖", "宛", "万"],
        ["wang", "汪", "王", "枉", "网", "往", "旺", "望", "忘", "妄", "亡"],
        ["wei", "威", "巍", "微", "危", "韦", "违", "桅", "围", "唯", "惟", "为", "潍", "维", "苇", "萎", "委", "伟", "伪",
            "纬", "未", "味", "畏", "胃", "喂", "魏", "位", "渭", "谓", "慰", "卫", "尾", "蔚", "尉"],
        ["wen", "瘟", "温", "蚊", "文", "闻", "纹", "吻", "稳", "紊", "问"],
        ["weng", "嗡", "翁", "瓮"],
        ["wo", "挝", "蜗", "窝", "我", "斡", "卧", "握", "沃", "涡"],
        ["wu", "巫", "呜", "钨", "乌", "污", "诬", "屋", "芜", "梧", "吾", "吴", "毋", "武", "五", "捂", "午", "舞", "伍",
            "侮", "坞", "戊", "雾", "晤", "物", "勿", "务", "悟", "误", "恶", "无"],
        ["xi", "昔", "熙", "析", "西", "硒", "矽", "晰", "嘻", "吸", "锡", "牺", "稀", "息", "希", "悉", "膝", "夕", "惜",
            "熄", "烯", "汐", "犀", "檄", "袭", "席", "习", "媳", "喜", "隙", "细", "栖", "溪", "铣", "洗", "系", "戏"],
        ["xia", "瞎", "匣", "霞", "辖", "暇", "峡", "侠", "狭", "下", "虾", "厦", "夏", "吓"],
        ["xian", "掀", "锨", "先", "仙", "鲜", "咸", "贤", "衔", "舷", "闲", "涎", "弦", "嫌", "显", "险", "现", "献", "县",
            "腺", "馅", "羡", "宪", "陷", "限", "线", "铣", "纤"],
        ["xiang", "相", "厢", "镶", "香", "箱", "襄", "湘", "乡", "翔", "祥", "详", "想", "响", "享", "项", "橡", "像",
            "向", "象", "降", "巷"],
        ["xiao", "萧", "硝", "霄", "哮", "销", "消", "宵", "晓", "小", "孝", "肖", "啸", "笑", "效", "削", "嚣", "淆",
            "校"],
        ["xie", "楔", "些", "歇", "鞋", "协", "携", "胁", "谐", "写", "械", "卸", "蟹", "懈", "泄", "泻", "谢", "屑", "解",
            "蝎", "挟", "邪", "斜", "血", "叶", "契"],
        ["xin", "薪", "芯", "锌", "欣", "辛", "新", "忻", "心", "衅", "信"],
        ["xing", "星", "腥", "猩", "惺", "兴", "刑", "型", "形", "邢", "醒", "幸", "杏", "性", "姓", "省", "行"],
        ["xiong", "兄", "凶", "胸", "匈", "汹", "雄", "熊"],
        ["xiu", "休", "修", "羞", "朽", "嗅", "锈", "秀", "袖", "绣", "臭", "宿"],
        ["xu", "墟", "需", "虚", "须", "徐", "许", "蓄", "酗", "叙", "旭", "序", "恤", "絮", "婿", "绪", "续", "戌", "嘘",
            "畜", "吁"],
        ["xuan", "轩", "喧", "宣", "悬", "旋", "玄", "选", "癣", "眩", "绚"],
        ["xue", "靴", "薛", "学", "穴", "雪", "削", "血"],
        ["xun", "勋", "熏", "循", "旬", "询", "驯", "巡", "殉", "汛", "训", "讯", "逊", "迅", "浚", "寻"],
        ["ya", "压", "押", "鸦", "鸭", "呀", "丫", "牙", "蚜", "衙", "涯", "雅", "哑", "亚", "讶", "芽", "崖", "轧"],
        ["yan", "焉", "阉", "淹", "盐", "严", "研", "蜒", "岩", "延", "言", "颜", "阎", "炎", "沿", "奄", "掩", "眼", "衍",
            "演", "艳", "堰", "燕", "厌", "砚", "雁", "唁", "彦", "焰", "宴", "谚", "验", "铅", "咽", "烟", "殷"],
        ["yang", "殃", "央", "鸯", "秧", "杨", "扬", "佯", "疡", "羊", "洋", "阳", "氧", "仰", "痒", "养", "样", "漾"],
        ["yao", "邀", "腰", "妖", "瑶", "摇", "尧", "遥", "窑", "谣", "姚", "咬", "舀", "药", "要", "耀", "约", "钥", "侥"],
        ["ye", "椰", "噎", "耶", "爷", "野", "冶", "也", "页", "业", "夜", "咽", "掖", "叶", "腋", "液", "拽", "曳"],
        ["yi", "一", "壹", "医", "揖", "铱", "依", "伊", "衣", "颐", "夷", "移", "仪", "胰", "疑", "沂", "宜", "姨", "彝",
            "椅", "蚁", "倚", "已", "乙", "矣", "以", "艺", "抑", "易", "邑", "亿", "役", "臆", "逸", "肄", "疫", "亦", "裔",
            "意", "毅", "忆", "义", "益", "溢", "诣", "议", "谊", "译", "异", "翼", "翌", "绎", "遗", "屹"],
        ["yin", "茵", "荫", "因", "音", "阴", "姻", "吟", "银", "淫", "寅", "饮", "尹", "引", "隐", "印", "殷"],
        ["ying", "英", "樱", "婴", "鹰", "应", "缨", "莹", "萤", "营", "荧", "蝇", "迎", "赢", "盈", "影", "颖", "硬",
            "映"],
        ["yo", "哟"],
        ["yong", "拥", "佣", "臃", "痈", "庸", "雍", "踊", "蛹", "咏", "泳", "永", "恿", "勇", "用", "涌"],
        ["you", "幽", "优", "悠", "忧", "尤", "由", "邮", "铀", "犹", "油", "游", "酉", "有", "友", "右", "佑", "釉", "诱",
            "又", "幼"],
        ["yu", "迂", "淤", "于", "盂", "榆", "虞", "愚", "舆", "逾", "鱼", "愉", "渝", "渔", "隅", "予", "娱", "雨", "与",
            "屿", "禹", "宇", "语", "羽", "玉", "域", "芋", "郁", "遇", "喻", "峪", "御", "愈", "欲", "狱", "誉", "浴", "寓",
            "裕", "预", "豫", "驭", "尉", "余", "俞", "吁", "育"],
        ["yuan", "鸳", "渊", "冤", "元", "垣", "袁", "原", "援", "辕", "园", "圆", "猿", "源", "缘", "远", "苑", "愿", "怨",
            "院", "员"],
        ["yue", "曰", "越", "跃", "岳", "粤", "月", "悦", "阅", "乐", "约", "钥"],
        ["yun", "耘", "云", "郧", "匀", "陨", "允", "运", "蕴", "酝", "晕", "韵", "孕", "均", "员"],
        ["za", "匝", "砸", "杂", "扎", "咱", "咋"],
        ["zai", "栽", "哉", "灾", "宰", "载", "再", "在", "仔"],
        ["zan", "暂", "赞", "攒", "咱"],
        ["zang", "赃", "脏", "葬", "藏"],
        ["zao", "遭", "糟", "藻", "枣", "早", "澡", "蚤", "躁", "噪", "造", "皂", "灶", "燥", "凿"],
        ["ze", "责", "则", "泽", "择", "侧", "咋"],
        ["zei", "贼"],
        ["zen", "怎"],
        ["zeng", "增", "憎", "赠", "曾", "综"],
        ["zha", "渣", "札", "铡", "闸", "眨", "榨", "乍", "炸", "诈", "查", "扎", "喳", "栅", "柞", "轧", "咋"],
        ["zhai", "斋", "债", "寨", "翟", "祭", "择", "摘", "宅", "窄", "侧"],
        ["zhan", "瞻", "毡", "詹", "沾", "盏", "斩", "辗", "崭", "展", "蘸", "栈", "占", "战", "站", "湛", "绽", "颤",
            "粘"],
        ["zhang", "樟", "章", "彰", "漳", "张", "掌", "涨", "杖", "丈", "帐", "账", "仗", "胀", "瘴", "障", "长"],
        ["zhao", "招", "昭", "找", "沼", "赵", "照", "罩", "兆", "肇", "朝", "召", "爪", "着"],
        ["zhe", "遮", "哲", "蛰", "辙", "者", "蔗", "浙", "折", "锗", "这", "着"],
        ["zhei"],
        ["zhen", "珍", "斟", "真", "甄", "砧", "臻", "贞", "针", "侦", "枕", "疹", "诊", "震", "振", "镇", "阵", "帧"],
        ["zheng", "蒸", "挣", "睁", "征", "狰", "争", "怔", "整", "拯", "正", "政", "症", "郑", "证"],
        ["zhi", "芝", "支", "蜘", "知", "肢", "脂", "汁", "之", "织", "职", "直", "植", "执", "值", "侄", "址", "指", "止",
            "趾", "只", "旨", "纸", "志", "挚", "掷", "至", "致", "置", "帜", "制", "智", "秩", "稚", "质", "炙", "痔", "滞",
            "治", "窒", "识", "枝", "吱", "殖", "峙"],
        ["zhong", "中", "盅", "忠", "钟", "衷", "终", "肿", "仲", "众", "种", "重"],
        ["zhou", "舟", "周", "州", "洲", "诌", "轴", "肘", "帚", "咒", "皱", "宙", "昼", "骤", "粥"],
        ["zhu", "珠", "株", "蛛", "朱", "猪", "诸", "诛", "逐", "竹", "烛", "煮", "拄", "瞩", "嘱", "主", "柱", "助", "蛀",
            "贮", "铸", "筑", "住", "注", "祝", "驻", "属", "著"],
        ["zhua", "抓", "爪"],
        ["zhuai", "拽"],
        ["zhuan", "专", "砖", "撰", "篆", "传", "转", "赚"],
        ["zhuang", "桩", "庄", "装", "妆", "壮", "状", "幢", "撞"],
        ["zhui", "锥", "追", "赘", "坠", "缀", "椎"],
        ["zhun", "谆", "准"],
        ["zhuo", "捉", "拙", "卓", "桌", "茁", "酌", "啄", "灼", "浊", "琢", "缴", "着"],
        ["zi", "咨", "资", "姿", "滋", "淄", "孜", "紫", "籽", "滓", "子", "自", "渍", "字", "吱", "兹", "仔"],
        ["zong", "鬃", "棕", "踪", "宗", "总", "纵", "综"],
        ["zou", "邹", "走", "奏", "揍"],
        ["zu", "租", "族", "祖", "诅", "阻", "组", "足", "卒"],
        ["zuan", "钻", "纂"],
        ["zui", "嘴", "醉", "最", "罪"],
        ["zun", "尊", "遵"],
        ["zuo", "昨", "左", "佐", "做", "作", "坐", "座", "撮", "琢", "柞"]
    ];
}
export class keyValue {
    key: ResourceStr = '';
    value: ResourceStr = '';
}
