export class Proxy {
    type: Type;
    host: string;
    port: number;
    constructor(type: Type, proxyHost?: string, proxyPort?: number) {
        this.type = type;
        this.host = proxyHost;
        this.port = proxyPort;
    }
    getType(): Type {
        return this.type;
    }
}
export enum Type {
    /**
     * Represents a direct connection, or the absence of a proxy.
     */
    DIRECT = 0,
    /**
     * Represents proxy for high level protocols such as HTTP or FTP.
     */
    HTTP = 1,
    /**
     * Represents a SOCKS (V4 or V5) proxy.
     */
    SOCKS = 2
}
