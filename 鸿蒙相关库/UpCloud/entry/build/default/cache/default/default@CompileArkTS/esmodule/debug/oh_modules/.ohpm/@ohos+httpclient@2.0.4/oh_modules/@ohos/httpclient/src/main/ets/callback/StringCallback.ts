import { Logger } from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/Logger&2.0.4";
import AbsCallback from "@normalized:N&&&@ohos/httpclient/src/main/ets/callback/AbsCallback&2.0.4";
class StringCallback extends AbsCallback {
    constructor() {
        super();
        Logger.info("httpclient- StringCallback constructor");
    }
    convertResponse(response) {
        Logger.info("httpclient- StringCallback convertResponse: " + response);
        if (typeof response === 'string' || response instanceof String) {
            return response;
        }
        return JSON.stringify(response);
    }
}
export default StringCallback;
