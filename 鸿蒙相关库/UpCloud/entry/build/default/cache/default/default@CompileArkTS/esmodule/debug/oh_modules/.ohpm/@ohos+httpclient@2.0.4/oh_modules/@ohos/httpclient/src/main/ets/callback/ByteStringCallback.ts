import { Logger } from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/Logger&2.0.4";
import AbsCallback from "@normalized:N&&&@ohos/httpclient/src/main/ets/callback/AbsCallback&2.0.4";
class ByteStringCallback extends AbsCallback {
    constructor() {
        super();
        Logger.info("httpclient- ByteArrayCallback constructor");
    }
    convertResponse(response) {
        var byteStringObj = "";
        return byteStringObj;
    }
}
export default ByteStringCallback;
