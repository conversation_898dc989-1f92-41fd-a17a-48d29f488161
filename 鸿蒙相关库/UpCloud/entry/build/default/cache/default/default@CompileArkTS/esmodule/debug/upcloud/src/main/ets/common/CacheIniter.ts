import type { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from "@normalized:N&&&@uplus/upcloud/src/main/ets/initer/builder/HttpClientBuilder&0.1.0";
import type { HttpIniter } from '../initer/HttpIniter';
import type common from "@ohos:app.ability.common";
import fs from "@ohos:file.fs";
import { UpCloudLog } from "@normalized:N&&&@uplus/upcloud/src/main/ets/UpCloudLog&0.1.0";
import { Cache } from "@normalized:N&&&@ohos/httpclient/index&2.0.4";
import { CacheInterceptor } from "@normalized:N&&&@uplus/upcloud/src/main/ets/common/CacheInterceptor&0.1.0";
export class CacheIniter implements HttpIniter<HttpClientBuilder> {
    private static readonly DEFAULT_WEAK_NETWORK_MAX_AGE_RATIO = 2.0;
    private static readonly DEFAULT_MAX_CACHE_SIZE = 100 * 1024 * 1024;
    private weakNetworkMaxAgeRatio: number;
    private maxCacheSize: number;
    private cacheFolder?: fs.File;
    constructor() {
        this.weakNetworkMaxAgeRatio = CacheIniter.DEFAULT_WEAK_NETWORK_MAX_AGE_RATIO;
        this.maxCacheSize = CacheIniter.DEFAULT_MAX_CACHE_SIZE;
    }
    initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: common.Context): HttpClientBuilder {
        UpCloudLog.debug(`CacheIniter.initialize called apiServer = ${apiServer.constructor.name}`);
        if (!builder) {
            builder = new HttpClientBuilder();
        }
        if (!this.cacheFolder) {
            this.cacheFolder = fs.openSync(context.cacheDir + "upcloud_" + apiServer.constructor.name, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        }
        return builder.cache(new Cache.Cache(this.cacheFolder.path, this.maxCacheSize, context))
            .addInterceptor(new CacheInterceptor(this.weakNetworkMaxAgeRatio));
    }
}
