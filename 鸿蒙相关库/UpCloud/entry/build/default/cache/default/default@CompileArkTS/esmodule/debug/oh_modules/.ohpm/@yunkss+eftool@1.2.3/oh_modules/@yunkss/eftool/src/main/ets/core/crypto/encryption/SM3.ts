import type { OutDTO } from '../../base/OutDTO';
import { CryptoUtil } from "@normalized:N&&&@yunkss/eftool/src/main/ets/core/util/CryptoUtil&1.2.3";
import { DynamicUtil } from "@normalized:N&&&@yunkss/eftool/src/main/ets/core/util/DynamicUtil&1.2.3";
export class SM3 {
    /**
     * SM3摘要
     * @param str 带摘要的字符串
     * @returns 摘要后的字符串
     */
    static async digest(str: string): Promise<OutDTO<string>> {
        return CryptoUtil.digest(str, 'SM3');
    }
    /**
     * 消息认证码计算
     * @param str  计算字符串
     * @returns
     */
    static async hmac(str: string): Promise<OutDTO<string>> {
        return DynamicUtil.hmac(str, 'SM3');
    }
}
