import type { Response } from '../response/Response';
export class CAResUtil {
    constructor() {
    }
    async getCA(ca, context): Promise<string> {
        if (!ca) {
            return "adapter getCA is error";
        }
        const value = await new Promise<Uint8Array>((resolve, reject) => {
            context.resourceManager.getRawFileContent(ca, (err: Response, value) => {
                if (err) {
                    reject(err);
                }
                resolve(value);
            });
        });
        const rawFile: Uint8Array = value;
        return this.parsingRawFile(rawFile);
    }
    private parsingRawFile(rawFile: Uint8Array): string {
        let fileContent: string = "";
        for (let index = 0, len = rawFile.length; index < len; index++) {
            const todo = rawFile[index];
            const item = String.fromCharCode(todo);
            fileContent += item + "";
        }
        return fileContent;
    }
}
