import dataStorage from "@ohos:data.storage";
import { Logger } from "@normalized:N&&&@ohos/httpclient/src/main/ets/utils/Logger&2.0.4";
function CookieStore(cacheDir) {
    var cookieJSON, path = null;
    this.path = cacheDir;
    Logger.info('httpclient- CookieStore path: ' + this.path);
}
CookieStore.prototype.setCacheDir = function setCacheDir(filePath) {
    this.path = filePath;
};
CookieStore.prototype.readCookie = function readCookie(hostname) {
    Logger.info('httpclient- CookieStore readCookie: ' + hostname);
    let storage = dataStorage.getStorageSync(this.path + '/cookiestore');
    let cookieJSON = storage.getSync(hostname, '');
    storage.flushSync();
    return cookieJSON;
};
CookieStore.prototype.writeCookie = function writeCookie(hostname, cookieJSON) {
    Logger.info('httpclient- CookieStore writeCookie: ' + hostname + ',cookieJSON:' + cookieJSO<PERSON> + ',path:' + this.path);
    let storage = dataStorage.getStorageSync(this.path + '/cookiestore');
    storage.putSync(hostname, cookieJSON);
    storage.flushSync();
};
export default CookieStore;
