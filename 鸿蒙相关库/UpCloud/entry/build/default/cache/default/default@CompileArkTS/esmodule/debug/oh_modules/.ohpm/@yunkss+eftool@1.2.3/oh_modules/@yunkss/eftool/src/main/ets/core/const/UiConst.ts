/**
 * <AUTHOR>
 * @DateTime 2024/1/24 19:35
 * @TODO UiConst  UI界面所需的常量
 */
export class UiConst {
    /**
     * 弹出提示类显示时间
     */
    static readonly ANIMATION_DURATION: number = 2000;
    /**
     * dialog标题
     */
    static readonly DIALOG_TITLE: string = '温馨提示';
    /**
     * alert标题
     */
    static readonly ALERT_TITLE: string = '警告提示';
    /**
     * action标题
     */
    static readonly ACTION_TITLE: string = '操作菜单';
    /**
     * dialog确定文字
     */
    static readonly DIALOG_OK: string = '确定';
    /**
     * dialog取消文字
     */
    static readonly DIALOG_CANCEL: string = '取消';
    /**
     * 主颜色
     */
    static readonly PRIMARY_COLOR: string = "#409eff";
    /**
     * dialog确定颜色
     */
    static readonly DIALOG_OK_COLOR: string = '#409eff';
    /**
     * alert 确定按钮文本颜色
     */
    static readonly ALERT_OK_COLOR: string = "#fff";
    /**
     * alert 确定按钮背景颜色
     */
    static readonly ALERT_OK_BG_COLOR: string = "#409eff";
    /**
     * alert 取消按钮文本颜色
     */
    static readonly ALERT_CANCEL_COLOR: string = "#fff";
    /**
     * alert 取消按钮背景颜色
     */
    static readonly ALERT_CANCEL_BG_COLOR: string = "#dcdfe6";
    /**
     * action menu 背景颜色
     */
    static readonly MENU_BG_COLOR: string = "#323233";
    /**
     * dialog取消颜色
     */
    static readonly DIALOG_CANCEL_COLOR: string = '#606266';
    /**
     * 字体相关
     */
    static readonly FONT_12: string = '12fp';
    static readonly FONT_13: string = '13fp';
    static readonly FONT_14: string = '14fp';
    static readonly FONT_15: string = '15fp';
    static readonly FONT_16: string = '16fp';
    static readonly FONT_18: string = '18fp';
    /**
     * 数量相关
     */
    static readonly NUMBER_3: number = 3;
    static readonly VP_3: string = '3vp';
    static readonly NUMBER_5: number = 5;
    static readonly VP_5: string = '5vp';
    static readonly NUMBER_8: number = 8;
    static readonly NUMBER_10: number = 10;
    static readonly VP_10: string = '10vp';
    static readonly NUMBER_15: number = 15;
    static readonly NUMBER_18: number = 18;
    static readonly VP_15: string = '15vp';
    static readonly NUMBER_20: number = 20;
    static readonly VP_20: string = '20vp';
    static readonly NUMBER_25: number = 25;
    static readonly VP_35: string = '35vp';
    static readonly NUMBER_30: number = 30;
    static readonly VP_25: string = '25vp';
    static readonly NUMBER_40: number = 40;
    static readonly NUMBER_45: number = 45;
    static readonly VP_45: string = '45vp';
    static readonly NUMBER_50: number = 50;
}
