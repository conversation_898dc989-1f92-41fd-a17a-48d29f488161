{"BuildOptions": {"SELECT_BUILD_TARGET": "default", "BUILD_PATH": {"OUTPUT_METADATA_JSON": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/hap_metadata/default/output_metadata.json", "OUTPUT_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/outputs/default", "RES_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default", "ETS_SUPER_VISUAL_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/cache/default/default@CompileArkTS/esmodule", "JS_ASSETS_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out/default", "INTERMEDIA_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates", "RES_PROFILE_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/res/default/resources/base/profile", "WORKER_LOADER": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader/default/loader.json", "MANIFEST_JSON": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/manifest/default", "JS_LITE_ASSETS_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/intermediates/loader_out_lite/default", "JS_SUPER_VISUAL_PATH": "/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud/entry/build/default/cache/default/default@CompileJS/jsbundle"}}, "CommonInfo": {"current.select.target": "default"}}