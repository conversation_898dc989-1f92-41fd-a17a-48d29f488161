<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3a041ceb-0d0d-4d5d-be76-b93d07339909" name="Changes" comment="升级axios版本，解决其不适配新版deveco studio以及hvigor脚本问题">
      <change beforePath="$PROJECT_DIR$/build-profile.json5" beforeDir="false" afterPath="$PROJECT_DIR$/build-profile.json5" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="ArkTS File" />
      </list>
    </option>
  </component>
  <component name="FilterPreference">
    <option name="filterName" value="User logs of selected app" />
    <option name="processName" value="com.huawei.hmos.aidataservice.1" />
  </component>
  <component name="Git.Merge.Settings">
    <option name="BRANCH" value="origin/task_SYN-T121451" />
    <option name="OPTIONS">
      <set>
        <option value="NO_FF" />
      </set>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="HiLogStateProjectLevelPreference">
    <panelStates>
      <hilogPanelState>
        <option name="filterName" value="User logs of selected app" />
        <option name="logPanelType" value="ONLINE" />
        <option name="processName" value="com.haier.uhome.upcloud" />
      </hilogPanelState>
    </panelStates>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2i2DLoSLTCon19K82sRYT2QJA11" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;127.0.0.1:5555&quot;: &quot;5374593881733879810281&quot;,
    &quot;4DV0224A24000901&quot;: &quot;5374752691736407882177&quot;,
    &quot;Application.entry.executor&quot;: &quot;Run&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (10899).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (12092).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (12240).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (13195).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (13613).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (14440).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (16283).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (16840).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (17614).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (20650).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (23289).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (24405).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (26394).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (2814).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (29121).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (29771).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (30886).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (31929).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (4503).executor&quot;: &quot;Debug&quot;,
    &quot;Js Attach.ArkTS/JS Debugger (7202).executor&quot;: &quot;Debug&quot;,
    &quot;MODULE_HAP_TIME&quot;: &quot;{\&quot;4DV0224A24000901entry\&quot;:\&quot;2025-01-09T07:31:20.997175841Z\&quot;,\&quot;127.0.0.1:5555entry\&quot;:\&quot;2024-12-10T09:33:21.767046787Z\&quot;}&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;ace.nodejs.version&quot;: &quot;18.20.1&quot;,
    &quot;auto_debug_bundleName&quot;: &quot;0000002732BD90F3089CC760EF4851390060712BB4DDFA7E4425A138AA98C1ADAC9ECEDF1A65D43B3ECCE08086039933D8D62911DE9D95&quot;,
    &quot;auto_debug_cert_list&quot;: &quot;000000232D28FC731B1DE3CA1DB792F06729A1EC2E383D16D54746B91F56D21222227227DF4E34338CFCAA0561573635D8A09B&quot;,
    &quot;auto_debug_device_list&quot;: &quot;000003BBD75E13EBDE12258A46F897977B34358C93D561190547B4544C23AC709C361753E51AD956D3C50596F4458822C9540691DB942812A9691A10081198854E10F42FA2054D59721090CAA0B0B77AF75676CC23577E11F574B4A1D2A08A24F61BFB97FE20D90353AF571D67E8CB61A0E620EF41CF57176E7F5D14C7B928F60647EDD626064DB99518F50CFCDEAE2A3ED673F7318FC981B853962DFB601DAFA08F7E0B29B6FDFB23E6D4EDA4D62B3C38A23E614F8F69F62398F2F98D516F9C25E5A33F5698BCA59D0F57674754A695821668DEDDF03A5D72CBF58726AE060D4966AC3EF36C4690590B96847B88382BE71817C480F134EE2DDA8D44153DA0559967F268861C59B97898AD6F96304E33855A0FCCE428F4BAFB392B946D3553EE7E1F90958B1F6838FAC43E9B18B270E175C7D594DDECBB9F2034BAE5FD17A2AA165991D7C18AE129EF7F455F0717D52CEF5280CB312B2CCE5A4A31465D69E8F6ECD78C315931A65979C7C18D6024056F034CFA6F41910E12B569C9C09130588BD6BEF83FEE527234FC5E930F6A469B30A2AA36BDBC1CA411CF28D250E47C54CF3266E6EBDC72631E831EBD24883638608A169FBC61FA433ADC1D87D8282655E241694769A999718C0CCF82EC6F89AC1DD9F2253CF26FEAAF8EDB0C3F74CA5B6B25B55236B3A88B65B3244CB9D58FCF41A3CB8B59C6CF62636E2E3C99676BD3D5C18EECDCAE3952989C4D456FE8BE52E2D01FC0ACF81F52C579382005F6F16CA058D74D827B8555CF4D5A4F604B8A1DC7F9AFE221414B1850F3181E5DCD70AB24B011901F254ED126295AC19DD81E42C0E40E4C73188FC8DE4252AE1DD1D8ADC127BCFFC134102EDC2C6438339AE27432011C8A751C4E6A6046F92762AECA764884428A02E54F7639C4FB2CE1C4959FCF3EF1FA12E4CA19086860CA8F457C5BE5BFF56DC65039619F0E9AF0FF0551919E4CB4A72A090786E24F860EB7C4F7BC94D76B0ED90D140622A62DA51B298F6F1B8FE1BA4E61CB3E8B5DDE70CE8458C0305EDF615598E814C4BDAD3D144F4EAF91C0CCC85C14CCCDF41DEE8F59E3A5D1832EE1ABDFC855FEA71F1698FCC7DD3008AA1755FBFA8C01AC4253299DFBE4C037321D1E9434F9D69D6B806C5670369F83B3195CF1E5F0A74432486DD96D1FE184218AD4DAE56A350190482837F2F73D40A9DD547C2649051CA2252DA6A6BC63EF3F6C8A274B1C8AF8FD3095196DA54EF5BC9542E4C1742668EB9DC5EBB0BAB4938C753F7F91DD96616EE9CA3CE2806483991755751A9490EFC38DC116CDC272AE2B65E22F8AE3E803A7300F4F65941AA7334F6F3A78086D85DC27EBE209F60689780DA844940814747C0D46&quot;,
    &quot;auto_debug_package_name&quot;: &quot;000000271E61844717FE8213B463CABF497450627D15E354139F4244F538C88F1C5CB217528C8F89E52226B1A2F30553DD3487137364F0&quot;,
    &quot;auto_debug_team_id&quot;: &quot;00000022DB190FAE87E6784F7BDD26EB213AD8C75C72E5CA6B3796279AB32CD0BD8BBD063029B15B2D49EB47A89A4ACD9D0E&quot;,
    &quot;client&quot;: &quot;com.haier.uhome.upcloud&quot;,
    &quot;debugger&quot;: &quot;Detect Automatically&quot;,
    &quot;device&quot;: &quot;Huawei_Phone&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/DevEcoStudioProjects/Haier/base/UpCloud&quot;,
    &quot;projectSizeLastTraceTimestamp-80d5b5fa73f2802f3a657c1a61cfa9955bb897b3d1867e618c5ffba950c9&quot;: &quot;1723684564735&quot;,
    &quot;selectedConfiguration&quot;: &quot;Application: entry&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Errors&quot;,
    &quot;showAllProcesses&quot;: &quot;false&quot;,
    &quot;system&quot;: &quot;openharmony&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;reqPermissions&quot;: [
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/upcloud/src/main/ets/appserver" />
      <recent name="$PROJECT_DIR$/upcloud/src/main/ets" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/upcloud/src/main/ets/initer" />
    </key>
  </component>
  <component name="RunManager" selected="Application.entry">
    <configuration name="entry" type="HotReLoadTask" factoryName="Hot Reload">
      <MODULE_NAME />
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <HOT_RELOAD_MODULE_NAME>entry</HOT_RELOAD_MODULE_NAME>
      <method v="2">
        <option name="Build.Hvigor.HotReloadBeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="UpCloud [assembleHap]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value="--mode module -p product=default assembleHap --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value=" -p product=default clean --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value=" -p product=default clean --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value=" -p product=default clean --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value=" -p product=default clean --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean,assembleHap]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value="clean --mode module -p product=default assembleHap --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [clean,assembleHap]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value="clean --mode module -p product=default assembleHap --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [upcloud:GenerateBuildProfile]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value="--mode module -p module=upcloud@default -p product=default GenerateBuildProfile --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="UpCloud [upcloud:assembleHar]" type="HvigorRunConfiguration" factoryName="HvigorRunConfiguration" temporary="true">
      <option name="applicationParameters" value="--mode module -p product=default -p module=upcloud@default assembleHar --analyze=normal --parallel --incremental" />
      <option name="nodeInterpreter" value="$APPLICATION_HOME_DIR$/tools/node/bin" />
      <option name="scriptFile" value="$APPLICATION_HOME_DIR$/tools/hvigor/bin/hvigorw.js" />
      <option name="workingDir" value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration name="entry" type="OhosDebugTask" factoryName="OpenHarmony App">
      <MODULE_NAME>entry</MODULE_NAME>
      <REINSTALL_APPLICATION>true</REINSTALL_APPLICATION>
      <ENVIRONMENT_VARIABLES>[]</ENVIRONMENT_VARIABLES>
      <DEBUG_LINE_ENABLE_DATA>false</DEBUG_LINE_ENABLE_DATA>
      <AUTO_DEPENDENCIES_DATA>true</AUTO_DEPENDENCIES_DATA>
      <INSTALL_FLAGS_CONFIG_KEY />
      <LAUNCH_FLAGS_CONFIG_KEY />
      <LAUNCH_ABILITY_CONFIG_TYPE>Default Ability</LAUNCH_ABILITY_CONFIG_TYPE>
      <LAUNCH_ABILITY_CONFIG_VALUE />
      <SYMBOL_DIRS>[]</SYMBOL_DIRS>
      <DEBUGGER_TYPE>Detect Automatically</DEBUGGER_TYPE>
      <MULTI_HAP_MODULE_DATA>[]</MULTI_HAP_MODULE_DATA>
      <SHOW_STATIC_VARIABLES>true</SHOW_STATIC_VARIABLES>
      <LOGGING_TARGET_CHANNELS>lldb process:gdb-remote packets</LOGGING_TARGET_CHANNELS>
      <USER_POST_ATTACH_COMMANDS>[]</USER_POST_ATTACH_COMMANDS>
      <USER_STARTUP_COMMANDS>[]</USER_STARTUP_COMMANDS>
      <ETS_SOURCE_PARIS>[]</ETS_SOURCE_PARIS>
      <DEPLOY_MULTI_HAP>false</DEPLOY_MULTI_HAP>
      <ASAN_ENABLE_DATA>false</ASAN_ENABLE_DATA>
      <TSAN_ENABLE_DATA>false</TSAN_ENABLE_DATA>
      <ERROR_INFO_ENHANCE_ENABLE_DATA>false</ERROR_INFO_ENHANCE_ENABLE_DATA>
      <MULTI_THREAD_CHECK_SELECTED_DATA>false</MULTI_THREAD_CHECK_SELECTED_DATA>
      <UBSAN_ENABLE_DATA>false</UBSAN_ENABLE_DATA>
      <HWASAN_ENABLE_DATA>false</HWASAN_ENABLE_DATA>
      <method v="2">
        <option name="Build.Hvigor.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Hvigor.UpCloud [clean]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="工程搭建以及UpCloud整体接口对齐" />
    <MESSAGE value="Initer修改以及header、cache、token处理" />
    <MESSAGE value="完成token、日志拦截以及apiServer注册处理" />
    <MESSAGE value="完成UplusAppServer实现以及签名、token验证问题处理" />
    <MESSAGE value="打包失败处理" />
    <MESSAGE value="增加验收环境统拦截处理" />
    <MESSAGE value="增加验收环境统拦截处理，对外暴露RequestBoy创建post请求参数" />
    <MESSAGE value="第三方部分api调用crash问题处理" />
    <MESSAGE value="通用header中移除appkey" />
    <MESSAGE value="导出ApiServerConfig，main库需要登录、退出登录时更新相关token" />
    <MESSAGE value="导出ApiServerConfig，main库需要登录、退出登录时更新相关token，关闭混淆否者找不到配置" />
    <MESSAGE value="升级axios版本，解决其不适配新版deveco studio以及hvigor脚本问题" />
    <option name="LAST_COMMIT_MESSAGE" value="升级axios版本，解决其不适配新版deveco studio以及hvigor脚本问题" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="JavaScript">
          <url>file://$PROJECT_DIR$/upcloud/src/main/ets/initer/HttpIniter.ets</url>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>