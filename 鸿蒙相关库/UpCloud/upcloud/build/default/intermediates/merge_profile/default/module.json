{"app": {"bundleName": "com.haier.uhome.upcloud", "debug": true, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50001013, "apiReleaseType": "Release", "compileSdkVersion": "5.0.1.115", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "upcloud", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "requestPermissions": [{"name": "ohos.permission.GET_NETWORK_INFO"}, {"name": "ohos.permission.INTERNET"}], "packageName": "@uplus/upcloud", "installationFree": false}}