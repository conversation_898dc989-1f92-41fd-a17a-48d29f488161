import { Cache, Dns, HttpClient, Interceptor, TimeUnit } from '@ohos/httpclient';
import Dispatcher from '@ohos/httpclient/src/main/ets/dispatcher/Dispatcher';
import { ApiServer } from '../../ApiServer';
import { OkHttpClient } from '../../client/OkHttpClient';
import { UpCloudLog } from '../../UpCloudLog';

export class HttpClientBuilder {
  private interceptors: Array<Interceptor> = new Array();
  private _cache?: Cache.Cache;
  private _connectTimeout: number = ApiServer.DEFAULT_TIMEOUT;
  private _readTimeout: number = ApiServer.DEFAULT_TIMEOUT;
  private _writeTimeout: number = ApiServer.DEFAULT_TIMEOUT;
  private _callTimeOut: number = ApiServer.DEFAULT_TIMEOUT;
  private _dns?: Dns;
  private _disPatcher?: Dispatcher;

  constructor() {

  }

  addInterceptor(interceptor: Interceptor) {
    this.interceptors.push(interceptor);
    return this;
  }

  cache(cache: Cache.Cache) {
    this._cache = cache;
    return this;
  }

  dns(dns: Dns) {
    this._dns = dns;
    return this;
  }

  dispatcher(dispatcher: Dispatcher) {
    this._disPatcher = dispatcher;
    return this;
  }

  public callTimeout(value: number) {
    this._callTimeOut = value;
    return this;
  }

  public connectTimeout(value: number) {
    this._connectTimeout = value;
    return this;
  }

  public readTimeout(value: number) {
    this._readTimeout = value;
    return this;
  }

  public writeTimeout(value: number) {
    this._writeTimeout = value;
    return this;
  }

  public build(): OkHttpClient {
    let clientBuilder = new HttpClient.Builder;
    clientBuilder.setCallTimeout(this._callTimeOut, TimeUnit.SECONDS)
      .setConnectTimeout(this._connectTimeout, TimeUnit.SECONDS)
      .setWriteTimeout(this._writeTimeout, TimeUnit.SECONDS)
      .setReadTimeout(this._readTimeout, TimeUnit.SECONDS)
      .cache(this._cache)
      .dns(this._dns)
      ._dispatcher = this._disPatcher;
    this.interceptors.forEach((interceptor) => {
      UpCloudLog.debug(`HttpClientBuilder.build() addInterceptor ${interceptor.constructor.name}`);
      clientBuilder.addInterceptor(interceptor);
    });
    return new OkHttpClient(clientBuilder.build());
  }

}