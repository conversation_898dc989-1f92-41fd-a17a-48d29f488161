import { Chain, Interceptor, Request, RequestBody, Response } from '@ohos/httpclient';
import { HttpUrl } from '@ohos/httpclient/src/main/ets/HttpUrl';
import { ApiServer } from '../ApiServer';
import { ApiServerConfig } from '../ApiServerConfig';
import { UpCloudConstants } from '../common/UpCloudConstants';
import { Utils } from '../Utils';

export class UwsInterceptor implements Interceptor {
  private apiServer: ApiServer;
  private requestSn: number = 0;

  constructor(apiServer: ApiServer) {
    this.apiServer = apiServer;
  }

  async intercept(chain: Chain): Promise<Response> {
    let request: Request = chain.requestI();
    request = await this.addCommonHeaders(request);
    request = await this.signRequest(request);
    return chain.proceedI(request);
  }

  private async addCommonHeaders(request: Request): Promise<Request> {
    let builder = request.newBuilder();
    let timestamp = Date.now();
    builder.addHeader(UpCloudConstants.HEADER_CONTENT_TYPE, this.apiServer.getConfig(ApiServerConfig.CONTENT_TYPE));
    builder.addHeader(UpCloudConstants.HEADER_APP_ID, this.apiServer.getConfig(ApiServerConfig.APP_ID));
    builder.addHeader(UpCloudConstants.HEADER_APP_VERSION, this.apiServer.getConfig(ApiServerConfig.APP_VERSION));
    builder.addHeader(UpCloudConstants.HEADER_CLIENT_ID, this.apiServer.getConfig(ApiServerConfig.CLIENT_ID));
    builder.addHeader(UpCloudConstants.HEADER_SEQUENCE_ID, this.generateNextSequenceId(timestamp));
    builder.addHeader(UpCloudConstants.HEADER_TIMESTAMP, timestamp.toString());
    builder.addHeader(UpCloudConstants.HEADER_LANGUAGE, this.apiServer.getConfig(ApiServerConfig.LANGUAGE));
    builder.addHeader(UpCloudConstants.HEADER_TIMEZONE, this.apiServer.getConfig(ApiServerConfig.TIMEZONE));
    builder.addHeader(UpCloudConstants.HEADER_ACCESS_TOKEN, this.apiServer.getConfig(ApiServerConfig.ACCESS_TOKEN));
    return builder.build();
  }

  private async signRequest(request: Request): Promise<Request> {
    let requestBody: RequestBody = request.body as RequestBody;
    let body: string;
    if (requestBody && requestBody.content && requestBody.content !== null) {
      body = requestBody.content as string;
    } else {
      body = "";
    }
    body = body.replace(/[\s\t\r\n]/g, "");
    let timestamp = request.getHeader(UpCloudConstants.HEADER_TIMESTAMP) as string;
    let pathname = ((request.url as HttpUrl).url).replaceAll(/^(http:\/\/|https:\/\/)[^/]+/g, "");
    let content = pathname + body
      + this.apiServer.getConfig(ApiServerConfig.APP_ID)
      + this.apiServer.getConfig(ApiServerConfig.APP_KEY)
      + timestamp;
    let sign = await Utils.sha256(content);
    let builder = request.newBuilder();
    builder.addHeader(UpCloudConstants.HEADER_SIGNATURE, sign);
    return builder.build();
  }

  private generateNextSequenceId(timestamp: number): string {
    this.increaseRequestSn();
    return `${timestamp}${this.requestSn.toString().padStart(6, '0')}`;
  }

  private increaseRequestSn(): void {
    this.requestSn++;
    if (this.requestSn > 999999) {
      this.requestSn = 0;
    }
  }
}