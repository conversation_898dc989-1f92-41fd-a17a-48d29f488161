import { ApiServer } from "../ApiServer";
import { ArrayList } from "@kit.ArkTS";
import { HttpIniter } from "../initer/HttpIniter";
import { HttpClientBuilder } from "../initer/builder/HttpClientBuilder";
import { UwsInterceptor } from "./UwsInterceptor";
import { CacheIniter } from "../common/CacheIniter";
import { TokenVerifierIniter } from "../common/TokenVerifierIniter";
import { TimeOutIniter } from "../common/TimeOutIniter";
import { LoggingIniter } from "../common/LoggingIniter";

export class UwsWebServer extends ApiServer {
  getBaseUrlList(): string[] {
    return [
      "https://uws.haier.net",
    ];
  }

  getHttpIniterList(): ArrayList<HttpIniter<HttpClientBuilder>> {
    let list: ArrayList<HttpIniter<HttpClientBuilder>> = new ArrayList();
    list.add(new UwsIniter());
    list.add(new CacheIniter());
    list.add(new TokenVerifierIniter(false));
    list.add(new TimeOutIniter());
    list.add(new LoggingIniter());
    return list;
  }
}

class UwsIniter implements HttpIniter<HttpClientBuilder> {
  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: Context): HttpClientBuilder {
    builder.addInterceptor(new UwsInterceptor(apiServer));
    return builder;
  }
}