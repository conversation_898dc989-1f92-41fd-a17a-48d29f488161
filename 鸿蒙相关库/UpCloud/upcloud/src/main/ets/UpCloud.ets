import { Dns } from '@ohos/httpclient';
import Dispatcher from '@ohos/httpclient/src/main/ets/dispatcher/Dispatcher';
import BuildProfile from '../../../BuildProfile';
import { ApiServer } from './ApiServer';
import { ApiServerHolder } from './ApiServerHolder';
import { ClientIdProvider } from './common/ClientIdProvider';
import { UpCloudHttpDns } from './dns/UpCloudHttpDns';
import { HttpClientBuilder } from './initer/builder/HttpClientBuilder';
import { UpCloudLog } from './UpCloudLog';
import { common } from '@kit.AbilityKit';
import { OkHttpClient } from './client/OkHttpClient';

export class UpCloud {
  private static instance: UpCloud;
  private clientIdProvider?: ClientIdProvider;
  private isInitialized: boolean = false;
  private apiServerHolder: ApiServerHolder = new ApiServerHolder();
  private httpDns: UpCloudHttpDns = new UpCloudHttpDns();
  private dispatcher: Dispatcher = new Dispatcher();
  private httpClientMap: Map<ApiServer, OkHttpClient> = new Map();
  private context!: common.UIAbilityContext;

  private constructor() {
  }

  public static getInstance(): UpCloud {
    if (!UpCloud.instance) {
      UpCloud.instance = new UpCloud();
    }
    BuildProfile.DEBUG
    return UpCloud.instance;
  }

  public initialize(initer: Initializer, clientIdProvider: ClientIdProvider): UpCloud {
    if (this.isInitialized) {
      UpCloudLog.warn('Upcloud already initialize return!');
      return this;
    }
    this.isInitialized = true;
    this.clientIdProvider = clientIdProvider;
    if (initer.isEnablePresetApiServers) {
      this.apiServerHolder.registerPresetApiServers();
    }
    this.context = initer.getContext();
    this.setHttpDns(initer.httpDns);
    this.setHttpDnsEnabled(initer.isEnableHttpDns);
    return this;
  }

  public getHttpDns(): Dns {
    return this.httpDns;
  }

  public setHttpDns(dns: Dns | undefined) {
    this.httpDns.setDns(dns);
  }

  public setHttpDnsEnabled(enabled: boolean) {
    this.httpDns.setEnabled(enabled);
    if (enabled) {
      this.httpDns.initHttpDns(this.context);
    }
  }

  public getClientIdProvider(): ClientIdProvider | undefined {
    return this.clientIdProvider;
  }

  /**
   * 获取http请求实例
   * @param baseUrl 请求基础地址
   * @returns http请求实例
   * @throws 如果baseUrl对应的ApiServer不存在抛出异常
   */
  public getHttpClient(baseUrl: string): OkHttpClient {
    let apiServer: ApiServer | null = this.getApiServer(baseUrl);
    if (apiServer === null) {
      throw new Error(`UpCloud.getHttpClient() cannot find ApiServer by url ${baseUrl} please implement ApiServer first`);
    }
    let client: OkHttpClient | undefined = this.getHttpClientByApiServer(apiServer);
    if (client === undefined) {
      throw new Error("UpCloud.getHttpClient() client create fail");
    }
    return client;
  }

  public getCustomHttpClient(baseUrl: string,apiServer:ApiServer): OkHttpClient {
    let client: OkHttpClient | undefined = this.getHttpClientByApiServer(apiServer);
    if (client === undefined) {
      throw new Error("UpCloud.getHttpClient() client create fail");
    }
    return client;
  }

  getApiServer(baseUrl: string): ApiServer | null {
    return this.apiServerHolder.getApiServer(baseUrl);
  }

  private getHttpClientByApiServer(apiServer: ApiServer): OkHttpClient | undefined {
    UpCloudLog.debug("UpCloud.getHttpClientByApiServer() start");
    let client: OkHttpClient | undefined = this.httpClientMap.get(apiServer);
    if (client === undefined) {
      client = this.createHttpClient(apiServer);
    }
    UpCloudLog.debug(`UpCloud.getHttpClientByApiServer().createHttpClient result is ${client}`);
    if (client !== undefined) {
      this.httpClientMap.set(apiServer, client);
      UpCloudLog.debug(`UpCloud.getHttpClientByApiServer() create client success`);
    } else {
      return undefined;
    }
    return client;
  }

  private createHttpClient(apiServer: ApiServer): OkHttpClient | undefined {
    UpCloudLog.debug(`UpCloud.createHttpClient() start apiServer = ${apiServer.constructor.name}`);
    let builder: HttpClientBuilder = apiServer.createHttpClientBuilder(this.context);
    builder.dns(this.httpDns);
    builder.dispatcher(this.dispatcher);
    builder = apiServer.onHttpBuilderCreated(builder);
    return builder.build();
  }

  static getInitializer(): Initializer {
    return new Initializer();
  }

}

export class Initializer {
  private _httpDns?: Dns | undefined;
  private _isEnableHttpDns: boolean = false;
  private _isEnablePresetApiServers: boolean = true;
  private context!: common.UIAbilityContext;

  constructor() {
  }

  public get isEnablePresetApiServers(): boolean {
    return this._isEnablePresetApiServers;
  }

  public setIsEnablePresetApiServers(isEnablePresetApiServers: boolean): Initializer {
    this._isEnablePresetApiServers = isEnablePresetApiServers;
    return this;
  }

  public get httpDns(): Dns | undefined {
    return this._httpDns;
  }

  public getContext(): common.UIAbilityContext {
    return this.context;
  }

  public setContext(context: common.UIAbilityContext): Initializer {
    this.context = context;

    return this;
  }

  public get isEnableHttpDns(): boolean {
    return this._isEnableHttpDns;
  }

  public enableHttpDns(httpDns: Dns): Initializer {
    if (!httpDns) {
      throw new Error('HttpDns instance cannot be NULL.');
    }
    this._isEnableHttpDns = true;
    this._httpDns = httpDns;
    return this;
  }

  public initialize(initer: Initializer, clientIdProvider: ClientIdProvider): UpCloud {
    return UpCloud.getInstance().initialize(initer, clientIdProvider);
  }

}