/**
 * API 服务器配置相关常量
 */
export class ApiServerConfig {
  static CONTENT_TYPE = 'contentType';
  static APP_ID = "appId";
  static APP_KEY = "appKey";
  static APP_VERSION = "appVersion";
  static CLIENT_ID = "uuid";// clientId
  static ACCESS_TOKEN = "accessToken";
  static MAIN_SITE_TOKEN = "mainSiteToken";
  static MAIN_SITE_USER_ID = "mainSiteUserId";
  static MAIN_SITE_SESSION_ID = "mainSiteSessionId";
  static MAIN_SITE_APP_NAME = "mainSiteAppName";
  static LANGUAGE = "language";
  static TIMEZONE = "timeZone";
  static PRO_VERSION = "proVersion";
  static USER_CENTER_CLIENT_ID = "userCenterClientId";
  static USER_CENTER_CLIENT_SECRET = "userCenterClientSecret";
  static USER_CENTER_UHOME_SIGN = "userCenterUhomeSign";
  static USER_CENTER_APP_ACCESS_TOKEN = "userCenterAppAccessToken";
  // 东南亚叫accountToken
  static USER_CENTER_USER_ACCESS_TOKEN = "userCenterUserAccessToken";
  static USER_CENTER_USER_REFRESH_TOKEN = "userCenterUserRefreshToken";

  /**
   * 无效token错误码
   */
  static INVALID_TOKEN_LIST = "invalid_token_list";

  // 东南亚版新加，gray：服务模式--控制接口是否灰度 针对支持灰度接口起作用
  static SERVICE_MODE = "serviceModel";
}