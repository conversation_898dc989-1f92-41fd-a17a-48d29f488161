import { Chain, Interceptor, Request, RequestBody, Response } from '@ohos/httpclient';
import { UpCloudLog } from '../UpCloudLog';
import { systemDateTime } from '@kit.BasicServicesKit';
import { HttpUrl } from '@ohos/httpclient/src/main/ets/HttpUrl';

export class HttpLoggingInterceptor implements Interceptor {
  async intercept(chain: Chain): Promise<Response> {
    let request: Request = chain.requestI();
    let requestStartMessage = "-->"
      + request.method
      + " " + (request.url as HttpUrl).url;
    UpCloudLog.debug(requestStartMessage);
    let headers: object = request.headers;
    Object.keys(headers).forEach((key) => {
      UpCloudLog.debug(key + ":" + headers[key]);
    })
    let requestBody: RequestBody = request.body;
    let hasRequestBody: boolean = request.body !== null;
    if (!hasRequestBody) {
      UpCloudLog.debug("--> END " + request.method);
    } else {
      UpCloudLog.debug(requestBody.content);
      UpCloudLog.debug(requestBody.mimes);
      UpCloudLog.debug("--> END " + request.method);
    }
    let startMs = systemDateTime.getUptime(systemDateTime.TimeType.STARTUP);
    let response: Response = await chain.proceedI(request);
    let tookMs = systemDateTime.getUptime(systemDateTime.TimeType.STARTUP) - startMs;
    UpCloudLog.debug("<-- "
      + response.getCode()
      + " " + response.protocol
      + " " + (response.request.url as HttpUrl).url
      + " (" + tookMs + "ms" + ")");
    UpCloudLog.debug("--> END HTTP");
    return response;
  }
}