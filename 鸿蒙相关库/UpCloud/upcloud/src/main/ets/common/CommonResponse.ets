export class CommonResponse {
  public static readonly SUCCESS_CODE = "00000";
  private retInfo: string = "";
  private retCode: string = "";

  public getRetCode(): string {
    return this.retCode;
  }

  public getRetInfo(): string {
    return this.retInfo;
  }

  public isSuccess(): boolean {
    return CommonResponse.SUCCESS_CODE === this.retCode;
  }

  public toString(): string {
    return "CommonResponse{" +
      "retCode='" + this.getRetCode() + '\'' +
      ", retInfo='" + this.getRetInfo() + '\'' +
      ", isSuccess()='" + this.isSuccess() + '\'' +
      '}';
  }
}