import { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { HttpIniter } from '../initer/HttpIniter';
import { UpCloudLog } from '../UpCloudLog';
import { TokenVerifier } from './TokenVerifier';

export class TokenVerifierIniter implements HttpIniter<HttpClientBuilder> {

  private isUpdateDefaultTokenConfig: boolean = false;

  constructor(isUpdateDefaultTokenConfig: boolean) {
    this.isUpdateDefaultTokenConfig = isUpdateDefaultTokenConfig;
  }

  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: Context): HttpClientBuilder {
    UpCloudLog.debug(`TokenVerifierIniter.initialize called apiServer = ${apiServer.constructor.name}`);
    if (!builder) {
      builder = new HttpClientBuilder();
    }
    return builder.addInterceptor(new TokenVerifier(apiServer, this.isUpdateDefaultTokenConfig));
  }
}