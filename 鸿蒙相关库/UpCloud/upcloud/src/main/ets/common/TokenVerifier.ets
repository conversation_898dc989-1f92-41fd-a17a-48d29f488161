import { Chain, Interceptor, Response } from '@ohos/httpclient';
import { ApiServer } from '../ApiServer';
import { HashSet, util } from '@kit.ArkTS';
import { ApiServerConfig } from '../ApiServerConfig';
import { Utils } from '../Utils';
import { plainToInstance } from 'class-transformer';
import { CommonResponse } from './CommonResponse';
import emitter from '@ohos.events.emitter';
import { UpCloudLog } from '../UpCloudLog';

export const EVENT_ACTION_NEW_TOKEN = "com.haier.uhome.upcloud.common.TokenVerifier.NEW_TOKEN";

export const EVENT_ACTION_INVALID_TOKEN = "com.haier.uhome.upcloud.common.TokenVerifier.INVALID_TOKEN";

const ACCESS_TOKEN = "accessToken";

const SD_TOKEN = "sdToken";

const OFFICIAL_USER_ID = "offUserId";

const CO_SESSION_ID = "coSessionId";

const SD_APP_NAME = "sdAppName";

export class TokenVerifier implements Interceptor {
  private static invalidCodes: HashSet<string> = new HashSet();
  private apiServer: ApiServer;
  private isUpdateDefaultConfig: boolean = false;

  constructor(apiServer: ApiServer, isUpdateDefaultConfig: boolean) {
    this.apiServer = apiServer;
    this.isUpdateDefaultConfig = isUpdateDefaultConfig;
    let invalidToken: string = apiServer.getConfig(ApiServerConfig.INVALID_TOKEN_LIST);
    this.initInvalidCodes(invalidToken);
  }

  initInvalidCodes(invalidToken: string) {
    if (Utils.isEmpty(invalidToken)) {
      return
    }
    let tokens: string[] = invalidToken.split(",");
    for (let token of tokens) {
      if (!Utils.isBlank(token)) {
        TokenVerifier.invalidCodes.add(token);
      }
    }
  }

  async intercept(chain: Chain): Promise<Response> {
    let response: Response = await chain.proceedI(chain.requestI());
    let body = this.convertBodyToLiteralObject(response);
    let commonResponse: CommonResponse = plainToInstance(CommonResponse, body);
    let retCode: string = commonResponse.getRetCode();
    if (TokenVerifier.invalidCodes.has(retCode)) {
      emitter.emit(EVENT_ACTION_INVALID_TOKEN, {
        data: {
          "apiServer": this.apiServer.constructor.name
        }
      });
    }
    let headerMap: Map<string, string> = plainToInstance(Map, JSON.parse(response.getHeader())) as Map<string, string>;
    let accessToken: string = headerMap.get(ACCESS_TOKEN) ?? "";
    let lastAccessToken: string = this.apiServer.getConfig(ApiServerConfig.ACCESS_TOKEN);
    let mainSiteToken: string = headerMap.get(SD_TOKEN) ?? "";
    let mainSiteUserId: string = headerMap.get(OFFICIAL_USER_ID) ?? "";
    let mainSiteSessionId: string = headerMap.get(CO_SESSION_ID) ?? "";
    let mainSiteAppName: string = headerMap.get(SD_APP_NAME) ?? "";
    if (!Utils.isEmpty(accessToken) && accessToken !== lastAccessToken) {
      UpCloudLog.debug(`TokenVerifier.intercept() find new accessToken,accessToken = ${accessToken},
       mainSiteToken = ${mainSiteToken}, mainSiteUserId = ${mainSiteUserId}, mainSiteSessionId = ${mainSiteSessionId}`);
      this.apiServer.setConfig(ApiServerConfig.ACCESS_TOKEN, accessToken);
      this.apiServer.setConfig(ApiServerConfig.MAIN_SITE_TOKEN, mainSiteToken);
      this.apiServer.setConfig(ApiServerConfig.MAIN_SITE_USER_ID, mainSiteUserId);
      this.apiServer.setConfig(ApiServerConfig.MAIN_SITE_SESSION_ID, mainSiteSessionId);
      this.apiServer.setConfig(ApiServerConfig.MAIN_SITE_APP_NAME, mainSiteAppName);
    }
    emitter.emit(EVENT_ACTION_NEW_TOKEN, {
      data: {
        "accessToken": accessToken,
        "sdToken": mainSiteToken,
        "apiServer": this.apiServer.constructor.name
      }
    });
    return response;
  }

  private convertBodyToLiteralObject(response: Response): Object {
    let body = response.getBody();
    let bodyType = typeof body;
    if (bodyType === "string") {
      return JSON.parse(body as string);
    } else {
      if (body instanceof ArrayBuffer) {
        let buffer: ArrayBuffer = body as ArrayBuffer;
        let uintArr: Uint8Array = new Uint8Array(buffer);
        let decoder: util.TextDecoder = util.TextDecoder.create('utf-8', {
          fatal: false, ignoreBOM: true
        });
        let bodyStr: string = decoder.decodeWithStream(uintArr);
        return JSON.parse(bodyStr);
      } else {
        return body;
      }
    }
  }
}
