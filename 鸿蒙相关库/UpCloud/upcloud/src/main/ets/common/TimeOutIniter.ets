import { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { HttpIniter } from '../initer/HttpIniter';
import { UpCloudLog } from '../UpCloudLog';

export class TimeOutIniter implements HttpIniter<HttpClientBuilder> {

  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: Context): HttpClientBuilder {
    UpCloudLog.debug(`TimeOutIniter.initialize called apiServer = ${apiServer.constructor.name}`);
    if (!builder) {
      builder = new HttpClientBuilder();
    }
    return builder.callTimeout(ApiServer.DEFAULT_TIMEOUT)
      .connectTimeout(ApiServer.DEFAULT_TIMEOUT)
      .readTimeout(ApiServer.DEFAULT_TIMEOUT)
      .writeTimeout(ApiServer.DEFAULT_TIMEOUT);
  }

}