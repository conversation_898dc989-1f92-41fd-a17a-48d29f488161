import { CacheControl, Chain, Interceptor, Request, Response, TimeUnit } from '@ohos/httpclient';
import { Utils } from '../Utils';

export class CacheInterceptor implements Interceptor {

  private weakNetworkMaxAgeRatio: number;

  constructor(weakNetworkMaxAgeRatio: number) {
    this.weakNetworkMaxAgeRatio = weakNetworkMaxAgeRatio;
  }

  async intercept(chain: Chain): Promise<Response> {
    let request: Request = chain.requestI();
    if (request.getHeader("Cache-Control") === null) {
      return chain.proceedI(request);
    }
    let netWorkState: number = await Utils.getNetWorkState();
    if (netWorkState === Utils.NETWORK_BAD) {
      request = this.modifyCacheControlForBadNetWork(request);
    } else if (netWorkState === Utils.NETWORK_WEAK) {
      request = this.modifyCacheControlForWeakNetWork(request);
    }
    return chain.proceedI(request);
  }

  private modifyCacheControlForBadNetWork(request: Request): Request {
    return request.newBuilder().cacheControl(CacheControl.FORCE_CACHE()).build();
  }

  private modifyCacheControlForWeakNetWork(request: Request): Request {
    let cacheControl: CacheControl = request.cacheControl;
    let maxAgeSeconds: number = cacheControl.maxAgeSeconds() / 1000;
    if (maxAgeSeconds > 0) {
      let modifiedCacheControl = this.genCacheControlBuilder(cacheControl)
        .maxAge(Math.floor(maxAgeSeconds * this.weakNetworkMaxAgeRatio))
        .build();
      request = request.newBuilder()
        .cacheControl(modifiedCacheControl)
        .build();
    }
    return request;
  }

  private genCacheControlBuilder(cacheControl: CacheControl) {
    let builder = new CacheControl.Builder();
    if (cacheControl.noCache) {
      builder.noCache();
    }
    if (cacheControl.noStore) {
      builder.noStore();
    }
    builder.maxAge(cacheControl.maxAgeSeconds() / 1000);
    builder.maxStale(cacheControl.maxStaleSeconds() / 1000);
    builder._minFreshSeconds = cacheControl.minFreshSeconds();
    if (cacheControl.onlyIfCached()) {
      builder.onlyIfCached();
    }
    if (cacheControl.noTransform()) {
      builder.noTransform();
    }
    if (cacheControl.immutable()) {
      builder.immutable();
    }
    return builder;
  }

}