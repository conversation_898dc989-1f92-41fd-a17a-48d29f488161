import { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { HttpIniter } from '../initer/HttpIniter';
import { UpCloudLog } from '../UpCloudLog';
import { HttpLoggingInterceptor } from './HttpLoggingInterceptor';

export class LoggingIniter implements HttpIniter<HttpClientBuilder> {

  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: Context): HttpClientBuilder {
    UpCloudLog.debug(`LoggingIniter.initialize called apiServer = ${apiServer.constructor.name}`);
    if (!builder) {
      builder = new HttpClientBuilder();
    }
    return builder.addInterceptor(new HttpLoggingInterceptor());
  }

}