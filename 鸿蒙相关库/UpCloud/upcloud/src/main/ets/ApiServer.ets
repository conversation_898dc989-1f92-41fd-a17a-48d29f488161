/**
 * api服务器配置基础抽象类
 */
import { ArrayList, HashMap } from '@kit.ArkTS';
import { ApiServerConfig } from './ApiServerConfig';
import { HttpClientBuilder } from './initer/builder/HttpClientBuilder';
import { HttpIniter } from './initer/HttpIniter';
import { UpCloud } from './UpCloud';
import { UpCloudLog } from './UpCloudLog';
import { common } from '@kit.AbilityKit';

export abstract class ApiServer {
  public static readonly DEFAULT_TIMEOUT = 15;
  private static defaultConfig: HashMap<string, string> = new HashMap();
  private config: HashMap<string, string> = new HashMap();

  public static getDefaultConfig(key: string): string {
    return ApiServer.defaultConfig.get(key) ?? '';
  }

  public static setDefaultConfig(key: string, value: string) {
    UpCloudLog.info('ApiServer.setDefaultConfig key = %{public}s, value = %{public}s', key, value);
    if (key === ApiServerConfig.CLIENT_ID) {
      return;
    }
    ApiServer.defaultConfig.set(key, value);
  }

  public getConfig(key: string): string {
    if (ApiServerConfig.CLIENT_ID === key) {
      UpCloudLog.info('getConfig key is uuid,return to default configuration item directly!');
      return ApiServer.getClientId();
    }
    let value: string | undefined = this.getLocalConfig(key);
    if (!value) {
      value = ApiServer.getDefaultConfig(key);
    }
    return value;
  }

  public static getClientId(): string {
    let clientIdProvider = UpCloud.getInstance().getClientIdProvider();
    return clientIdProvider?.getClientId() ?? '';
  }

  public getLocalConfig(key: string): string | undefined {
    if (key === ApiServerConfig.CLIENT_ID) {
      return ApiServer.getClientId();
    }
    return this.config.get(key);
  }

  public setConfig(key: string, value: string): void {
    if (key === ApiServerConfig.CLIENT_ID) {
      UpCloudLog.error('ApiServer.setConfig Not allow to set custom uuid, return! value = %{public}s', value);
      return;
    }
    this.config.set(key, value);
  }

  createHttpClientBuilder(context: common.Context): HttpClientBuilder {
    let initList: ArrayList<HttpIniter<HttpClientBuilder>> = this.getHttpIniterList();
    let builder: HttpClientBuilder = new HttpClientBuilder();
    initList.forEach((initer) => {
      builder = initer.initialize(builder, this, context);
    });
    return builder;
  }

  onHttpBuilderCreated(builder: HttpClientBuilder): HttpClientBuilder {
    return builder;
  }

  abstract getBaseUrlList(): Array<string>;

  abstract getHttpIniterList(): ArrayList<HttpIniter<HttpClientBuilder>>;

}
