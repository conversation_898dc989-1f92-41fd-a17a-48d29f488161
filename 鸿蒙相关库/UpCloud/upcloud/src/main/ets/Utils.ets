import { connection } from '@kit.NetworkKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { MD5, SHA } from '@yunkss/eftool';

export class Utils {
  public static readonly NETWORK_NON: number = -1;
  public static readonly NETWORK_BAD: number = 0;
  public static readonly NETWORK_NORMAL: number = 1;
  public static readonly NETWORK_WEAK: number = 2;
  private static SERIAL_NUMBER: number = 0;

  static generateSequenceId(timestamp: string): string {
    let requestSn: number = Utils.SERIAL_NUMBER++;
    if (requestSn === 1000000) {
      requestSn = 0;
    }
    return `${timestamp}${requestSn.toString().padStart(6, "0")}`;
  }

  static formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = Utils.padZero(date.getMonth() + 1); // getMonth 返回的是 0-11，需要加 1
    const day = Utils.padZero(date.getDate());
    const hours = Utils.padZero(date.getHours());
    const minutes = Utils.padZero(date.getMinutes());
    const seconds = Utils.padZero(date.getSeconds());
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  }

  static padZero(num: number): string {
    return num < 10 ? `0${num}` : num.toString();
  }

  static async getNetWorkState(): Promise<number> {
    return new Promise((resolve) => {
      connection.getDefaultNet().then((netHandle: connection.NetHandle) => {
        connection.getNetCapabilities(netHandle, (error: BusinessError, data: connection.NetCapabilities) => {
          if (error) {
            resolve(Utils.NETWORK_NON);
            return;
          }
          let upSpeed: number = data.linkUpBandwidthKbps ?? 0;
          let downSpeed: number = data.linkDownBandwidthKbps ?? 0;
          if (upSpeed === 0 || downSpeed === 0) {
            resolve(Utils.NETWORK_NON);
          } else if (upSpeed <= 500 && downSpeed <= 500) {
            resolve(Utils.NETWORK_BAD);
          } else if (upSpeed <= 2000 && upSpeed <= 2000) {
            resolve(Utils.NETWORK_WEAK);
          } else {
            resolve(Utils.NETWORK_NORMAL);
          }
        })
      });
    });
  }

  static isEmpty(str: string): boolean {
    return str === null || str.length === 0;
  }

  static isBlank(str: string): boolean {
    let length: number;
    if ((str == null) || ((length = str.length) == 0)) {
      return true;
    }
    for (let i = 0; i < length; i++) {
      if (false == Utils.isBlankChar(str.charCodeAt(i))) {
        return false;
      }
    }

    return true;
  }

  static async sha256(originStr: string): Promise<string> {
    let digest = await SHA.digest(originStr);
    return digest.getDataRow();
  }

  static async  md5(originStr: string): Promise<string> {
    let md5 = await MD5.digest(originStr);
    return md5.getDataRow();
  }

  private static isBlankChar(c: number): boolean {
    return Utils.isWhitespace(c)
      || Utils.isSpaceChar(c)
      || c == 0xFEFF
      || c == 0x202A
      || c == 0x0000;
  }

  private static isWhitespace(codePoint: number): boolean {
    const whitespaceRegex = /^\s$/;
    const character = String.fromCodePoint(codePoint);
    return whitespaceRegex.test(character);
  }

  private static isSpaceChar(codePoint: number): boolean {
    const spaceCategories = [
      "Zs", // Space separator
      "Zl", // Line separator
      "Zp"// Paragraph separator
    ];
    const character = String.fromCodePoint(codePoint);
    const category = character.charCodeAt(0).toString(16);
    return spaceCategories.includes(category);
  }

}