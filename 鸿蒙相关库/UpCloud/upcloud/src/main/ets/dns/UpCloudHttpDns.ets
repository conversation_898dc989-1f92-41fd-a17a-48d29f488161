import { Dns } from '@ohos/httpclient/src/main/ets/Dns';
import connection from '@ohos.net.connection';
import { Utils } from '../Utils';
import { BusinessError } from '@kit.BasicServicesKit';
import { httpdns, IpType } from '@aliyun/httpdns';
import { common } from '@kit.AbilityKit';
import { AppInfo } from '@uplus/upbase';
import { ServerEnv } from '@uplus/upbase/src/main/ets/ServerEnv';
const ACCOUNT_ID = '146683';
const APP_KEY = '500dedf10c5494b5cc32a6dbaad1a03f';

const UPHTTPDNSPreResolveUWSHost = "uws.haier.net";
const UPHTTPDNSPreResolveUhomeHost = "uhome.haier.net";
const UPHTTPDNSPreResolveVhallyunHost = "haier.vhallyun.com";
const UPHTTPDNSPreResolveSmartHomeHost = "zj.haier.net";
const UPHTTPDNSPreResolveBigData = "data.haier.net";

export class UpCloudHttpDns implements Dns {
  public initHttpDns(context:common.UIAbilityContext){
    httpdns.configService(ACCOUNT_ID, {
          context: context,
          secretKey:APP_KEY,
          useHttps: true,
          enableExpiredIp: true,
    });
    httpdns.getService(ACCOUNT_ID).then((httpdnsService)=>{
      httpdnsService.resolveHosts([
        UPHTTPDNSPreResolveUWSHost,
        UPHTTPDNSPreResolveUhomeHost,
        UPHTTPDNSPreResolveVhallyunHost,
        UPHTTPDNSPreResolveSmartHomeHost,
        UPHTTPDNSPreResolveBigData], IpType.Auto);
    });
  }

  private enabled: boolean = true;
  private dns?: Dns;

  public setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  public setDns(dns: Dns | undefined) {
    this.dns = dns;
  }

  lookup(hostname: string): Promise<connection.NetAddress[]> {
    if (this.enabled && this.dns && (AppInfo.getServerEnv() == ServerEnv.EV_SC || AppInfo.getServerEnv() == ServerEnv.SE_ASIA_SC)) {
      return this.dns.lookup(hostname);
    }
    return new SystemDns().lookup(hostname);
  }
}

export class SystemDns implements Dns {
  lookup(hostname: string): Promise<connection.NetAddress[]> {
    if (Utils.isEmpty(hostname)) {
      throw Error("hostname == null");
    }
    return new Promise((resolve, reject) => {
      connection.getAddressesByName(hostname).then((netAddress) => {
        resolve(netAddress)
      }).catch((err: BusinessError) => {
        reject(err)
      });
    })
  }
}

export class HttpDns implements Dns {

  lookup(hostname: string): Promise<connection.NetAddress[]> {
    if (Utils.isEmpty(hostname)) {
       throw Error("hostname == null");
    }
    return new Promise(async (resolve, reject) => {
      const httpdnsService = await httpdns.getService(ACCOUNT_ID);
      const result = await httpdnsService.getHttpDnsResultAsync(hostname, IpType.Auto);
      try {
        await connection.removeCustomDnsRule(hostname);
      } catch (ignored) {
        reject(Error("httpdns解析失败"));
      }
      if ((result.ipv4s?.length ?? 0) > 0) {
        await connection.addCustomDnsRule(hostname, result.ipv4s)
      } else if ((result.ipv6s?.length ?? 0) > 0) {
        await connection.addCustomDnsRule(hostname, result.ipv6s);
      } else {
        reject(Error("httpdns解析失败"));
      }
      connection.getAddressesByName(hostname).then((netAddress) => {
        resolve(netAddress)
      }).catch((err: BusinessError) => {
        reject(err)
      });
    })
  }
}