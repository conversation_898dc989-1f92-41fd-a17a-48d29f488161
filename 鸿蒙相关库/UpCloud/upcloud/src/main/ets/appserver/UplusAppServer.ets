import { ApiServer } from '../ApiServer';
import { HttpIniter } from '../initer/HttpIniter';
import { ArrayList } from '@kit.ArkTS';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { AppServerHeadersIniter } from './AppServerHeadersIniter';
import { AppServerSignIniter } from './AppServerSignIniter';
import { CacheIniter } from '../common/CacheIniter';
import { TokenVerifierIniter } from '../common/TokenVerifierIniter';
import { LoggingIniter } from '../common/LoggingIniter';
import { TimeOutIniter } from '../common/TimeOutIniter';

export class UplusAppserver extends ApiServer {

  getBaseUrlList(): string[] {
    return [
      "https://uhome.haier.net:7583",
      "https://uhome.haier.net:9063",
      "https://zj.haier.net",
      "https://uhome.haier.net:443",
      "https://zj-yanshou.haier.net"
      //模块自测使用链接
      // "https://zj.haier.net/omsappapi/"
    ]
  }

  getHttpIniterList(): ArrayList<HttpIniter<HttpClientBuilder>> {
    let list: ArrayList<HttpIniter<HttpClientBuilder>> = new ArrayList();
    list.add(new AppServerHeadersIniter());
    list.add(new AppServerSignIniter());
    list.add(new CacheIniter());
    list.add(new LoggingIniter());
    list.add(new TokenVerifierIniter(true));
    list.add(new TimeOutIniter());
    return list;
  }
}