import { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { HttpIniter } from '../initer/HttpIniter';
import { UpCloudLog } from '../UpCloudLog';
import { AppServerHeadersInterceptor } from './AppServerHeadersInterceptor';
import { common } from '@kit.AbilityKit';

export class AppServerHeadersIniter implements HttpIniter<HttpClientBuilder> {

  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: common.Context): HttpClientBuilder {
    UpCloudLog.debug(`AppServerHeadersIniter.initialize called apiServer = ${apiServer.constructor.name}`);
    if (!builder) {
      builder = new HttpClientBuilder();
    }
    return builder.addInterceptor(new AppServerHeadersInterceptor(apiServer));
  }

}