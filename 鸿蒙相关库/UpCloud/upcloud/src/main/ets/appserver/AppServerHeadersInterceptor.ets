import { Chain, Interceptor, Request, Response } from '@ohos/httpclient';
import { ApiServer } from '../ApiServer';
import { ApiServerConfig } from '../ApiServerConfig';
import { UpCloudConstants } from '../common/UpCloudConstants';
import { Utils } from '../Utils';

export class AppServerHeadersInterceptor implements Interceptor {

  private pendingHeaders: Map<string, string>;

  constructor(apiServer: ApiServer) {
    this.pendingHeaders = new Map();
    this.pendingHeaders.set(UpCloudConstants.HEADER_CONTENT_TYPE, apiServer.getConfig(ApiServerConfig.CONTENT_TYPE));
    this.pendingHeaders.set(UpCloudConstants.HEADER_APP_ID, apiServer.getConfig(ApiServerConfig.APP_ID));
    this.pendingHeaders.set(UpCloudConstants.HEADER_APP_VERSION, apiServer.getConfig(ApiServerConfig.APP_VERSION));
    this.pendingHeaders.set(UpCloudConstants.HEADER_CLIENT_ID, apiServer.getConfig(ApiServerConfig.CLIENT_ID));
    this.pendingHeaders.set(UpCloudConstants.HEADER_ACCESS_TOKEN, apiServer.getConfig(ApiServerConfig.ACCESS_TOKEN));
  }

  intercept(chain: Chain): Promise<Response> {
    let request: Request = chain.requestI();
    request = this.addCommonHeaders(request);
    return chain.proceedI(request);
  }

  private addCommonHeaders(request: Request): Request {
    let builder = request.newBuilder();
    let name: Array<string> = Object.keys(request.headers);
    let timestamp: string = Utils.formatDate(new Date(Date.now()));
    this.pendingHeaders.set(UpCloudConstants.HEADER_SEQUENCE_ID, Utils.generateSequenceId(timestamp));
    this.pendingHeaders.set(UpCloudConstants.HEADER_TIMESTAMP, timestamp);
    this.pendingHeaders.forEach((value, key) => {
      if (!name.includes(key)) {
        builder.addHeader(key, value);
      }
    })
    return builder.build();
  }
}