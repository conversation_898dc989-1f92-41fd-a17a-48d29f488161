import { ApiServer } from '../ApiServer';
import { HttpClientBuilder } from '../initer/builder/HttpClientBuilder';
import { HttpIniter } from '../initer/HttpIniter';
import { UpCloudLog } from '../UpCloudLog';
import { AppServerSignInterceptor } from './AppServerSignInterceptor';
import { common } from '@kit.AbilityKit';

export class AppServerSignIniter implements HttpIniter<HttpClientBuilder> {

  initialize(builder: HttpClientBuilder, apiServer: ApiServer, context: common.Context): HttpClientBuilder {
    UpCloudLog.debug(`AppServerSignIniter.initialize called apiServer = ${apiServer.constructor.name}`);
    if (!builder) {
      builder = new HttpClientBuilder();
    }
    return builder.addInterceptor(new AppServerSignInterceptor(apiServer));
  }

}