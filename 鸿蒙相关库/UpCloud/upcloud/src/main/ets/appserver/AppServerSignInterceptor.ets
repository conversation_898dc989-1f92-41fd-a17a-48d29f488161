import { Chain, Interceptor, Request, RequestBody, Response } from '@ohos/httpclient';
import { HttpUrl } from '@ohos/httpclient/src/main/ets/HttpUrl';
import { ApiServer } from '../ApiServer';
import { ApiServerConfig } from '../ApiServerConfig';
import { UpCloudConstants } from '../common/UpCloudConstants';
import { Utils } from '../Utils';

export class AppServerSignInterceptor implements Interceptor {
  private apiServer: ApiServer;
  private static readonly URL_NEW_SIGN: string = 'oms';

  constructor(apiServer: ApiServer) {
    this.apiServer = apiServer;
  }

  async intercept(chain: Chain): Promise<Response> {
    let request: Request = chain.requestI();
    request = await this.signRequest(request);
    return chain.proceedI(request);
  }

  private async signRequest(request: Request): Promise<Request> {
    let url: string = (request.url as HttpUrl).url;
    let isNewSignRule: boolean = false;
    if (!Utils.isEmpty(url) && url.includes(AppServerSignInterceptor.URL_NEW_SIGN)) {
      isNewSignRule = true;
      url = url.replaceAll(/^(http:\/\/|https:\/\/)[^/]+/g, "");
    }
    let requestBody: RequestBody = request.body as RequestBody;
    let body: string;
    if (requestBody && requestBody.content && requestBody.content !== null) {
      body = requestBody.content as string;
    } else {
      body = "";
    }
    body = body.replace(/[\s\t\r\n]/g, "");
    let signContent: string = body + this.apiServer.getConfig(ApiServerConfig.APP_ID)
      + this.apiServer.getConfig(ApiServerConfig.APP_KEY) + request.getHeader(UpCloudConstants.HEADER_TIMESTAMP);
    signContent = isNewSignRule ? url + signContent : signContent;
    let sign: string = isNewSignRule ? await Utils.sha256(signContent) : await Utils.md5(signContent);
    return request.newBuilder().addHeader(UpCloudConstants.HEADER_SIGNATURE, sign).build();
  }
}