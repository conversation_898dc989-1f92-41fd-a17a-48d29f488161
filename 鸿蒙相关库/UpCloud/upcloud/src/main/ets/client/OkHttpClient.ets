import { HttpClient, Request } from '@ohos/httpclient'
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { UpCloudLog } from '../UpCloudLog';

export class OkHttpClient {
  private client: HttpClient;

  constructor(client: HttpClient) {
    this.client = client;
  }

  execRequest<T>(request: Request, dataType: ClassConstructor<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.client.newCall(request).execute().then((result) => {
        UpCloudLog.debug("OkHttpClient.execRequest result is " + result.result);
        let data: T = plainToInstance(dataType, JSON.parse(result.result));
        resolve(data);
      }).catch((error: Error)=> {
        reject(error);
      });
    });
  }

}