import { hilog } from '@kit.PerformanceAnalysisKit'
import BuildProfile from '../../../BuildProfile';

const LOG_TAG = 'UpCloud(' + BuildProfile.HAR_VERSION + ')';

export class UpCloudLog {
  static error(message: string, ...args: Object[]): void {
    hilog.error(0x0000, LOG_TAG, message, args);
  }

  static warn(message: string, ...args: Object[]): void {
    hilog.warn(0x0000, LOG_TAG, message, args);
  }

  static debug(message: string, ...args: Object[]): void {
    hilog.debug(0x0000, LOG_TAG, message, args);
  }

  static info(message: string, ...args: Object[]): void {
    hilog.info(0x0000, LOG_TAG, message, args);
  }
}