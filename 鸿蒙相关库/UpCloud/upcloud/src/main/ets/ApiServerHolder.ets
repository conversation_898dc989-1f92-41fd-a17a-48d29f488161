import { ApiServer } from './ApiServer'
import { ArrayList, LightWeightMap } from '@kit.ArkTS';
import { UpCloudLog } from './UpCloudLog';
import { UplusAppserver } from './appserver/UplusAppServer';
import { HttpUrl } from '@ohos/httpclient/src/main/ets/HttpUrl';
import { Utils } from './Utils';
import { UwsWebServer } from './uws/UwsWebServer';

export class ApiServerHolder {
  private presetApiServerList: ArrayList<ApiServer> = new ArrayList();
  private apiServerList: ArrayList<ApiServer> = new ArrayList();
  private baseUrlList: ArrayList<string> = new ArrayList();
  private baseUrlMap: LightWeightMap<string, ApiServer> = new LightWeightMap();

  dumpApiServerList(): void {
    UpCloudLog.debug('ApiServerHolder.dumpApiServerList');
    this.baseUrlList.forEach((baseUrl, index) => {
      UpCloudLog.debug(`[${index}] ${baseUrl} -> ${this.baseUrlMap.get(baseUrl)}`);
    });
  }

  registerPresetApiServers() {
    UpCloudLog.debug('ApiServerHolder.registerPresetApiServers() begin');
    this.presetApiServerList.add(new UplusAppserver());
    this.presetApiServerList.add(new UwsWebServer());
    UpCloudLog.debug('ApiServerHolder.registerPresetApiServers() end');
  }

  registerApiServer(apiServer: ApiServer) {
    let apiServerName: string = apiServer.constructor.name;
    UpCloudLog.debug(`ApiServerHolder.registerApiServer() name = ${apiServerName}`);
    if (this.apiServerList.has(apiServer)) {
      UpCloudLog.warn("ERROR! Already existed ApiServer: " + apiServerName);
      return;
    }
    let baseUrlList: string[] | null = this.checkBaseUrlList(apiServer.getBaseUrlList());
    if (baseUrlList !== null) {
      baseUrlList.forEach((baseUrl) => {
        if (this.baseUrlList.has(baseUrl)) {
          UpCloudLog.warn(`ApiServerHolder.registerApiServer() error already exist baseUrl: ${baseUrl}`);
          return;
        }
        this.baseUrlMap.set(baseUrl, apiServer);
        this.baseUrlList.add(baseUrl);
      });
      this.baseUrlList.sort((first, second): number => {
        return second.length - first.length;
      })
      this.apiServerList.add(apiServer);
      UpCloudLog.debug(`ApiServerHolder.registerApiServer() name = ${apiServerName} success`);
    } else {
      UpCloudLog.warn("ApiServerHolder.registerApiServer() error checkBaseUrlList fail !");
    }
  }

  private checkBaseUrlList(baseUrls: string[]): string[] | null {
    if (!baseUrls || baseUrls.length == 0) {
      return null;
    }
    let result: string[] | null = baseUrls;
    baseUrls.forEach((value) => {
      try {
        HttpUrl.get(value);
      } catch (error) {
        UpCloudLog.warn(`ApiServerHolder.checkBaseUrlList() error url is ${value}`);
        result = null;
      }
    });
    return result;
  }

  unregisterApiServer(apiServer: ApiServer) {
    UpCloudLog.debug(`ApiServerHolder.unregisterApiServer() name = ${apiServer.constructor.name}`);
    if (!this.apiServerList.has(apiServer)) {
      return;
    }
    let baseUrlList: string[] = apiServer.getBaseUrlList();
    if (baseUrlList.length == 0) {
      return;
    }
    baseUrlList.forEach((baseUrl) => {
      this.baseUrlList.remove(baseUrl);
      this.baseUrlMap.remove(baseUrl);
    });
    this.apiServerList.remove(apiServer);
  }

  getApiServer(url: string): ApiServer | null {
    if (Utils.isEmpty(url)) {
      return null;
    }
    let remainPreSetApiServerCount: number = 0;
    do {
      for (let baseUrl of this.baseUrlList) {
        if (baseUrl === url) {
          let apiServer: ApiServer = this.baseUrlMap.get(baseUrl);
          if (apiServer) {
            UpCloudLog.debug(`ApiServerHolder.getApiServer url = ${url} match apiserver is ${apiServer.constructor.name}`);
            return apiServer;
          }
        }
      }
      this.registerFirstPresetApiServers();
      remainPreSetApiServerCount = this.presetApiServerList.length;
    } while (remainPreSetApiServerCount > 0);

    for (let baseUrl of this.baseUrlList) {
      if (url.startsWith(baseUrl)) {
        let apiServer: ApiServer = this.baseUrlMap.get(baseUrl);
        if (apiServer) {
          UpCloudLog.debug(`ApiServerHolder.getApiServer two url = ${url} match apiserver is ${apiServer.constructor.name}`);
          return apiServer;
        }
      }
    }
    UpCloudLog.warn(`ApiServerHolder.getApiServer url = ${url} cannot find match apiserver!`);
    return null;
  }

  private registerFirstPresetApiServers() {
    UpCloudLog.debug(`ApiServerHolder.registerFirstPresetApiServers()`);
    if (this.presetApiServerList.length == 0) {
      UpCloudLog.debug(`ApiServerHolder.registerFirstPresetApiServers() preset is empty`);
      return;
    }
    let apiServer: ApiServer = this.presetApiServerList.removeByIndex(0);
    this.registerApiServer(apiServer);
    UpCloudLog.debug(`ApiServerHolder.registerFirstPresetApiServers() apiServer is ${apiServer.constructor.name}`);
  }
}