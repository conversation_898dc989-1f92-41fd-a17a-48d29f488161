{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/axios@2.2.4": "@ohos/axios@2.2.4", "@ohos/crypto-js@^2.0.2": "@ohos/crypto-js@2.0.4", "@ohos/hamock@1.0.0": "@ohos/hamock@1.0.0", "@ohos/httpclient@^2.0.4": "@ohos/httpclient@2.0.4", "@ohos/hypium@1.0.18": "@ohos/hypium@1.0.18", "@yunkss/eftool@1.2.3": "@yunkss/eftool@1.2.3", "base64-js@^1.5.1": "base64-js@1.5.1", "class-transformer@^0.5.1": "class-transformer@0.5.1", "pako@^2.1.0": "pako@2.1.0", "reflect-metadata@^0.1.13": "reflect-metadata@0.1.13"}, "packages": {"@ohos/axios@2.2.4": {"name": "@ohos/axios", "version": "2.2.4", "integrity": "sha512-5oH753kGFwfJP1kvCnI92RRaSqRbN0b24vmNNrUV+T8+5ako98rtVQOhA7ezMPox5Z3PFWndLQTF/P/hEK/yeQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/axios/-/axios-2.2.4.har", "registryType": "ohpm"}, "@ohos/crypto-js@2.0.4": {"name": "@ohos/crypto-js", "version": "2.0.4", "integrity": "sha512-589ur6oqU1UNibqefMly2cwEeEhkSoCAA3uc+oNUwRnYYtevn/kQnO+Coi36N+VJSeeg/uFzZk1K/wUMdovpOA==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/crypto-js/-/crypto-js-2.0.4.har", "registryType": "ohpm"}, "@ohos/hamock@1.0.0": {"name": "@ohos/hamock", "version": "1.0.0", "integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hamock/-/hamock-1.0.0.har", "registryType": "ohpm"}, "@ohos/httpclient@2.0.4": {"name": "@ohos/httpclient", "version": "2.0.4", "integrity": "sha512-Ky7TJuRN3/BYBaWyqDB4PktZ1p4YdUhEqHRBVl+b+p2B203LYnjU3tiwo0jb2sbSTi+T5WnieoX6sNNFttvEmQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/httpclient/-/httpclient-2.0.4.har", "registryType": "ohpm", "dependencies": {"pako": "^2.1.0", "@ohos/crypto-js": "^2.0.2", "base64-js": "^1.5.1"}}, "@ohos/hypium@1.0.18": {"name": "@ohos/hypium", "version": "1.0.18", "integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "resolved": "https://repo.harmonyos.com/ohpm/@ohos/hypium/-/hypium-1.0.18.har", "registryType": "ohpm"}, "@yunkss/eftool@1.2.3": {"name": "@yunkss/eftool", "version": "1.2.3", "integrity": "sha512-mu8NTyKmksquwbA4XRCAcdBiCFDK2cKiisOgv3G9sQw2AI5C+Ea1nPhRhnjppeZmF02hRzvv22WHtQnnUF68Bg==", "resolved": "https://repo.harmonyos.com/ohpm/@yunkss/eftool/-/eftool-1.2.3.har", "registryType": "ohpm"}, "base64-js@1.5.1": {"name": "base64-js", "version": "1.5.1", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "resolved": "https://repo.harmonyos.com/ohpm/base64-js/-/base64-js-1.5.1.tgz", "shasum": "1b1b440160a5bf7ad40b650f095963481903930a", "registryType": "ohpm"}, "class-transformer@0.5.1": {"name": "class-transformer", "version": "0.5.1", "integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "resolved": "https://repo.harmonyos.com/ohpm/class-transformer/-/class-transformer-0.5.1.tgz", "shasum": "24147d5dffd2a6cea930a3250a677addf96ab336", "registryType": "ohpm"}, "pako@2.1.0": {"name": "pako", "version": "2.1.0", "integrity": "sha512-w+eufiZ1WuJYgPXbV/PO3NCMEc3xqylkKHzp8bxp1uW4qaSNQUkwmLLEc3kKsfz8lpV1F8Ht3U1Cm+9Srog2ug==", "resolved": "https://repo.harmonyos.com/ohpm/pako/-/pako-2.1.0.tgz", "shasum": "266cc37f98c7d883545d11335c00fbd4062c9a86", "registryType": "ohpm"}, "reflect-metadata@0.1.13": {"name": "reflect-metadata", "version": "0.1.13", "integrity": "sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==", "resolved": "https://repo.harmonyos.com/ohpm/reflect-metadata/-/reflect-metadata-0.1.13.tgz", "shasum": "67ae3ca57c972a2aa1642b10fe363fe32d49dc08", "registryType": "ohpm"}}}