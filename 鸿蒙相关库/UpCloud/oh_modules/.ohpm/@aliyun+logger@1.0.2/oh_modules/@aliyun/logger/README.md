# logger

aliyun SDK内部使用日志接口。

## 下载安装

```shell
ohpm install @aliyun/logger
```

## 使用

### 创建模块日志管理接口

```typescript
import { createLog } from '@aliyun/logger';

const MODULE_DOMAIN = 0x0001;

export const log = createLog(MODULE_DOMAIN);
```

MODULE_DOMAIN为输出日志时，使用的domain参数，参考hilog的domain字段。

### 创建日志接口, 输出日志

```typescript
const logI = log.createLogI({ tag: 'loggerTest' });

// 日志输出
logI.d("debug 日志");
logI.i("info 日志");
logI.w("warn 日志");
logI.e("error 日志");

// 日志输出，延迟日志获取
logI.d(()=>"debug 日志");
logI.i(()=>"info 日志");
logI.w(()=>"warn 日志");
logI.e(()=>"error 日志");
```

### 日志输出到hilog

```typescript
log.enableHiLogLogger();
```

### 注册其它日志接口
创建其它日志接口
```typescript
import { Logger } from '@aliyun/logger';
import { hilog } from '@kit.PerformanceAnalysisKit';

class MyLogger implements Logger {
  print(domain: number, tag: string, level: hilog.LogLevel, msg: string): void {
    // 输出日志到其它日志系统
    hilog.info(domain, tag, `logger -> ${level} ${msg}`);
  }
}

export const logger = new MyLogger();
```

注册日志接口
```typescript
log.addLogger(logger);
```

移除日志接口
```typescript
log.removeLogger(logger);
```