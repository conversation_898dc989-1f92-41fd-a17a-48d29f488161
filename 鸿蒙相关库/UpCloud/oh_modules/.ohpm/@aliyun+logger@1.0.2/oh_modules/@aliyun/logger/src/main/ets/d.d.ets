// @keepTs
// @ts-nocheck
import hilog from '@ohos.hilog';
export type getMsg = () => string;
export interface Logger {
    print(domain: number, tag: string, level: hilog.LogLevel, msg: string): void;
}
export interface LogI {
    d(msg: string | getMsg): void;
    i(msg: string | getMsg): void;
    w(msg: string | getMsg): void;
    e(msg: string | getMsg): void;
    log(level: hilog.LogLevel, msg: string | getMsg): void;
}
export interface LogIOptions {
    domain?: number;
    tag: string;
}
export interface ILogManager {
    createLogI(options: LogIOptions): LogI;
    enableHiLogLogger(): void;
    addLogger(logger: Logger): void;
    removeLogger(logger: Logger): void;
    reset(): void;
}
export declare function createLog(domain: number): ILogManager;
