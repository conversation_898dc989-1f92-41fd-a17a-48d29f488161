<!doctype html>
<head><meta charset="utf-8">
<title>Metadata Proposal - ECMAScript</title>
<link rel="stylesheet" href="ecmarkup.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/8.4/styles/github.min.css">
<script src="ecmarkup.js"></script>
<script>
  if (location.hostname === 'rbuckton.github.io' && location.protocol !== 'https:') {
    location.protocol = 'https:';
  }
</script>
<script type="application/json" id="menu-search-biblio">[{"type":"clause","id":"introduction","aoid":null,"title":"Metadata Proposal - ECMAScript","titleHTML":"Metadata Proposal - ECMAScript","number":"","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Metadata Proposal - ECMAScript"},{"type":"clause","id":"syntax","aoid":null,"title":"Syntax","titleHTML":"Syntax","number":"1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Syntax"},{"type":"op","aoid":"GetOrCreateMetadataMap","refId":"getorcreatemetadatamap","location":"","referencingIds":[],"key":"GetOrCreateMetadataMap"},{"type":"clause","id":"getorcreatemetadatamap","aoid":"GetOrCreateMetadataMap","title":"GetOrCreateMetadataMap ( O, P, Create )","titleHTML":"GetOrCreateMetadataMap ( O, P, Create )","number":"2.1.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_8","_ref_17","_ref_22","_ref_42","_ref_52"],"key":"GetOrCreateMetadataMap ( O, P, Create )"},{"type":"clause","id":"operations-on-objects","aoid":null,"title":"Operations on Objects","titleHTML":"Operations on Objects","number":"2.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Operations on Objects"},{"type":"clause","id":"abstract-operations","aoid":null,"title":"Abstract Operations","titleHTML":"Abstract Operations","number":"2","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Abstract Operations"},{"type":"op","aoid":"OrdinaryHasMetadata","refId":"ordinaryhasmetadata","location":"","referencingIds":[],"key":"OrdinaryHasMetadata"},{"type":"clause","id":"ordinaryhasmetadata","aoid":"OrdinaryHasMetadata","title":"OrdinaryHasMetadata ( MetadataKey, O, P )","titleHTML":"OrdinaryHasMetadata ( MetadataKey, O, P )","number":"*******","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_3"],"key":"OrdinaryHasMetadata ( MetadataKey, O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-hasmetadata","aoid":null,"title":"[[HasMetadata]] ( MetadataKey, P )","titleHTML":"[[HasMetadata]] ( MetadataKey, P )","number":"3.1.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[HasMetadata]] ( MetadataKey, P )"},{"type":"op","aoid":"OrdinaryHasOwnMetadata","refId":"ordinaryhasownmetadata","location":"","referencingIds":[],"key":"OrdinaryHasOwnMetadata"},{"type":"clause","id":"ordinaryhasownmetadata","aoid":"OrdinaryHasOwnMetadata","title":"OrdinaryHasOwnMetadata ( MetadataKey, O, P )","titleHTML":"OrdinaryHasOwnMetadata ( MetadataKey, O, P )","number":"*******","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_5","_ref_6","_ref_13"],"key":"OrdinaryHasOwnMetadata ( MetadataKey, O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-hasownmetadata","aoid":null,"title":"[[HasOwnMetadata]] ( MetadataKey, P )","titleHTML":"[[HasOwnMetadata]] ( MetadataKey, P )","number":"3.1.2","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[HasOwnMetadata]] ( MetadataKey, P )"},{"type":"op","aoid":"OrdinaryGetMetadata","refId":"ordinarygetmetadata","location":"","referencingIds":[],"key":"OrdinaryGetMetadata"},{"type":"clause","id":"ordinarygetmetadata","aoid":"OrdinaryGetMetadata","title":"OrdinaryGetMetadata ( MetadataKey, O, P )","titleHTML":"OrdinaryGetMetadata ( MetadataKey, O, P )","number":"*******","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_11"],"key":"OrdinaryGetMetadata ( MetadataKey, O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-getmetadata","aoid":null,"title":"[[GetMetadata]] ( MetadataKey, P )","titleHTML":"[[GetMetadata]] ( MetadataKey, P )","number":"3.1.3","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[GetMetadata]] ( MetadataKey, P )"},{"type":"op","aoid":"OrdinaryGetOwnMetadata","refId":"ordinarygetownmetadata","location":"","referencingIds":[],"key":"OrdinaryGetOwnMetadata"},{"type":"clause","id":"ordinarygetownmetadata","aoid":"OrdinaryGetOwnMetadata","title":"OrdinaryGetOwnMetadata ( MetadataKey, O, P )","titleHTML":"OrdinaryGetOwnMetadata ( MetadataKey, O, P )","number":"*******","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_14","_ref_15"],"key":"OrdinaryGetOwnMetadata ( MetadataKey, O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-getownmetadata","aoid":null,"title":"[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )","titleHTML":"[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )","number":"3.1.4","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )"},{"type":"op","aoid":"OrdinaryDefineOwnMetadata","refId":"ordinarydefineownmetadata","location":"","referencingIds":[],"key":"OrdinaryDefineOwnMetadata"},{"type":"clause","id":"ordinarydefineownmetadata","aoid":"OrdinaryDefineOwnMetadata","title":"OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )","titleHTML":"OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )","number":"*******","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_19"],"key":"OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-defineownmetadata","aoid":null,"title":"[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )","titleHTML":"[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )","number":"3.1.5","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )"},{"type":"op","aoid":"OrdinaryMetadataKeys","refId":"ordinarymetadatakeys","location":"","referencingIds":[],"key":"OrdinaryMetadataKeys"},{"type":"clause","id":"ordinarymetadatakeys","aoid":"OrdinaryMetadataKeys","title":"OrdinaryMetadataKeys ( O, P )","titleHTML":"OrdinaryMetadataKeys ( O, P )","number":"3.1.6.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_24"],"key":"OrdinaryMetadataKeys ( O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-metadatakeys","aoid":null,"title":"[[MetadataKeys]] ( P )","titleHTML":"[[MetadataKeys]] ( P )","number":"3.1.6","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[MetadataKeys]] ( P )"},{"type":"op","aoid":"OrdinaryOwnMetadataKeys","refId":"ordinaryownmetadatakeys","location":"","referencingIds":[],"key":"OrdinaryOwnMetadataKeys"},{"type":"clause","id":"ordinaryownmetadatakeys","aoid":"OrdinaryOwnMetadataKeys","title":"OrdinaryOwnMetadataKeys ( O, P )","titleHTML":"OrdinaryOwnMetadataKeys ( O, P )","number":"3.1.7.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":["_ref_26","_ref_39"],"key":"OrdinaryOwnMetadataKeys ( O, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-ownmetadatakeys","aoid":null,"title":"[[OwnMetadataKeys]] ( P )","titleHTML":"[[OwnMetadataKeys]] ( P )","number":"3.1.7","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[OwnMetadataKeys]] ( P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots-deletemetadata","aoid":null,"title":"[[DeleteMetadata]]( MetadataKey, P )","titleHTML":"[[DeleteMetadata]]( MetadataKey, P )","number":"3.1.8","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"[[DeleteMetadata]]( MetadataKey, P )"},{"type":"clause","id":"ordinary-object-internal-methods-and-internal-slots","aoid":null,"title":"Ordinary Object Internal Methods and Internal Slots","titleHTML":"Ordinary Object Internal Methods and Internal Slots","number":"3.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Ordinary Object Internal Methods and Internal Slots"},{"type":"clause","id":"ordinary-and-exotic-objects-behaviors","aoid":null,"title":"Ordinary and Exotic Objects Behaviors","titleHTML":"Ordinary and Exotic Objects Behaviors","number":"3","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Ordinary and Exotic Objects Behaviors"},{"type":"clause","id":"reflect-metadatadecoratorfunctions","aoid":null,"title":"Metadata Decorator Functions","titleHTML":"Metadata Decorator Functions","number":"4.1.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Metadata Decorator Functions"},{"type":"clause","id":"reflect.metadata","aoid":null,"title":"Reflect.metadata ( metadataKey, metadataValue )","titleHTML":"Reflect.metadata ( metadataKey, metadataValue )","number":"4.1.2","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.metadata ( metadataKey, metadataValue )"},{"type":"clause","id":"reflect.definemetadata","aoid":null,"title":"Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )","titleHTML":"Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )","number":"4.1.3","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )"},{"type":"clause","id":"reflect.hasmetadata","aoid":null,"title":"Reflect.hasMetadata ( metadataKey, target [, propertyKey] )","titleHTML":"Reflect.hasMetadata ( metadataKey, target [, propertyKey] )","number":"4.1.4","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.hasMetadata ( metadataKey, target [, propertyKey] )"},{"type":"clause","id":"reflect.hasownmetadata","aoid":null,"title":"Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )","titleHTML":"Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )","number":"4.1.5","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )"},{"type":"clause","id":"reflect.getmetadata","aoid":null,"title":"Reflect.getMetadata ( metadataKey, target [, propertyKey] )","titleHTML":"Reflect.getMetadata ( metadataKey, target [, propertyKey] )","number":"4.1.6","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.getMetadata ( metadataKey, target [, propertyKey] )"},{"type":"clause","id":"reflect.getownmetadata","aoid":null,"title":"Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )","titleHTML":"Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )","number":"4.1.7","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )"},{"type":"clause","id":"reflect.getmetadatakeys","aoid":null,"title":"Reflect.getMetadataKeys ( target [, propertyKey] )","titleHTML":"Reflect.getMetadataKeys ( target [, propertyKey] )","number":"4.1.8","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.getMetadataKeys ( target [, propertyKey] )"},{"type":"clause","id":"reflect.getownmetadatakeys","aoid":null,"title":"Reflect.getOwnMetadataKeys ( target [, propertyKey] )","titleHTML":"Reflect.getOwnMetadataKeys ( target [, propertyKey] )","number":"4.1.9","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.getOwnMetadataKeys ( target [, propertyKey] )"},{"type":"clause","id":"reflect.deletemetadata","aoid":null,"title":"Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )","titleHTML":"Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )","number":"4.1.10","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )"},{"type":"clause","id":"reflect","aoid":null,"title":"The Reflect Object","titleHTML":"The Reflect Object","number":"4.1","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"The Reflect Object"},{"type":"clause","id":"reflection","aoid":null,"title":"Reflection","titleHTML":"Reflection","number":"4","namespace":"https://rbuckton.github.io/reflect-metadata","location":"","referencingIds":[],"key":"Reflection"}]</script><script>"use strict";

function Search(menu) {
  this.menu = menu;
  this.$search = document.getElementById('menu-search');
  this.$searchBox = document.getElementById('menu-search-box');
  this.$searchResults = document.getElementById('menu-search-results');
  
  this.loadBiblio();
  
  document.addEventListener('keydown', this.documentKeydown.bind(this));
  
  this.$searchBox.addEventListener('keydown', debounce(this.searchBoxKeydown.bind(this), { stopPropagation: true }));
  this.$searchBox.addEventListener('keyup', debounce(this.searchBoxKeyup.bind(this), { stopPropagation: true }));
}

Search.prototype.loadBiblio = function () {
  var $biblio = document.getElementById('menu-search-biblio');
  if (!$biblio) {
    this.biblio = [];
  } else {
    this.biblio = JSON.parse($biblio.textContent);
    this.biblio.clauses = this.biblio.filter(function (e) { return e.type === 'clause' });
    this.biblio.byId = this.biblio.reduce(function (map, entry) {
      map[entry.id] = entry;
      return map;
    }, {});
  }
}

Search.prototype.documentKeydown = function (e) {
  if (e.keyCode === 191) {
    e.preventDefault();
    e.stopPropagation();
    this.triggerSearch();
  }
}

Search.prototype.searchBoxKeydown = function (e) {
  e.stopPropagation();
  e.preventDefault();
  if (e.keyCode === 191 && e.target.value.length === 0) {
    e.preventDefault();
  } else if (e.keyCode === 13) {
    e.preventDefault();
    this.selectResult();
  }
}

Search.prototype.searchBoxKeyup = function (e) {
  if (e.keyCode === 13 || e.keyCode === 9) {
    return;
  }
  
  this.search(e.target.value);
}


Search.prototype.triggerSearch = function (e) {
  if (this.menu.isVisible()) {
    this._closeAfterSearch = false;
  } else {
    this._closeAfterSearch = true;
    this.menu.show();
  }

  this.$searchBox.focus();
  this.$searchBox.select();
}
// bit 12 - Set if the result starts with searchString
// bits 8-11: 8 - number of chunks multiplied by 2 if cases match, otherwise 1.
// bits 1-7: 127 - length of the entry
// General scheme: prefer case sensitive matches with fewer chunks, and otherwise
// prefer shorter matches.
function relevance(result, searchString) {
  var relevance = 0;
  
  relevance = Math.max(0, 8 - result.match.chunks) << 7;
  
  if (result.match.caseMatch) {
    relevance *= 2;
  }
  
  if (result.match.prefix) {
    relevance += 2048
  }
  
  relevance += Math.max(0, 255 - result.entry.key.length);
  
  return relevance;
}

Search.prototype.search = function (searchString) {
  var s = Date.now();

  if (searchString === '') {
    this.displayResults([]);
    this.hideSearch();
    return;
  } else {
    this.showSearch();
  }
  
  if (searchString.length === 1) {
    this.displayResults([]);
    return;
  }
    
  var results;

  if (/^[\d\.]*$/.test(searchString)) {
    results = this.biblio.clauses.filter(function (clause) {
      return clause.number.substring(0, searchString.length) === searchString;
    }).map(function (clause) {
      return { entry: clause };
    });
  } else {
    results = [];
    
    for (var i = 0; i < this.biblio.length; i++) {
      var entry = this.biblio[i];
      if (!entry.key) {
        // biblio entries without a key aren't searchable
        continue;
      }

      var match = fuzzysearch(searchString, entry.key);
      if (match) {
        results.push({ entry: entry, match: match });
      }
    }
  
    results.forEach(function (result) {
      result.relevance = relevance(result, searchString);
    });
    
    results = results.sort(function (a, b) { return b.relevance - a.relevance });

  }

  if (results.length > 50) {
    results = results.slice(0, 50);
  }
  
  this.displayResults(results);
}
Search.prototype.hideSearch = function () {
  this.$search.classList.remove('active');
}

Search.prototype.showSearch = function () {
  this.$search.classList.add('active');
}

Search.prototype.selectResult = function () {
  var $first = this.$searchResults.querySelector('li:first-child a');

  if ($first) {
    document.location = $first.getAttribute('href');
  }
  
  this.$searchBox.value = '';
  this.$searchBox.blur();
  this.displayResults([]);
  this.hideSearch();

  if (this._closeAfterSearch) {
    this.menu.hide();
  }
}

Search.prototype.displayResults = function (results) {
  if (results.length > 0) {
    this.$searchResults.classList.remove('no-results');
    
    var html = '<ul>';

    results.forEach(function (result) {
      var entry = result.entry;
      var id = entry.id;
      var cssClass = '';
      var text = '';

      if (entry.type === 'clause') {
        var number = entry.number ? entry.number + ' ' : '';
        text = number + entry.key;
        cssClass = 'clause';
        id = entry.id;
      } else if (entry.type === 'production') {
        text = entry.key;
        cssClass = 'prod';
        id = entry.id;  
      } else if (entry.type === 'op') {
        text = entry.key;
        cssClass = 'op';
        id = entry.id || entry.refId;
      } else if (entry.type === 'term') {
        text = entry.key;
        cssClass = 'term';
        id = entry.id || entry.refId;
      }

      if (text) {
        html += '<li class=menu-search-result-' + cssClass + '><a href="#' + id + '">' + text + '</a></li>'
      }
    });

    html += '</ul>'

    this.$searchResults.innerHTML = html;
  } else {
    this.$searchResults.innerHTML = '';
    this.$searchResults.classList.add('no-results');
  }
}


function Menu() {
  this.$toggle = document.getElementById('menu-toggle');
  this.$menu = document.getElementById('menu');
  this.$toc = document.querySelector('menu-toc > ol');
  this.$pins = document.querySelector('#menu-pins');
  this.$pinList = document.getElementById('menu-pins-list');
  this.$toc = document.querySelector('#menu-toc > ol');
  this.$specContainer = document.getElementById('spec-container');
  this.search = new Search(this);
  
  this._pinnedIds = {}; 
  this.loadPinEntries();

  // toggle menu
  this.$toggle.addEventListener('click', this.toggle.bind(this));

  // keydown events for pinned clauses
  document.addEventListener('keydown', this.documentKeydown.bind(this));

  // toc expansion
  var tocItems = this.$menu.querySelectorAll('#menu-toc li');
  for (var i = 0; i < tocItems.length; i++) {
    var $item = tocItems[i];
    $item.addEventListener('click', function($item, event) {
      $item.classList.toggle('active');
      event.stopPropagation();
    }.bind(null, $item));
  }

  // close toc on toc item selection
  var tocLinks = this.$menu.querySelectorAll('#menu-toc li > a');
  for (var i = 0; i < tocLinks.length; i++) {
    var $link = tocLinks[i];
    $link.addEventListener('click', function(event) {
      this.toggle();
      event.stopPropagation();
    }.bind(this));
  }

  // update active clause on scroll
  window.addEventListener('scroll', debounce(this.updateActiveClause.bind(this)));
  this.updateActiveClause();

  // prevent menu scrolling from scrolling the body
  this.$toc.addEventListener('wheel', function (e) {
    var target = e.currentTarget;
    var offTop = e.deltaY < 0 && target.scrollTop === 0;
    if (offTop) {
      e.preventDefault();
    }
    var offBottom = e.deltaY > 0
                    && target.offsetHeight + target.scrollTop >= target.scrollHeight;

    if (offBottom) {
		  e.preventDefault();
	  }
  })
}

Menu.prototype.documentKeydown = function (e) {
  e.stopPropagation();
  if (e.keyCode === 80) {
    this.togglePinEntry();
  } else if (e.keyCode > 48 && e.keyCode < 58) {
    this.selectPin(e.keyCode - 49);
  }
}

Menu.prototype.updateActiveClause = function () {
  this.setActiveClause(findActiveClause(this.$specContainer))
}

Menu.prototype.setActiveClause = function (clause) {
  this.$activeClause = clause;
  this.revealInToc(this.$activeClause);
}

Menu.prototype.revealInToc = function (path) {
  var current = this.$toc.querySelectorAll('li.revealed');
  for (var i = 0; i < current.length; i++) {
    current[i].classList.remove('revealed');
    current[i].classList.remove('revealed-leaf');
  }
  
  var current = this.$toc;
  var index = 0;
  while (index < path.length) {
    var children = current.children;
    for (var i = 0; i < children.length; i++) {
      if ('#' + path[index].id === children[i].children[1].getAttribute('href') ) {
        children[i].classList.add('revealed');
        if (index === path.length - 1) {
          children[i].classList.add('revealed-leaf');
          var rect = children[i].getBoundingClientRect();
          this.$toc.getBoundingClientRect().top
          var tocRect = this.$toc.getBoundingClientRect();
          if (rect.top + 10 > tocRect.bottom) {
            this.$toc.scrollTop = this.$toc.scrollTop + (rect.top - tocRect.bottom) + (rect.bottom - rect.top);
          } else if (rect.top < tocRect.top) {
            this.$toc.scrollTop = this.$toc.scrollTop - (tocRect.top - rect.top);
          }
        }
        current = children[i].querySelector('ol');
        index++;
        break;
      }      
    }
    
  }
}

function findActiveClause(root, path) {
  var clauses = new ClauseWalker(root);
  var $clause;
  var found = false;
  var path = path || [];
  
  while ($clause = clauses.nextNode()) {
    var rect = $clause.getBoundingClientRect();
    var $header = $clause.children[0];
    var marginTop = parseInt(getComputedStyle($header)["margin-top"]);
    
    if ((rect.top - marginTop) <= 0 && rect.bottom > 0) {
      found = true;
      return findActiveClause($clause, path.concat($clause)) || path;
    }
  }
  
  return path;
}

function ClauseWalker(root) {
  var previous;
  var treeWalker = document.createTreeWalker(
    root,
    NodeFilter.SHOW_ELEMENT,
    {
      acceptNode: function (node) {
        if (previous === node.parentNode) {
          return NodeFilter.FILTER_REJECT;
        } else {
          previous = node;
        }
        if (node.nodeName === 'EMU-CLAUSE' || node.nodeName === 'EMU-INTRO' || node.nodeName === 'EMU-ANNEX') {
          return NodeFilter.FILTER_ACCEPT;
        } else {
          return NodeFilter.FILTER_SKIP;
        }
      }
    },
    false
    );
  
  return treeWalker;
}

Menu.prototype.toggle = function () {
  this.$menu.classList.toggle('active');
}

Menu.prototype.show = function () {
  this.$menu.classList.add('active');
}

Menu.prototype.hide = function () {
  this.$menu.classList.remove('active');
}

Menu.prototype.isVisible = function() {
  return this.$menu.classList.contains('active');
}

Menu.prototype.showPins = function () {
  this.$pins.classList.add('active');
}

Menu.prototype.hidePins = function () {
  this.$pins.classList.remove('active');
}

Menu.prototype.addPinEntry = function (id) {
  var entry = this.search.biblio.byId[id];
  if (!entry) {
    // id was deleted after pin (or something) so remove it
    delete this._pinnedIds[id];
    this.persistPinEntries();
    return;
  }

  if (entry.type === 'clause') {
    var prefix;
    if (entry.number) {
      prefix = entry.number + ' ';
    } else {
      prefix = '';
    }
    this.$pinList.innerHTML += '<li><a href="#' + entry.id + '">' + prefix + entry.titleHTML + '</a></li>';
  } else {
    this.$pinList.innerHTML += '<li><a href="#' + entry.id + '">' + entry.key + '</a></li>';
  }

  if (Object.keys(this._pinnedIds).length === 0) {
    this.showPins();
  }
  this._pinnedIds[id] = true;
  this.persistPinEntries();
}

Menu.prototype.removePinEntry = function (id) {
  var item = this.$pinList.querySelector('a[href="#' + id + '"]').parentNode;
  this.$pinList.removeChild(item);
  delete this._pinnedIds[id];
  if (Object.keys(this._pinnedIds).length === 0) {
    this.hidePins();
  }

  this.persistPinEntries();
}

Menu.prototype.persistPinEntries = function () {
  try {
    if (!window.localStorage) return;
  } catch (e) {
    return;
  }

  localStorage.pinEntries = JSON.stringify(Object.keys(this._pinnedIds));
}

Menu.prototype.loadPinEntries = function () {
  try {
    if (!window.localStorage) return;
  } catch (e) {
    return;
  }
  
  var pinsString = window.localStorage.pinEntries;
  if (!pinsString) return;
  var pins = JSON.parse(pinsString);
  for(var i = 0; i < pins.length; i++) {
    this.addPinEntry(pins[i]);
  }
}

Menu.prototype.togglePinEntry = function (id) {
  if (!id) {
    id = this.$activeClause[this.$activeClause.length - 1].id;
  }

  if (this._pinnedIds[id]) {
    this.removePinEntry(id);
  } else {
    this.addPinEntry(id);
  }
}

Menu.prototype.selectPin = function (num) {
  document.location = this.$pinList.children[num].children[0].href;
}

var menu;
function init() {
  menu = new Menu();
  var $container = document.getElementById('spec-container');
  $container.addEventListener('mouseover', debounce(function (e) {
    Toolbox.activateIfMouseOver(e);
  }));
}

document.addEventListener('DOMContentLoaded', init);

function debounce(fn, opts) {
  opts = opts || {};
  var timeout;
  return function(e) {
    if (opts.stopPropagation) {
      e.stopPropagation();
    }
    var args = arguments;
    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(function() {
      timeout = null;
      fn.apply(this, args);
    }.bind(this), 150);
  }
}

var CLAUSE_NODES = ['EMU-CLAUSE', 'EMU-INTRO', 'EMU-ANNEX'];
function findLocalReferences ($elem) {
  var name = $elem.innerHTML;
  var references = [];

  var parentClause = $elem.parentNode;
  while (parentClause && CLAUSE_NODES.indexOf(parentClause.nodeName) === -1) {
    parentClause = parentClause.parentNode;
  }

  if(!parentClause) return;

  var vars = parentClause.querySelectorAll('var');

  for (var i = 0; i < vars.length; i++) {
    var $var = vars[i];

    if ($var.innerHTML === name) {
      references.push($var);
    }
  }

  return references;
}

function toggleFindLocalReferences($elem) {
  var references = findLocalReferences($elem);
  if ($elem.classList.contains('referenced')) {
    references.forEach(function ($reference) {
      $reference.classList.remove('referenced');
    });
  } else {
    references.forEach(function ($reference) {
      $reference.classList.add('referenced');
    });
  }
}

function installFindLocalReferences () {
  document.addEventListener('click', function (e) {
    if (e.target.nodeName === 'VAR') {
      toggleFindLocalReferences(e.target);
    }
  });
}

document.addEventListener('DOMContentLoaded', installFindLocalReferences);




// The following license applies to the fuzzysearch function
// The MIT License (MIT)
// Copyright © 2015 Nicolas Bevacqua
// Copyright © 2016 Brian Terlson
// Permission is hereby granted, free of charge, to any person obtaining a copy of
// this software and associated documentation files (the "Software"), to deal in
// the Software without restriction, including without limitation the rights to
// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
// the Software, and to permit persons to whom the Software is furnished to do so,
// subject to the following conditions:

// The above copyright notice and this permission notice shall be included in all
// copies or substantial portions of the Software.

// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
function fuzzysearch (searchString, haystack, caseInsensitive) {
  var tlen = haystack.length;
  var qlen = searchString.length;
  var chunks = 1;
  var finding = false;
  var prefix = true;
  
  if (qlen > tlen) {
    return false;
  }
  
  if (qlen === tlen) {
    if (searchString === haystack) {
      return { caseMatch: true, chunks: 1, prefix: true };
    } else if (searchString.toLowerCase() === haystack.toLowerCase()) {
      return { caseMatch: false, chunks: 1, prefix: true };
    } else {
      return false;
    }
  }
  
  outer: for (var i = 0, j = 0; i < qlen; i++) {
    var nch = searchString[i];
    while (j < tlen) {
      var targetChar = haystack[j++];
      if (targetChar === nch) {
        finding = true;
        continue outer;
      }
      if (finding) {
        chunks++;
        finding = false;
      }
    }
    
    if (caseInsensitive) { return false }
    
    return fuzzysearch(searchString.toLowerCase(), haystack.toLowerCase(), true);
  }
  
  return { caseMatch: !caseInsensitive, chunks: chunks, prefix: j <= qlen };
}

var Toolbox = {
  init: function () {
    this.$container = document.createElement('div');
    this.$container.classList.add('toolbox');
    this.$permalink = document.createElement('a');
    this.$permalink.textContent = 'Permalink';
    this.$pinLink = document.createElement('a');
    this.$pinLink.textContent = 'Pin';
    this.$pinLink.setAttribute('href', '#');
    this.$pinLink.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      menu.togglePinEntry(this.entry.id);
    }.bind(this));

    this.$refsLink = document.createElement('a');
    this.$refsLink.setAttribute('href', '#');
    this.$refsLink.addEventListener('click', function (e) {
      e.preventDefault();
      e.stopPropagation();
      referencePane.showReferencesFor(this.entry);
    }.bind(this));
    this.$container.appendChild(this.$permalink);
    this.$container.appendChild(this.$pinLink);
    this.$container.appendChild(this.$refsLink);
    document.body.appendChild(this.$container);
  },

  activate: function (el, entry, target) {
    if (el === this._activeEl) return;
    this.active = true;
    this.entry = entry;
    this.$container.classList.add('active');
    this.top = el.offsetTop - this.$container.offsetHeight - 10;
    this.left = el.offsetLeft;
    this.$container.setAttribute('style', 'left: ' + this.left + 'px; top: ' + this.top + 'px');
    this.updatePermalink();
    this.updateReferences();
    this._activeEl = el;
    if (this.top < document.body.scrollTop && el === target) {
      // don't scroll unless it's a small thing (< 200px)
      this.$container.scrollIntoView();
    }
  },

  updatePermalink: function () {
    this.$permalink.setAttribute('href', '#' + this.entry.id);
  },

  updateReferences: function () {
    this.$refsLink.textContent = 'References (' + this.entry.referencingIds.length + ')';
  },

  activateIfMouseOver: function (e) {
    var ref = this.findReferenceUnder(e.target);
    if (ref && (!this.active || e.pageY > this._activeEl.offsetTop)) {
      var entry = menu.search.biblio.byId[ref.id];
      this.activate(ref.element, entry, e.target);
    } else if (this.active && ((e.pageY < this.top) || e.pageY > (this._activeEl.offsetTop + this._activeEl.offsetHeight))) {
      this.deactivate();
    }
  },

  findReferenceUnder: function (el) {
    while (el) {
      var parent = el.parentNode;
      if (el.nodeName === 'H1' && parent.nodeName.match(/EMU-CLAUSE|EMU-ANNEX|EMU-INTRO/) && parent.id) {
        return { element: el, id: parent.id };
      } else if (el.nodeName.match(/EMU-(?!CLAUSE|XREF|ANNEX|INTRO)|DFN/) &&
                el.id && el.id[0] !== '_') {
        if (el.nodeName === 'EMU-FIGURE' || el.nodeName === 'EMU-TABLE' || el.nodeName === 'EMU-EXAMPLE') {
          // return the figcaption element
          return { element: el.children[0].children[0], id: el.id };
        } else if (el.nodeName === 'EMU-PRODUCTION') {
          // return the LHS non-terminal element
          return { element: el.children[0], id: el.id };
        } else {
          return { element: el, id: el.id };
        }
      }
      el = parent;
    }
  },

  deactivate: function () {
    this.$container.classList.remove('active');
    this._activeEl = null;
    this.activeElBounds = null;
    this.active = false;
  }
}

var referencePane = {
  init: function() {
    this.$container = document.createElement('div');
    this.$container.setAttribute('id', 'references-pane-container');

    var $spacer = document.createElement('div');
    $spacer.setAttribute('id', 'references-pane-spacer');

    this.$pane = document.createElement('div');
    this.$pane.setAttribute('id', 'references-pane');

    this.$container.appendChild($spacer);
    this.$container.appendChild(this.$pane);

    this.$header = document.createElement('div');
    this.$header.classList.add('menu-pane-header');
    this.$header.textContent = 'References to ';
    this.$headerRefId = document.createElement('a');
    this.$header.appendChild(this.$headerRefId);
    this.$closeButton = document.createElement('span');
    this.$closeButton.setAttribute('id', 'references-pane-close');
    this.$closeButton.addEventListener('click', function (e) {
      this.deactivate();
    }.bind(this));
    this.$header.appendChild(this.$closeButton);

    this.$pane.appendChild(this.$header);
    var tableContainer = document.createElement('div');
    tableContainer.setAttribute('id', 'references-pane-table-container');

    this.$table = document.createElement('table');
    this.$table.setAttribute('id', 'references-pane-table');

    this.$tableBody = this.$table.createTBody();

    tableContainer.appendChild(this.$table);
    this.$pane.appendChild(tableContainer);

    menu.$specContainer.appendChild(this.$container);
  },

  activate: function () {
    this.$container.classList.add('active');
  },

  deactivate: function () {
    this.$container.classList.remove('active');
  },

  showReferencesFor(entry) {
    this.activate();
    var newBody = document.createElement('tbody');
    var previousId;
    var previousCell;
    var dupCount = 0;
    this.$headerRefId.textContent = '#' + entry.id;
    this.$headerRefId.setAttribute('href', '#' + entry.id);
    entry.referencingIds.map(function (id) {
      var target = document.getElementById(id);
      var cid = findParentClauseId(target);
      var clause = menu.search.biblio.byId[cid];
      var dupCount = 0;
      return { id: id, clause: clause }
    }).sort(function (a, b) {
      return sortByClauseNumber(a.clause, b.clause);
    }).forEach(function (record, i) {
      if (previousId === record.clause.id) {
        previousCell.innerHTML += ' (<a href="#' + record.id + '">' + (dupCount + 2) + '</a>)';
        dupCount++;
      } else {
        var row = newBody.insertRow();
        var cell = row.insertCell();
        cell.innerHTML = record.clause.number;
        cell = row.insertCell();
        cell.innerHTML = '<a href="#' + record.id + '">' + record.clause.titleHTML + '</a>';
        previousCell = cell;
        previousId = record.clause.id;
        dupCount = 0;
      }
    }, this);
    this.$table.removeChild(this.$tableBody);
    this.$tableBody = newBody;
    this.$table.appendChild(this.$tableBody);
  }
}
function findParentClauseId(node) {
  while (node && node.nodeName !== 'EMU-CLAUSE' && node.nodeName !== 'EMU-INTRO' && node.nodeName !== 'EMU-ANNEX') {
    node = node.parentNode;
  }
  if (!node) return null;
  return node.getAttribute('id');
}

function sortByClauseNumber(c1, c2) {
  var c1c = c1.number.split('.');
  var c2c = c2.number.split('.');

  for (var i = 0; i < c1c.length; i++) {
    if (i >= c2c.length) {
      return 1;
    }
    
    var c1 = c1c[i];
    var c2 = c2c[i];
    var c1cn = Number(c1);
    var c2cn = Number(c2);

    if (Number.isNaN(c1cn) && Number.isNaN(c2cn)) {
      if (c1 > c2) {
        return 1;
      } else if (c1 < c2) {
        return -1;
      }
    } else if (!Number.isNaN(c1cn) && Number.isNaN(c2cn)) {
      return -1;
    } else if (Number.isNaN(c1cn) && !Number.isNaN(c2cn)) {
      return 1;
    } else if(c1cn > c2cn) {
      return 1;
    } else if (c1cn < c2cn) {
      return -1;
    }
  }

  if (c1c.length === c2c.length) {
    return 0;
  }
  return -1;
}

document.addEventListener('DOMContentLoaded', function () {
  Toolbox.init();
  referencePane.init();
})
var CLAUSE_NODES = ['EMU-CLAUSE', 'EMU-INTRO', 'EMU-ANNEX'];
function findLocalReferences ($elem) {
  var name = $elem.innerHTML;
  var references = [];

  var parentClause = $elem.parentNode;
  while (parentClause && CLAUSE_NODES.indexOf(parentClause.nodeName) === -1) {
    parentClause = parentClause.parentNode;
  }

  if(!parentClause) return;

  var vars = parentClause.querySelectorAll('var');

  for (var i = 0; i < vars.length; i++) {
    var $var = vars[i];

    if ($var.innerHTML === name) {
      references.push($var);
    }
  }

  return references;
}

function toggleFindLocalReferences($elem) {
  var references = findLocalReferences($elem);
  if ($elem.classList.contains('referenced')) {
    references.forEach(function ($reference) {
      $reference.classList.remove('referenced');
    });
  } else {
    references.forEach(function ($reference) {
      $reference.classList.add('referenced');
    });
  }
}

function installFindLocalReferences () {
  document.addEventListener('click', function (e) {
    if (e.target.nodeName === 'VAR') {
      toggleFindLocalReferences(e.target);
    }
  });
}

document.addEventListener('DOMContentLoaded', installFindLocalReferences);
</script><style>body {
  display: flex;
  font-size: 18px;
  line-height: 1.5;
  font-family: Cambria, Palatino Linotype, Palatino, Liberation Serif, serif;
  padding: 0;
  margin: 0;
  color: #111;
}

#spec-container {
  padding: 0 20px;
  flex-grow: 1;
  flex-basis: 66%;
  box-sizing: border-box;
  overflow: hidden;
}

body.oldtoc {
  margin: 0 auto;
}

a {
    text-decoration: none;
    color: #206ca7;
}

a:visited {
  color: #206ca7;
}

a:hover {
    text-decoration: underline;
    color: #239dee;
}


code {
    font-weight: bold;
    font-family: Consolas, Monaco, monospace;
    white-space: pre;
}

pre code {
  font-weight: inherit;
}

pre code.hljs {
  background-color: #fff;
  margin: 0;
  padding: 0;
}

ol.toc {
    list-style: none;
    padding-left: 0;
}

ol.toc ol.toc {
    padding-left: 2ex;
    list-style: none;
}

var {
  color: #2aa198;
  transition: background-color 0.25s ease;
  cursor: pointer;
}

var.referenced {
  background-color: #ffff33;
}

emu-const {
  font-family: sans-serif;
}

emu-val {
  font-weight: bold;
}
emu-alg ol, emu-alg ol ol ol ol {
    list-style-type: decimal;
}

emu-alg ol ol, emu-alg ol ol ol ol ol {
    list-style-type: lower-alpha;
}

emu-alg ol ol ol, ol ol ol ol ol ol {
    list-style-type: lower-roman;
}

emu-eqn {
  display: block;
  margin-left: 4em;
}

emu-eqn.inline {
  display: inline;
  margin: 0;
}

emu-eqn div:first-child {
  margin-left: -2em;
}

emu-note {
    margin: 1em 0;
    color: #666;
    border-left: 5px solid #ccc;
    display: flex;
    flex-direction: row;
}

emu-note > span.note {
    flex-basis: 100px;
    min-width: 100px;
    flex-grow: 0;
    flex-shrink: 1;
    text-transform: uppercase;
    padding-left: 5px;
}

emu-note[type=editor] {
  border-left-color: #faa;
}

emu-note > div.note-contents {
  flex-grow: 1;
  flex-shrink: 1;
}

emu-note > div.note-contents > p:first-of-type {
  margin-top: 0;
}

emu-note > div.note-contents > p:last-of-type {
  margin-bottom: 0;
}

emu-figure {
  display: block;
}

emu-example {
  display: block;
  margin: 1em 3em;
}

emu-example figure figcaption {
  margin-top: 0.5em;
  text-align: left;
}

emu-figure figure,
emu-example figure,
emu-table figure {
  display: flex;
  flex-direction: column;
  align-items: center;
}

emu-production {
    display: block;
    margin-top: 1em;
    margin-bottom: 1em;
    margin-left: 5ex;
}

emu-grammar.inline, emu-production.inline,
emu-grammar.inline emu-production emu-rhs, emu-production.inline emu-rhs,
emu-grammar[collapsed] emu-production emu-rhs, emu-production[collapsed] emu-rhs {
    display: inline;
    padding-left: 1ex;
    margin-left: 0;
}

emu-grammar[collapsed] emu-production, emu-production[collapsed] {
    margin: 0;
}

emu-constraints {
    font-size: .75em;
    margin-right: 1ex;
}

emu-gann {
    margin-right: 1ex;
}

emu-gann emu-t:last-child,
emu-gann emu-nt:last-child {
    margin-right: 0;
}

emu-geq {
    margin-left: 1ex;
    font-weight: bold;
}

emu-oneof {
    font-weight: bold;
    margin-left: 1ex;
}

emu-nt {
    display: inline-block;
    font-style: italic;
    white-space: nowrap;
    text-indent: 0;
}

emu-nt a, emu-nt a:visited {
  color: #333;
}

emu-rhs emu-nt {
    margin-right: 1ex;
}

emu-t {
    display: inline-block;
    font-family: monospace;
    font-weight: bold;
    white-space: nowrap;
    text-indent: 0;
}

emu-production emu-t {
    margin-right: 1ex;
}

emu-rhs {
    display: block;
    padding-left: 75px;
    text-indent: -25px;
}

emu-mods {
    font-size: .85em;
    vertical-align: sub;
    font-style: normal;
    font-weight: normal;
}

emu-production[collapsed] emu-mods {
  display: none;
}

emu-params, emu-opt {
  margin-right: 1ex;
  font-family: monospace;
}

emu-params, emu-constraints {
  color: #2aa198;
}

emu-opt {
  color: #b58900;
}

emu-gprose {
    font-size: 0.9em;
    font-family: Helvetica, Arial, sans-serif;
}

h1.shortname {
  color: #f60;
  font-size: 1.5em;
  margin: 0;
}

h1.version {
  color: #f60;
  font-size: 1.5em;
  margin: 0;
}

h1.title {
  margin-top: 0;
  color: #f60;
}

h1.first {
  margin-top: 0;
}

h1, h2, h3, h4, h5, h6 {
    position: relative;
}

h1 .secnum {
  text-decoration: none;
  margin-right: 10px;
}

h1 span.title {
  order: 2;
}


h1 { font-size: 2.67em; margin-top: 2em; margin-bottom: 0; line-height: 1em;}
h2 { font-size: 2em; }
h3 { font-size: 1.56em; }
h4 { font-size: 1.25em; }
h5 { font-size: 1.11em; }
h6 { font-size: 1em; }

h1:hover span.utils {
  display: block;
}

span.utils {
  font-size: 18px;
  line-height: 18px;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  font-weight: normal;
}

span.utils:before {
    content: "⤷";
    display: inline-block;
    padding: 0 5px;
}

span.utils > * {
  display: inline-block;
  margin-right: 20px;
}

h1 span.utils span.anchor a,
h2 span.utils span.anchor a,
h3 span.utils span.anchor a,
h4 span.utils span.anchor a,
h5 span.utils span.anchor a,
h6 span.utils span.anchor a {
  text-decoration: none;
  font-variant: small-caps;
}

h1 span.utils span.anchor a:hover,
h2 span.utils span.anchor a:hover,
h3 span.utils span.anchor a:hover,
h4 span.utils span.anchor a:hover,
h5 span.utils span.anchor a:hover,
h6 span.utils span.anchor a:hover {
  color: #333;
}

emu-intro h1, emu-clause h1, emu-annex h1 { font-size: 2em; }
emu-intro h2, emu-clause h2, emu-annex h2 { font-size: 1.56em; }
emu-intro h3, emu-clause h3, emu-annex h3 { font-size: 1.25em; }
emu-intro h4, emu-clause h4, emu-annex h4 { font-size: 1.11em; }
emu-intro h5, emu-clause h5, emu-annex h5 { font-size: 1em; }
emu-intro h6, emu-clause h6, emu-annex h6 { font-size: 0.9em; }
emu-intro emu-intro h1, emu-clause emu-clause h1, emu-annex emu-annex h1 { font-size: 1.56em; }
emu-intro emu-intro h2, emu-clause emu-clause h2, emu-annex emu-annex h2 { font-size: 1.25em; }
emu-intro emu-intro h3, emu-clause emu-clause h3, emu-annex emu-annex h3 { font-size: 1.11em; }
emu-intro emu-intro h4, emu-clause emu-clause h4, emu-annex emu-annex h4 { font-size: 1em; }
emu-intro emu-intro h5, emu-clause emu-clause h5, emu-annex emu-annex h5 { font-size: 0.9em; }
emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex h1 { font-size: 1.25em; }
emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex h2 { font-size: 1.11em; }
emu-intro emu-intro emu-intro h3, emu-clause emu-clause emu-clause h3, emu-annex emu-annex emu-annex h3 { font-size: 1em; }
emu-intro emu-intro emu-intro h4, emu-clause emu-clause emu-clause h4, emu-annex emu-annex emu-annex h4 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex h1 { font-size: 1.11em; }
emu-intro emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex emu-annex h2 { font-size: 1em; }
emu-intro emu-intro emu-intro emu-intro h3, emu-clause emu-clause emu-clause emu-clause h3, emu-annex emu-annex emu-annex emu-annex h3 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex emu-annex h1 { font-size: 1em; }
emu-intro emu-intro emu-intro emu-intro emu-intro h2, emu-clause emu-clause emu-clause emu-clause emu-clause h2, emu-annex emu-annex emu-annex emu-annex emu-annex h2 { font-size: 0.9em; }
emu-intro emu-intro emu-intro emu-intro emu-intro emu-intro h1, emu-clause emu-clause emu-clause emu-clause emu-clause emu-clause h1, emu-annex emu-annex emu-annex emu-annex emu-annex emu-annex h1 { font-size: 0.9em }

emu-clause, emu-intro, emu-annex {
    display: block;
}

/* Figures and tables */
figure { display: block; margin: 1em 0 3em 0; }
figure object { display: block; margin: 0 auto; }
figure table.real-table { margin: 0 auto; }
figure figcaption {
    display: block;
    color: #555555;
    font-weight: bold;
    text-align: center;
}

emu-table table {
  margin: 0 auto;
}

emu-table table, table.real-table {
    border-collapse: collapse;
}

emu-table td, emu-table th, table.real-table td, table.real-table th {
    border: 1px solid black;
    padding: 0.4em;
    vertical-align: baseline;
}
emu-table th, emu-table thead td, table.real-table th {
    background-color: #eeeeee;
}

/* Note: the left content edges of table.lightweight-table >tbody >tr >td
   and div.display line up. */
table.lightweight-table {
    border-collapse: collapse;
    margin: 0 0 0 1.5em;
}
table.lightweight-table td, table.lightweight-table th {
    border: none;
    padding: 0 0.5em;
    vertical-align: baseline;
}

/* diff styles */
ins {
    background-color: #e0f8e0;
    text-decoration: none;
    border-bottom: 1px solid #396;
}

del {
    background-color: #fee;
}

ins.block, del.block,
emu-production > ins, emu-production > del,
emu-grammar > ins, emu-grammar > del {
  display: block;
}
emu-rhs > ins, emu-rhs > del { 
  display: inline;
}

tr.ins > td > ins {
  border-bottom: none;
}

tr.ins > td {
  background-color: #e0f8e0;
}

tr.del > td {
  background-color: #fee;
}

/* Menu Styles */
#menu-toggle {
  font-size: 2em;

  position: fixed;
  top: 0;
  left: 0;
  width: 1.5em;
  height: 1.5em;
  z-index: 3;
  visibility: hidden;
  color: #1567a2;
  background-color: #fff;

  line-height: 1.5em;
  text-align: center;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;;

  cursor: pointer;
}

#menu {
  display: flex;
  flex-direction: column;
  width: 33%; height: 100vh;
  max-width: 500px;
  box-sizing: border-box;
  background-color: #ddd;
  overflow: hidden;
  transition: opacity 0.1s linear;
  padding: 0 5px;
  position: fixed;
  left: 0; top: 0;
  border-right: 2px solid #bbb;

  z-index: 2;
}

#menu-spacer {
  flex-basis: 33%;
  max-width: 500px;
  flex-grow: 0;
  flex-shrink: 0;
}

#menu a {
  color: #1567a2;
}

#menu.active {
  display: flex;
  opacity: 1;
  z-index: 2;
}

#menu-pins {
  flex-grow: 1;
  display: none;
}

#menu-pins.active {
  display: block;
}

#menu-pins-list {
  margin: 0;
  padding: 0;
  counter-reset: pins-counter;
}

#menu-pins-list > li:before {
  content: counter(pins-counter);
  counter-increment: pins-counter;
  display: inline-block;
  width: 25px;
  text-align: center;
  border: 1px solid #bbb;
  padding: 2px;
  margin: 4px;
  box-sizing: border-box;
  line-height: 1em;
  background-color: #ccc;
  border-radius: 4px;
}
#menu-toc > ol {
  padding: 0;
  flex-grow: 1;
}

#menu-toc > ol li {
  padding: 0;
}

#menu-toc > ol , #menu-toc > ol ol {
  list-style-type: none;
  margin: 0;
  padding: 0;
}

#menu-toc > ol ol {
  padding-left: 0.75em;
}

#menu-toc li {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

#menu-toc .item-toggle {
  display: inline-block;
  transform: rotate(-45deg) translate(-5px, -5px);
  transition: transform 0.1s ease;
  text-align: center;
  width: 20px;

  color: #aab;

  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;;

  cursor: pointer;
}

#menu-toc .item-toggle-none {
  display: inline-block;
  width: 20px;
}

#menu-toc li.active > .item-toggle {
  transform: rotate(45deg) translate(-5px, -5px);
}

#menu-toc li > ol {
  display: none;
}

#menu-toc li.active > ol {
  display: block;
}

#menu-toc li.revealed > a {
  background-color: #bbb;
  font-weight: bold;
  /*
  background-color: #222;
  color: #c6d8e4;
  */
}

#menu-toc li.revealed-leaf> a {
  color: #206ca7;
  /*
  background-color: #222;
  color: #c6d8e4;
  */
}

#menu-toc li.revealed > .item-toggle {
  transform: rotate(45deg) translate(-5px, -5px);
}

#menu-toc li.revealed > ol {
  display: block;
}

#menu-toc li > a {
  padding: 2px 5px;
}

#menu > * {
  margin-bottom: 5px;
}

.menu-pane-header {
  padding: 0 5px;
  text-transform: uppercase;
  background-color: #aaa;
  color: #335;
  font-weight: bold;
  letter-spacing: 2px;
  flex-grow: 0;
  flex-shrink: 0;
  font-size: 0.8em;
}

#menu-toc {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  flex-grow: 1;
}

#menu-toc ol.toc {
  overflow-x: hidden;
  overflow-y: auto;
}

#menu-search {
  position: relative;
  flex-grow: 0;
  flex-shrink: 0;
  width: 100%;
  
  display: flex;
  flex-direction: column;
  
  max-height: 300px;
}

#menu-trace-list {
  display: none;
}

#menu-search-box {
  box-sizing: border-box;
  display: block;
  width: 100%;
  margin: 5px 0 0 0;
  font-size: 1em;
  padding: 2px;
  background-color: #bbb;
  border: 1px solid #999;
}

#menu-search-results {
  overflow-x: hidden;
  overflow-y: auto;
}

li.menu-search-result-clause:before {
    content: 'clause';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%;
}
li.menu-search-result-op:before {
    content: 'op';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%;
}

li.menu-search-result-prod:before {
    content: 'prod';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%
}


li.menu-search-result-term:before {
    content: 'term';
    width: 40px;
    display: inline-block;
    text-align: right;
    padding-right: 1ex;
    color: #666;
    font-size: 75%
}

#menu-search-results ul {
  padding: 0 5px;
  margin: 0;
}

#menu-search-results li {
  white-space: nowrap;
  text-overflow: ellipsis;
}


#menu-trace-list {
  counter-reset: item;
  margin: 0 0 0 20px;
  padding: 0;
}
#menu-trace-list li {
  display: block;
  white-space: nowrap;
}

#menu-trace-list li .secnum:after {
  content: " ";
}
#menu-trace-list li:before {
    content: counter(item) " ";
    background-color: #222;
    counter-increment: item;
    color: #999;
    width: 20px;
    height: 20px;
    line-height: 20px;
    display: inline-block;
    text-align: center;
    margin: 2px 4px 2px 0;
}

@media (max-width: 1000px) {
  body {
    margin: 0;
    display: block;
  }

  #menu {
    display: none;
    padding-top: 3em;
    width: 450px;
  }

  #menu.active {
    position: fixed;
    height: 100%;
    left: 0;
    top: 0;
    right: 300px;
  }

  #menu-toggle {
    visibility: visible;
  }

  #spec-container {
    padding: 0 5px;
  }

  #references-pane-spacer {
    display: none;
  }
}

@media only screen and (max-width: 800px) {
  #menu {
    width: 100%;
  }

  h1 .secnum:empty {
    margin: 0; padding: 0;
  }
}


/* Toolbox */
.toolbox {
	position: absolute;
	background: #ddd;
	border: 1px solid #aaa;
  display: none;
  color: #eee;
  padding: 5px;
  border-radius: 3px;
}

.toolbox.active {
  display: inline-block;
}

.toolbox a {
  text-decoration: none;
  padding: 0 5px;
}

.toolbox a:hover {
  text-decoration: underline;
}

.toolbox:after, .toolbox:before {
	top: 100%;
	left: 15px;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}

.toolbox:after {
	border-color: rgba(0, 0, 0, 0);
	border-top-color: #ddd;
	border-width: 10px;
	margin-left: -10px;
}
.toolbox:before {
	border-color: rgba(204, 204, 204, 0);
	border-top-color: #aaa;
	border-width: 12px;
	margin-left: -12px;
}

#references-pane-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  display: none;
  background-color: #ddd;
  z-index: 1;
}

#references-pane-table-container {
  overflow-x: hidden;
  overflow-y: auto;
}

#references-pane-spacer {
  flex-basis: 33%;
  max-width: 500px;
}

#references-pane {
  flex-grow: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

#references-pane-container.active {
  display: flex;
}

#references-pane-close:after {
  content: '✖';
  float: right;
  cursor: pointer;
}

#references-pane table tr td:first-child {
  text-align: right;
  padding-right: 5px;
}

@media print {
  #menu-toggle {
    display: none; 
  }
}
</style></head><body><div id="menu-toggle">☰</div><div id="menu-spacer"></div><div id="menu"><div id="menu-search"><input type="text" id="menu-search-box" placeholder="Search..."><div id="menu-search-results" class="inactive"></div></div><div id="menu-pins"><div class="menu-pane-header">Pins</div><ul id="menu-pins-list"></ul></div><div class="menu-pane-header">Table of Contents</div><div id="menu-toc"><ol class="toc"><li><span class="item-toggle-none"></span><a href="#introduction" title="Metadata Proposal - ECMAScript">Metadata Proposal - ECMAScript</a></li><li><span class="item-toggle-none"></span><a href="#syntax" title="Syntax"><span class="secnum">1</span> Syntax</a></li><li><span class="item-toggle">◢</span><a href="#abstract-operations" title="Abstract Operations"><span class="secnum">2</span> Abstract Operations</a><ol class="toc"><li><span class="item-toggle">◢</span><a href="#operations-on-objects" title="Operations on Objects"><span class="secnum">2.1</span> Operations on Objects</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#getorcreatemetadatamap" title="GetOrCreateMetadataMap ( O, P, Create )"><span class="secnum">2.1.1</span> GetOrCreateMetadataMap ( O, P, Create )</a></li></ol></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-and-exotic-objects-behaviors" title="Ordinary and Exotic Objects Behaviors"><span class="secnum">3</span> Ordinary and Exotic Objects Behaviors</a><ol class="toc"><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots" title="Ordinary Object Internal Methods and Internal Slots"><span class="secnum">3.1</span> Ordinary Object Internal Methods and Internal Slots</a><ol class="toc"><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-hasmetadata" title="[[HasMetadata]] ( MetadataKey, P )"><span class="secnum">3.1.1</span> [[HasMetadata]] ( MetadataKey, P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinaryhasmetadata" title="OrdinaryHasMetadata ( MetadataKey, O, P )"><span class="secnum">*******</span> OrdinaryHasMetadata ( MetadataKey, O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-hasownmetadata" title="[[HasOwnMetadata]] ( MetadataKey, P )"><span class="secnum">3.1.2</span> [[HasOwnMetadata]] ( MetadataKey, P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinaryhasownmetadata" title="OrdinaryHasOwnMetadata ( MetadataKey, O, P )"><span class="secnum">*******</span> OrdinaryHasOwnMetadata ( MetadataKey, O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-getmetadata" title="[[GetMetadata]] ( MetadataKey, P )"><span class="secnum">3.1.3</span> [[GetMetadata]] ( MetadataKey, P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinarygetmetadata" title="OrdinaryGetMetadata ( MetadataKey, O, P )"><span class="secnum">*******</span> OrdinaryGetMetadata ( MetadataKey, O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-getownmetadata" title="[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )"><span class="secnum">3.1.4</span> [[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinarygetownmetadata" title="OrdinaryGetOwnMetadata ( MetadataKey, O, P )"><span class="secnum">*******</span> OrdinaryGetOwnMetadata ( MetadataKey, O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-defineownmetadata" title="[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )"><span class="secnum">3.1.5</span> [[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinarydefineownmetadata" title="OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )"><span class="secnum">*******</span> OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-metadatakeys" title="[[MetadataKeys]] ( P )"><span class="secnum">3.1.6</span> [[MetadataKeys]] ( P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinarymetadatakeys" title="OrdinaryMetadataKeys ( O, P )"><span class="secnum">3.1.6.1</span> OrdinaryMetadataKeys ( O, P )</a></li></ol></li><li><span class="item-toggle">◢</span><a href="#ordinary-object-internal-methods-and-internal-slots-ownmetadatakeys" title="[[OwnMetadataKeys]] ( P )"><span class="secnum">3.1.7</span> [[OwnMetadataKeys]] ( P )</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#ordinaryownmetadatakeys" title="OrdinaryOwnMetadataKeys ( O, P )"><span class="secnum">3.1.7.1</span> OrdinaryOwnMetadataKeys ( O, P )</a></li></ol></li><li><span class="item-toggle-none"></span><a href="#ordinary-object-internal-methods-and-internal-slots-deletemetadata" title="[[DeleteMetadata]]( MetadataKey, P )"><span class="secnum">3.1.8</span> [[DeleteMetadata]]( MetadataKey, P )</a></li></ol></li></ol></li><li><span class="item-toggle">◢</span><a href="#reflection" title="Reflection"><span class="secnum">4</span> Reflection</a><ol class="toc"><li><span class="item-toggle">◢</span><a href="#reflect" title="The Reflect Object"><span class="secnum">4.1</span> The Reflect Object</a><ol class="toc"><li><span class="item-toggle-none"></span><a href="#reflect-metadatadecoratorfunctions" title="Metadata Decorator Functions"><span class="secnum">4.1.1</span> Metadata Decorator Functions</a></li><li><span class="item-toggle-none"></span><a href="#reflect.metadata" title="Reflect.metadata ( metadataKey, metadataValue )"><span class="secnum">4.1.2</span> Reflect.metadata ( metadataKey, metadataValue )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.definemetadata" title="Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )"><span class="secnum">4.1.3</span> Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.hasmetadata" title="Reflect.hasMetadata ( metadataKey, target [, propertyKey] )"><span class="secnum">4.1.4</span> Reflect.hasMetadata ( metadataKey, target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.hasownmetadata" title="Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )"><span class="secnum">4.1.5</span> Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.getmetadata" title="Reflect.getMetadata ( metadataKey, target [, propertyKey] )"><span class="secnum">4.1.6</span> Reflect.getMetadata ( metadataKey, target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.getownmetadata" title="Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )"><span class="secnum">4.1.7</span> Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.getmetadatakeys" title="Reflect.getMetadataKeys ( target [, propertyKey] )"><span class="secnum">4.1.8</span> Reflect.getMetadataKeys ( target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.getownmetadatakeys" title="Reflect.getOwnMetadataKeys ( target [, propertyKey] )"><span class="secnum">4.1.9</span> Reflect.getOwnMetadataKeys ( target [, propertyKey] )</a></li><li><span class="item-toggle-none"></span><a href="#reflect.deletemetadata" title="Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )"><span class="secnum">4.1.10</span> Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )</a></li></ol></li></ol></li></ol></div></div><div id="spec-container">

<emu-intro id="introduction">
  <h1 class="first">Metadata Proposal - ECMAScript</h1>
  <emu-note><span class="note">Note</span><div class="note-contents">A shim for this API can be found here:  <a href="https://github.com/rbuckton/ReflectDecorators">https://github.com/rbuckton/ReflectDecorators</a></div></emu-note>
  <p>Proposal to add Metadata to ECMAScript.</p>
</emu-intro>

<emu-clause id="syntax">
  <h1><span class="secnum">1</span>Syntax</h1>
  <emu-note><span class="note">Note</span><div class="note-contents">This section is non-normative.</div></emu-note>
  <pre><code class="javascript hljs"><span class="hljs-comment">// define metadata on an object or property</span>
<span class="hljs-built_in">Reflect</span>.defineMetadata(metadataKey, metadataValue, target);
<span class="hljs-built_in">Reflect</span>.defineMetadata(metadataKey, metadataValue, target, propertyKey);

<span class="hljs-comment">// check for presence of a metadata key on the prototype chain of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.hasMetadata(metadataKey, target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.hasMetadata(metadataKey, target, propertyKey);

<span class="hljs-comment">// check for presence of an own metadata key of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.hasOwnMetadata(metadataKey, target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.hasOwnMetadata(metadataKey, target, propertyKey);

<span class="hljs-comment">// get metadata value of a metadata key on the prototype chain of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getMetadata(metadataKey, target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getMetadata(metadataKey, target, propertyKey);

<span class="hljs-comment">// get metadata value of an own metadata key of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getOwnMetadata(metadataKey, target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getOwnMetadata(metadataKey, target, propertyKey);

<span class="hljs-comment">// get all metadata keys on the prototype chain of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getMetadataKeys(target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getMetadataKeys(target, propertyKey);

<span class="hljs-comment">// get all own metadata keys of an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getOwnMetadataKeys(target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.getOwnMetadataKeys(target, propertyKey);

<span class="hljs-comment">// delete metadata from an object or property</span>
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.deleteMetadata(metadataKey, target);
<span class="hljs-keyword">let</span> result = <span class="hljs-built_in">Reflect</span>.deleteMetadata(metadataKey, target, propertyKey);

<span class="hljs-comment">// apply metadata via a decorator to a constructor</span>
@<span class="hljs-built_in">Reflect</span>.metadata(metadataKey, metadataValue)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">C</span> </span>{
  <span class="hljs-comment">// apply metadata via a decorator to a method (property)</span>
  @<span class="hljs-built_in">Reflect</span>.metadata(metadataKey, metadataValue)
  method() {
  }
}

<span class="hljs-comment">// Design-time type annotations</span>
<span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">Type</span>(<span class="hljs-params">type</span>) </span>{ <span class="hljs-keyword">return</span> <span class="hljs-built_in">Reflect</span>.metadata(<span class="hljs-string">"design:type"</span>, type); }
<span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">ParamTypes</span>(<span class="hljs-params">...types</span>) </span>{ <span class="hljs-keyword">return</span> <span class="hljs-built_in">Reflect</span>.metadata(<span class="hljs-string">"design:paramtypes"</span>, types); }
<span class="hljs-function"><span class="hljs-keyword">function</span> <span class="hljs-title">ReturnType</span>(<span class="hljs-params">type</span>) </span>{ <span class="hljs-keyword">return</span> <span class="hljs-built_in">Reflect</span>.metadata(<span class="hljs-string">"design:returntype"</span>, type); }

<span class="hljs-comment">// Decorator application</span>
@ParamTypes(<span class="hljs-built_in">String</span>, <span class="hljs-built_in">Number</span>)
<span class="hljs-class"><span class="hljs-keyword">class</span> <span class="hljs-title">C</span> </span>{
  <span class="hljs-keyword">constructor</span>(text, i) {
  }

  @Type(<span class="hljs-built_in">String</span>)
  get name() { <span class="hljs-keyword">return</span> <span class="hljs-string">"text"</span>; }

  @Type(<span class="hljs-built_in">Function</span>)
  @ParamTypes(<span class="hljs-built_in">Number</span>, <span class="hljs-built_in">Number</span>)
  @ReturnType(<span class="hljs-built_in">Number</span>)
  add(x, y) {
    <span class="hljs-keyword">return</span> x + y;
  }
}

<span class="hljs-comment">// Metadata introspection</span>
<span class="hljs-keyword">let</span> obj = <span class="hljs-keyword">new</span> C(<span class="hljs-string">"a"</span>, <span class="hljs-number">1</span>);
<span class="hljs-keyword">let</span> paramTypes = <span class="hljs-built_in">Reflect</span>.getMetadata(<span class="hljs-string">"design:paramtypes"</span>, inst, <span class="hljs-string">"add"</span>); <span class="hljs-comment">// [Number, Number]</span></code></pre>
</emu-clause>

<emu-clause id="abstract-operations">
  <h1><span class="secnum">2</span>Abstract Operations</h1>
  <emu-clause id="operations-on-objects">
    <h1><span class="secnum">2.1</span>Operations on Objects</h1>
    <emu-clause id="getorcreatemetadatamap" aoid="GetOrCreateMetadataMap">
      <h1><span class="secnum">2.1.1</span>GetOrCreateMetadataMap ( O, P, Create )</h1>
      <p>When the abstract operation GetOrCreateMetadataMap is called with Object <var>O</var>, property key <var>P</var>, and Boolean <var>Create</var> the following steps are taken:</p>
      <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_0"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>targetMetadata</var> be the value of <var>O</var>'s [[Metadata]] internal slot.</li><li>If <var>targetMetadata</var> is <emu-val>undefined</emu-val>, then<ol><li>If <var>Create</var> is <emu-val>false</emu-val>, return <emu-val>undefined</emu-val>.</li><li>Set <var>targetMetadata</var> to be a newly created <emu-val>Map</emu-val> object.</li><li>Set the [[Metadata]] internal slot of <var>O</var> to <var>targetMetadata</var>.</li></ol></li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="Invoke" id="_ref_1"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>targetMetadata</var>, <code>"get"</code>, <var>P</var>).</li><li>If <var>metadataMap</var> is <emu-val>undefined</emu-val>, then<ol><li>If <var>Create</var> is <emu-val>false</emu-val>, return <emu-val>undefined</emu-val>.</li><li>Set <var>metadataMap</var> to be a newly created <emu-val>Map</emu-val> object.</li><li>Perform ?&nbsp;<emu-xref aoid="Invoke" id="_ref_2"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>targetMetadata</var>, <code>"set"</code>, <var>P</var>, <var>metadataMap</var>).</li></ol></li><li>Return <var>metadataMap</var>.
      </li></ol></emu-alg>
    </emu-clause>
  </emu-clause>
</emu-clause>

<emu-clause id="ordinary-and-exotic-objects-behaviors">
  <h1><span class="secnum">3</span>Ordinary and Exotic Objects Behaviors</h1>

  <emu-clause id="ordinary-object-internal-methods-and-internal-slots">
    <h1><span class="secnum">3.1</span>Ordinary Object Internal Methods and Internal Slots</h1>
    <p>All ordinary objects have an internal slot called [[Metadata]]. The value of this internal slot is either <emu-val>null</emu-val> or a <emu-val>Map</emu-val> object and is used for storing metadata for an object.</p>
    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-hasmetadata">
      <h1><span class="secnum">3.1.1</span>[[HasMetadata]] ( MetadataKey, P )</h1>
      <p>When the [[HasMetadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var> and property key <var>P</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryHasMetadata" id="_ref_3"><a href="#ordinaryhasmetadata">OrdinaryHasMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinaryhasmetadata" aoid="OrdinaryHasMetadata">
        <h1><span class="secnum">*******</span>OrdinaryHasMetadata ( MetadataKey, O, P )</h1>
        <p>When the abstract operation OrdinaryHasMetadata is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, Object <var>O</var>, and property key <var>P</var>, the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_4"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>hasOwn</var> be ?&nbsp;<emu-xref aoid="OrdinaryHasOwnMetadata" id="_ref_5"><a href="#ordinaryhasownmetadata">OrdinaryHasOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).</li><li>If <var>hasOwn</var> is <emu-val>true</emu-val>, return <emu-val>true</emu-val>.</li><li>Let <var>parent</var> be ? <var>O</var>.[[GetPrototypeOf]]().</li><li>If <var>parent</var> is not <emu-val>null</emu-val>, Return ? <var>parent</var>.[[HasMetadata]](<var>MetadataKey</var>, <var>P</var>).</li><li>Return <emu-val>false</emu-val>.
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-hasownmetadata">
      <h1><span class="secnum">3.1.2</span>[[HasOwnMetadata]] ( MetadataKey, P )</h1>
      <p>When the [[HasOwnMetadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var> and property key <var>P</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryHasOwnMetadata" id="_ref_6"><a href="#ordinaryhasownmetadata">OrdinaryHasOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinaryhasownmetadata" aoid="OrdinaryHasOwnMetadata">
        <h1><span class="secnum">*******</span>OrdinaryHasOwnMetadata ( MetadataKey, O, P )</h1>
        <p>When the abstract operation OrdinaryHasOwnMetadata is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, Object <var>O</var>, and property key <var>P</var>, the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_7"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="GetOrCreateMetadataMap" id="_ref_8"><a href="#getorcreatemetadatamap">GetOrCreateMetadataMap</a></emu-xref>(<var>O</var>, <var>P</var>, <emu-val>false</emu-val>).</li><li>If <var>metadataMap</var> is <emu-val>undefined</emu-val>, return <emu-val>false</emu-val>.</li><li>Return ?&nbsp;<emu-xref aoid="ToBoolean" id="_ref_9"><a href="https://tc39.github.io/ecma262/#sec-toboolean">ToBoolean</a></emu-xref>(? <emu-xref aoid="Invoke" id="_ref_10"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>metadataMap</var>, <code>"has"</code>, <var>MetadataKey</var>)).
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-getmetadata">
      <h1><span class="secnum">3.1.3</span>[[GetMetadata]] ( MetadataKey, P )</h1>
      <p>When the [[GetMatadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var> and property key <var>P</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryGetMetadata" id="_ref_11"><a href="#ordinarygetmetadata">OrdinaryGetMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinarygetmetadata" aoid="OrdinaryGetMetadata">
        <h1><span class="secnum">*******</span>OrdinaryGetMetadata ( MetadataKey, O, P )</h1>
        <p>When the abstract operation OrdinaryGetMetadata is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, Object <var>O</var>, and property key <var>P</var>, the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_12"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>hasOwn</var> be ?&nbsp;<emu-xref aoid="OrdinaryHasOwnMetadata" id="_ref_13"><a href="#ordinaryhasownmetadata">OrdinaryHasOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).</li><li>If <var>hasOwn</var> is <emu-val>true</emu-val>, return ?&nbsp;<emu-xref aoid="OrdinaryGetOwnMetadata" id="_ref_14"><a href="#ordinarygetownmetadata">OrdinaryGetOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).</li><li>Let <var>parent</var> be ? <var>O</var>.[[GetPrototypeOf]]().</li><li>If <var>parent</var> is not <emu-val>null</emu-val>, return ? <var>parent</var>.[[GetMetadata]](<var>MetadataKey</var>, <var>P</var>).</li><li>Return <emu-val>undefined</emu-val>.
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-getownmetadata">
      <h1><span class="secnum">3.1.4</span>[[GetOwnMetadata]] ( MetadataKey, P, ParamIndex )</h1>
      <p>When the [[GetOwnMetadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var> and property key <var>P</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryGetOwnMetadata" id="_ref_15"><a href="#ordinarygetownmetadata">OrdinaryGetOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinarygetownmetadata" aoid="OrdinaryGetOwnMetadata">
        <h1><span class="secnum">*******</span>OrdinaryGetOwnMetadata ( MetadataKey, O, P )</h1>
        <p>When the abstract operation OrdinaryGetOwnMetadata is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, Object <var>O</var>, and property key <var>P</var>, the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_16"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="GetOrCreateMetadataMap" id="_ref_17"><a href="#getorcreatemetadatamap">GetOrCreateMetadataMap</a></emu-xref>(<var>O</var>, <var>P</var>, <emu-val>false</emu-val>).</li><li>If <var>metadataMap</var> is <emu-val>undefined</emu-val>, return <emu-val>undefined</emu-val>.</li><li>Return ?&nbsp;<emu-xref aoid="Invoke" id="_ref_18"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>metadataMap</var>, <code>"get"</code>, <var>MetadataKey</var>).
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-defineownmetadata">
      <h1><span class="secnum">3.1.5</span>[[DefineOwnMetadata]] ( MetadataKey, MetadataValue, P )</h1>
      <p>When the [[DefineOwnMetadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataValue</var>, and property key <var>P</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryDefineOwnMetadata" id="_ref_19"><a href="#ordinarydefineownmetadata">OrdinaryDefineOwnMetadata</a></emu-xref>(<var>MetadataKey</var>, <var>MetadataValue</var>, <var>O</var>, <var>P</var>)
      </li></ol></emu-alg>
      <emu-clause id="ordinarydefineownmetadata" aoid="OrdinaryDefineOwnMetadata">
        <h1><span class="secnum">*******</span>OrdinaryDefineOwnMetadata ( MetadataKey, MetadataValue, O, P )</h1>
        <p>When the abstract operation <emu-xref aoid="OrdinaryDefineOwnProperty" id="_ref_20"><a href="https://tc39.github.io/ecma262/#sec-ordinarydefineownproperty">OrdinaryDefineOwnProperty</a></emu-xref> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var>, <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataValue</var>, Object <var>O</var>, and property key <var>P</var>, the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_21"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="GetOrCreateMetadataMap" id="_ref_22"><a href="#getorcreatemetadatamap">GetOrCreateMetadataMap</a></emu-xref>(<var>O</var>, <var>P</var>, <emu-val>true</emu-val>).</li><li>Return ?&nbsp;<emu-xref aoid="Invoke" id="_ref_23"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>metadataMap</var>, <code>"set"</code>, <var>MetadataKey</var>, <var>MetadataValue</var>).
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-metadatakeys">
      <h1><span class="secnum">3.1.6</span>[[MetadataKeys]] ( P )</h1>
      <p>When the [[MetadataKeys]] internal method of <var>O</var> is called with property key <var>P</var> the following steps are taken:</p>
      <emu-alg><ol><li>Return ?&nbsp;<emu-xref aoid="OrdinaryMetadataKeys" id="_ref_24"><a href="#ordinarymetadatakeys">OrdinaryMetadataKeys</a></emu-xref>(<var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinarymetadatakeys" aoid="OrdinaryMetadataKeys">
        <h1><span class="secnum">3.1.6.1</span>OrdinaryMetadataKeys ( O, P )</h1>
        <p>When the abstract operation OrdinaryMetadataKeys is called with Object <var>O</var> and property key <var>P</var> the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_25"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>ownKeys</var> be ?&nbsp;<emu-xref aoid="OrdinaryOwnMetadataKeys" id="_ref_26"><a href="#ordinaryownmetadatakeys">OrdinaryOwnMetadataKeys</a></emu-xref>(<var>O</var>, <var>P</var>).</li><li>Let <var>parent</var> be ? <var>O</var>.[[GetPrototypeOf]]().</li><li>If <var>parent</var> is <emu-val>null</emu-val>, then return <var>ownKeys</var>.</li><li>Let <var>parentKeys</var> be ? <var>O</var>.[[OrdinaryMetadataKeys]](<var>P</var>).</li><li>Let <var>ownKeysLen</var> = ?&nbsp;<emu-xref aoid="Get" id="_ref_27"><a href="https://tc39.github.io/ecma262/#sec-get-o-p">Get</a></emu-xref>(<var>ownKeys</var>, "length").</li><li>If <var>ownKeysLen</var> is <emu-val>0</emu-val>, return <var>parentKeys</var>.</li><li>Let <var>parentKeysLen</var> = ?&nbsp;<emu-xref aoid="Get" id="_ref_28"><a href="https://tc39.github.io/ecma262/#sec-get-o-p">Get</a></emu-xref>(<var>parentKeys</var>, "length").</li><li>If <var>parentKeysLen</var> is <emu-val>0</emu-val>, return <var>ownKeys</var>.</li><li>Let <var>set</var> be a newly created <emu-val>Set</emu-val> object.</li><li>Let <var>keys</var> be ?&nbsp;<emu-xref aoid="ArrayCreate" id="_ref_29"><a href="https://tc39.github.io/ecma262/#sec-arraycreate">ArrayCreate</a></emu-xref>(0).</li><li>Let <var>k</var> be <emu-val>0</emu-val>.</li><li>For each element <var>key</var> of <var>ownKeys</var><ol><li>Let <var>hasKey</var> be ?&nbsp;<emu-xref aoid="Invoke" id="_ref_30"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>set</var>, <code>"has"</code>, <var>key</var>).</li><li>If <var>hasKey</var> is <emu-val>false</emu-val>, then<ol><li>Let <var>Pk</var> be !&nbsp;<emu-xref aoid="ToString" id="_ref_31"><a href="https://tc39.github.io/ecma262/#sec-tostring">ToString</a></emu-xref>(<var>k</var>).</li><li>Perform ?&nbsp;<emu-xref aoid="Invoke" id="_ref_32"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>set</var>, "add", <var>key</var>).</li><li>Let <var>defineStatus</var> be <emu-xref aoid="CreateDataProperty" id="_ref_33"><a href="https://tc39.github.io/ecma262/#sec-createdataproperty">CreateDataProperty</a></emu-xref>(<var>keys</var>, <var>Pk</var>, <var>key</var>).</li><li>Assert: <var>defineStatus</var> is <emu-val>true</emu-val>.</li><li>Increase <var>k</var> by <emu-val>1</emu-val>.</li></ol></li></ol></li><li>For each element <var>key</var> of <var>parentKeys</var><ol><li>Let <var>hasKey</var> be ?&nbsp;<emu-xref aoid="Invoke" id="_ref_34"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>set</var>, <code>"has"</code>, <var>key</var>).</li><li>If <var>hasKey</var> is <emu-val>false</emu-val>, then<ol><li>Let <var>Pk</var> be !&nbsp;<emu-xref aoid="ToString" id="_ref_35"><a href="https://tc39.github.io/ecma262/#sec-tostring">ToString</a></emu-xref>(<var>k</var>).</li><li>Perform ?&nbsp;<emu-xref aoid="Invoke" id="_ref_36"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>set</var>, <code>"add"</code>, <var>key</var>).</li><li>Let <var>defineStatus</var> be <emu-xref aoid="CreateDataProperty" id="_ref_37"><a href="https://tc39.github.io/ecma262/#sec-createdataproperty">CreateDataProperty</a></emu-xref>(<var>keys</var>, <var>Pk</var>, <var>key</var>).</li><li>Assert: <var>defineStatus</var> is <emu-val>true</emu-val>.</li><li>Increase <var>k</var> by <emu-val>1</emu-val>.</li></ol></li></ol></li><li>Perform ?&nbsp;<emu-xref aoid="Set" id="_ref_38"><a href="https://tc39.github.io/ecma262/#sec-set-o-p-v-throw">Set</a></emu-xref>(<var>keys</var>, <code>"length"</code>, <var>k</var>).</li><li>return <var>keys</var>.
        </li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-ownmetadatakeys">
      <h1><span class="secnum">3.1.7</span>[[OwnMetadataKeys]] ( P )</h1>
      <p>When the [[OwnMetadataKeys]] internal method of <var>O</var> is called with property key <var>P</var> the following steps are taken:</p>
      <emu-alg><ol><li>Return <emu-xref aoid="OrdinaryOwnMetadataKeys" id="_ref_39"><a href="#ordinaryownmetadatakeys">OrdinaryOwnMetadataKeys</a></emu-xref>(<var>O</var>, <var>P</var>).
      </li></ol></emu-alg>
      <emu-clause id="ordinaryownmetadatakeys" aoid="OrdinaryOwnMetadataKeys">
        <h1><span class="secnum">3.1.7.1</span>OrdinaryOwnMetadataKeys ( O, P )</h1>
        <p>When the abstract operation OrdinaryOwnMetadataKeys is called with Object <var>O</var> and property key <var>P</var> the following steps are taken:</p>
        <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_40"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>keys</var> be ?&nbsp;<emu-xref aoid="ArrayCreate" id="_ref_41"><a href="https://tc39.github.io/ecma262/#sec-arraycreate">ArrayCreate</a></emu-xref>(0).</li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="GetOrCreateMetadataMap" id="_ref_42"><a href="#getorcreatemetadatamap">GetOrCreateMetadataMap</a></emu-xref>(<var>O</var>, <var>P</var>, <emu-val>false</emu-val>).</li><li>If <var>metadataMap</var> is <emu-val>undefined</emu-val>, return <var>keys</var>.</li><li>Let <var>keysObj</var> be ?&nbsp;<emu-xref aoid="Invoke" id="_ref_43"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>metadataMap</var>, <code>"keys"</code>).</li><li>Let <var>iterator</var> be ?&nbsp;<emu-xref aoid="GetIterator" id="_ref_44"><a href="https://tc39.github.io/ecma262/#sec-getiterator">GetIterator</a></emu-xref>(<var>keysObj</var>).</li><li>Let <var>k</var> be <emu-val>0</emu-val>.</li><li>Repeat<ol><li>Let <var>Pk</var> be !&nbsp;<emu-xref aoid="ToString" id="_ref_45"><a href="https://tc39.github.io/ecma262/#sec-tostring">ToString</a></emu-xref>(<var>k</var>).</li><li>Let <var>next</var> be ?&nbsp;<emu-xref aoid="IteratorStep" id="_ref_46"><a href="https://tc39.github.io/ecma262/#sec-iteratorstep">IteratorStep</a></emu-xref>(<var>iterator</var>).</li><li>If <var>next</var> is <emu-val>false</emu-val>, then<ol><li>Let <var>setStatus</var> be ?&nbsp;<emu-xref aoid="Set" id="_ref_47"><a href="https://tc39.github.io/ecma262/#sec-set-o-p-v-throw">Set</a></emu-xref>(<var>keys</var>, <code>"length"</code>, <var>k</var>, <var>true</var>).</li><li>Assert: <var>setStatus</var> is <emu-val>true</emu-val>.</li><li>Return <var>keys</var>.</li></ol></li><li>Let <var>nextValue</var> be ?&nbsp;<emu-xref aoid="IteratorValue" id="_ref_48"><a href="https://tc39.github.io/ecma262/#sec-iteratorvalue">IteratorValue</a></emu-xref>(<var>next</var>).</li><li>Let <var>defineStatus</var> be <emu-xref aoid="CreateDataPropertyOrThrow" id="_ref_49"><a href="https://tc39.github.io/ecma262/#sec-createdatapropertyorthrow">CreateDataPropertyOrThrow</a></emu-xref>(<var>keys</var>, <var>Pk</var>, <var>nextValue</var>).</li><li>If <var>defineStatus</var> is an <emu-xref href="#sec-completion-record-specification-type"><a href="https://tc39.github.io/ecma262/#sec-completion-record-specification-type">abrupt completion</a></emu-xref>, return ?&nbsp;<emu-xref aoid="IteratorClose" id="_ref_50"><a href="https://tc39.github.io/ecma262/#sec-iteratorclose">IteratorClose</a></emu-xref>(<var>iterator</var>, <var>defineStatus</var>).</li><li>Increase <var>k</var> by <emu-val>1</emu-val>.
        </li></ol></li></ol></emu-alg>
      </emu-clause>
    </emu-clause>

    <emu-clause id="ordinary-object-internal-methods-and-internal-slots-deletemetadata">
      <h1><span class="secnum">3.1.8</span>[[DeleteMetadata]]( MetadataKey, P )</h1>
      <p>When the [[DeleteMetadata]] internal method of <var>O</var> is called with <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref> <var>MetadataKey</var> and property key <var>P</var> the following steps are taken:</p>
      <emu-alg><ol><li>Assert: <var>P</var> is <emu-val>undefined</emu-val> or <emu-xref aoid="IsPropertyKey" id="_ref_51"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>P</var>) is <emu-val>true</emu-val>.</li><li>Let <var>metadataMap</var> be ?&nbsp;<emu-xref aoid="GetOrCreateMetadataMap" id="_ref_52"><a href="#getorcreatemetadatamap">GetOrCreateMetadataMap</a></emu-xref>(<var>O</var>, <var>P</var>, <emu-val>false</emu-val>).</li><li>If <var>metadataMap</var> is <emu-val>undefined</emu-val>, return <emu-val>false</emu-val>.</li><li>Return ?&nbsp;<emu-xref aoid="Invoke" id="_ref_53"><a href="https://tc39.github.io/ecma262/#sec-invoke">Invoke</a></emu-xref>(<var>metadataMap</var>, <code>"delete"</code>, <var>MetadataKey</var>).
      </li></ol></emu-alg>
    </emu-clause>
  </emu-clause>
</emu-clause>

<emu-clause id="reflection">
  <h1><span class="secnum">4</span>Reflection</h1>
  <emu-clause id="reflect">
    <h1><span class="secnum">4.1</span>The Reflect Object</h1>
    <p>This section contains amendments to the Reflect object.</p>
    <emu-clause id="reflect-metadatadecoratorfunctions">
      <h1><span class="secnum">4.1.1</span>Metadata Decorator Functions</h1>
      <p>A metadata decorator function is an anonymous built-in function that has [[MetadataKey]] and [[MetadataValue]] internal slots.</p>
      <p>When a metadata decorator function <var>F</var> is called with arguments <var>target</var> and <var>key</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Assert: <var>F</var> has a [[MetadataKey]] internal slot whose value is an <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref>, or <emu-val>undefined</emu-val>.</li><li>Assert: <var>F</var> has a [[MetadataValue]] internal slot whose value is an <emu-xref href="#sec-ecmascript-language-types"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-language-types">ECMAScript language value</a></emu-xref>, or <emu-val>undefined</emu-val>.</li><li>If <emu-xref aoid="Type" id="_ref_54"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>If <var>key</var> is not <emu-val>undefined</emu-val> and <emu-xref aoid="IsPropertyKey" id="_ref_55"><a href="https://tc39.github.io/ecma262/#sec-ispropertykey">IsPropertyKey</a></emu-xref>(<var>key</var>) is <emu-val>false</emu-val>, throw a <emu-val>TypeError</emu-val> exception.</li><li>Let <var>metadataKey</var> be the value of <var>F</var>'s [[MetadataKey]] internal slot.</li><li>Let <var>metadataValue</var> be the value of <var>F</var>'s [[MetadataValue]] internal slot.</li><li>Perform ? <var>target</var>.[[DefineMetadata]](<var>metadataKey</var>, <var>metadataValue</var>, <var>target</var>, <var>key</var>).</li><li>Return <emu-val>undefined</emu-val>.
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.metadata">
      <h1><span class="secnum">4.1.2</span>Reflect.metadata ( metadataKey, metadataValue )</h1>
      <p>When the <code>metadata</code> function is called with arguments <var>metadataKey</var> and <var>metadataValue</var>, the following steps are taken:</p>
      <emu-alg><ol><li>Let <var>decorator</var> be a new built-in function object as defined in Metadata Decorator Functions.</li><li>Set the [[MetadataKey]] internal slot of <var>decorator</var> to <var>metadataKey</var>.</li><li>Set the [[MetadataValue]] internal slot of <var>decorator</var> to <var>metadataValue</var>.</li><li>Return <var>decorator</var>.
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.definemetadata">
      <h1><span class="secnum">4.1.3</span>Reflect.defineMetadata ( metadataKey, metadataValue, target [, propertyKey] )</h1>
      <p>When the <code>defineMetadata</code> function is called with arguments <var>metadataKey</var>, <var>metadataValue</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_56"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[DefineMetadata]](<var>metadataKey</var>, <var>metadataValue</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.hasmetadata">
      <h1><span class="secnum">4.1.4</span>Reflect.hasMetadata ( metadataKey, target [, propertyKey] )</h1>
      <p>When the <code>hasMetadata</code> function is called with arguments <var>metadataKey</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_57"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[HasMetadata]](<var>metadataKey</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.hasownmetadata">
      <h1><span class="secnum">4.1.5</span>Reflect.hasOwnMetadata ( metadataKey, target [, propertyKey] )</h1>
      <p>When the <code>hasOwnMetadata</code> function is called with arguments <var>metadataKey</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_58"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[HasOwn]](<var>metadataKey</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.getmetadata">
      <h1><span class="secnum">4.1.6</span>Reflect.getMetadata ( metadataKey, target [, propertyKey] )</h1>
      <p>When the <code>getMetadata</code> function is called with arguments <var>metadataKey</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_59"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[GetMetadata]](<var>metadataKey</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.getownmetadata">
      <h1><span class="secnum">4.1.7</span>Reflect.getOwnMetadata ( metadataKey, target [, propertyKey] )</h1>
      <p>When the <code>getOwnMetadata</code> function is called with arguments <var>metadataKey</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_60"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[GetOwnMetadata]](<var>metadataKey</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.getmetadatakeys">
      <h1><span class="secnum">4.1.8</span>Reflect.getMetadataKeys ( target [, propertyKey] )</h1>
      <p>When the <code>getMetadataKeys</code> function is called with arguments <var>target</var> and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_61"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[GetMetadataKeys]](<var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.getownmetadatakeys">
      <h1><span class="secnum">4.1.9</span>Reflect.getOwnMetadataKeys ( target [, propertyKey] )</h1>
      <p>When the <code>getOwnMetadataKeys</code> function is called with arguments <var>target</var> and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_62"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[GetOwnMetadataKeys]](<var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>

    <emu-clause id="reflect.deletemetadata">
      <h1><span class="secnum">4.1.10</span>Reflect.deleteMetadata ( metadataKey, target [, propertyKey] )</h1>
      <p>When the <code>deleteMetadata</code> function is called with arguments <var>metadataKey</var>, <var>target</var>, and <var>propertyKey</var>, the following steps are taken:</p>
      <emu-alg><ol><li>If <emu-xref aoid="Type" id="_ref_63"><a href="https://tc39.github.io/ecma262/#sec-ecmascript-data-types-and-values">Type</a></emu-xref>(<var>target</var>) is not Object, throw a <emu-val>TypeError</emu-val> exception.</li><li>Return ? <var>target</var>.[[DeleteMetadata]](<var>metadataKey</var>, <var>propertyKey</var>).
      </li></ol></emu-alg>
    </emu-clause>
  </emu-clause>
</emu-clause></div></body>