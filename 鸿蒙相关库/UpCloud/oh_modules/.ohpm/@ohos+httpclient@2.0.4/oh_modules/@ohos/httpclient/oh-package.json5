{"types": "", "keywords": ["httpclient", "OpenHarmony", "Sync and Async calls", "HarmonyOS", "okhttp"], "author": "ohos_tpc", "description": "httpclient is a efficient HTTP client for OpenHarmony.", "ohos": {"org": "opensource"}, "main": "index.ts", "repository": "https://gitee.com/openharmony-tpc/httpclient", "type": "module", "version": "2.0.4", "dependencies": {"pako": "^2.1.0", "@ohos/crypto-js": "^2.0.2", "base64-js": "^1.5.1"}, "tags": ["Network", "Tool"], "license": "Apache License 2.0", "devDependencies": {}, "name": "@ohos/httpclient", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "metadata": {"sourceRoots": ["./src/main"]}, "compatibleSdkVersion": 12, "compatibleSdkType": "OpenHarmony", "obfuscated": false}