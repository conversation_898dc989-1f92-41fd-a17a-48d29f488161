/*
 * Copyright (c) 2021 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import featureAbility from '@ohos.ability.featureAbility'

function Route(netinfo) {
    this.netinfo = netinfo;
    this.currentIndex = 0;
    return this;
}

Route.prototype.getCurrentAddress = function getCurrentAddress() {

    return this.netinfo[this.currentIndex];
}
Route.prototype.getNextAddress = function getNextAddress() {
    this.currentIndex++;

    return this.netinfo[this.currentIndex];
}

Route.prototype.getCurrentIndex = function getCurrentIndex() {

    return this.currentIndex;
}

export default Route;