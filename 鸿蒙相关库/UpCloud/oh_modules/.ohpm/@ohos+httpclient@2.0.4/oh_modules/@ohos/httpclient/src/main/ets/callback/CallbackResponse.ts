/*
 * Copyright (c) 2021 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

var _body = Symbol();
var _rawResponse = Symbol();

class CallbackResponse {
    constructor(body, rawResponse) {
        this[_body] = body;
        this[_rawResponse] = rawResponse;
    }

    get code() {
        if (this[_rawResponse] == null) return null;
        return this[_rawResponse].responseCode;
    }

    get result() {
        if (this[_rawResponse] == null) return null;
        return this[_rawResponse].result;
    }

    get header() {
        if (this[_rawResponse] == null) return null;
        return this[_rawResponse].header;
    }

    get body() {
        if (this[_body] == null) return null;
        return this[_body];
    }

    get rawResponse() {
        if (this[_rawResponse] == null) return null;
        return this[_rawResponse];
    }
}

export default CallbackResponse;