/*
 * Copyright (c) 2021 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import HttpCall from '../HttpCall';
import { Exchange } from '../connection/Exchange';
import Request from '../Request';
import HttpClient from '../HttpClient';
import { Response } from '../response/Response';

abstract class Internal {
    public static instance: Internal;

    abstract addLenient(builder: any, name: string, value: string): void

    abstract equalsNonHost(a: any, b: any): boolean

    abstract code(responseBuilder): number

    abstract apply(tlsConfiguration, sslSocket, isFallback): void

    abstract newWebSocketCall(client: HttpClient, request: Request): HttpCall

    abstract initExchange(responseBuilder, exchange: Exchange): void

    abstract exchange(response: Response): Exchange
}

export default Internal