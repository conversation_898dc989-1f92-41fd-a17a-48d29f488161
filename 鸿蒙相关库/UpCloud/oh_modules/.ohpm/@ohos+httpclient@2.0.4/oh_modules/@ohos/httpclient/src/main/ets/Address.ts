/*
 * Copyright (c) 2021 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Dns } from './Dns';

export class Address {
    uriHost: string
    uriPort: number
    family:number
    mDns: Dns

    constructor(uriHost: string, uriPort: number,family?:number) {
        this.uriHost = uriHost
        this.uriPort = uriPort
        this.family = family
    }

    host(): string {
        return this.uriHost
    }

    port(): number {
        return this.uriPort
    }

    dns(): Dns {
        return this.mDns
    }
}