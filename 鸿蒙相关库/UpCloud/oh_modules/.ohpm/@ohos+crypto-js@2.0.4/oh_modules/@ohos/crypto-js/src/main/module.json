{"app": {"bundleName": "cn.openharmony.crypto", "versionCode": 1000002, "versionName": "2.0.3", "minAPIVersion": 9, "targetAPIVersion": 10, "apiReleaseType": "Release", "compileSdkVersion": "4.0.10.16", "compileSdkType": "OpenHarmony", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "library", "type": "har", "deviceTypes": ["default", "tablet"], "packageName": "@ohos/crypto-js", "installationFree": false, "virtualMachine": "ark9.0.0.0", "compileMode": "esmodule", "dependencies": []}}