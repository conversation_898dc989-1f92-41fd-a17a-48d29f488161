# error

aliyun SDK内部使用错误码接口。

## 下载安装

```shell
ohpm install @aliyun/error
```

## 使用

### 定义模块错误基类

```typescript
import { SdkError } from '@aliyun/error';

/**
 * 首先可以通过定义模块的错误码基类，以统一错误码前缀
 */
class MockSdkError extends SdkError {
  constructor(code: string, msg: string, suggestions?: string) {
    super(`M_${code}`, msg, suggestions);
  }
}
```
基类中可以规范错误码前缀等信息

### 定义具体错误类型

```typescript
class Error1 extends MockSdkError {
  constructor(msg: string) {
    super('1', msg, '这样处理问题1')
  }
}

class Error2 extends MockSdkError {
  constructor(argName: string, arg1: number) {
    super('2', `输入参数${argName}(${arg1})必须大于5}`, '请检查参数是否正确')
  }
}
```

### 使用错误定义

```typescript
import { log } from '../demo_errorability/log';

const logI = log.createLogI({ tag: 'errorTest' });

const e1 = new Error1('错误描述')
logI.d(`错误信息 ${e1}`);
logI.d(`错误码 ${e1.code}`);
logI.d(`错误信息 ${e1.message}`);
logI.d(`调用栈 ${e1.stack}`);

try {
  // do something
  throw new Error1('错误描述');
} catch (e) {
  if (e instanceof Error1) {
    // handle error
    logI.d(`捕获 错误 ${e.code} 错误信息 ${e.message}`);
  }
}
```