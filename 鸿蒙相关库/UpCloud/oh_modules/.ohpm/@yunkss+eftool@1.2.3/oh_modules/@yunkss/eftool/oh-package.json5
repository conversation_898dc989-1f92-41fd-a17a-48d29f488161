{"license": "Apache-2.0", "devDependencies": {"@ohos/axios": "2.2.2"}, "keywords": ["axios", "JSON", "UI组件", "loading", "rcp", "加密", "哈希", "文件", "级联", "弹框"], "author": "csx", "name": "@yunkss/eftool", "description": "一款高效的兼容API12的HarmonyOS工具包.封装了常用工具类如字符串、正则、加解密、图片处理、axios、rcp、json等和常用UI组件如Dialog,Loading,Cascade,通知,位置,窗口等,提供一系列快捷操作方法.", "main": "index.ets", "repository": "https://gitee.com/yunkss/ef-tool.git", "version": "1.2.3", "homepage": "https://gitee.com/yunkss", "dependencies": {}, "dynamicDependencies": {}, "metadata": {"sourceRoots": ["./src/main"]}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false}