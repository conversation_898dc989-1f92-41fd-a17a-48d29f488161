// @keepTs
// @ts-nocheck
import { IpType } from "../../f/Index";
import { INetChangeListener, INetworkManager } from './Index';
import { ORIGINAL_NETWORK_TYPE } from "../../g/j";
export declare class SystemNetworkManager implements INetworkManager {
    constructor();
    getNetType(): Promise<string>;
    getOriginalNetType(): Promise<ORIGINAL_NETWORK_TYPE>;
    getNetStack(): Promise<IpType.V4 | IpType.V6 | IpType.Both>;
    getNetStackCache(): IpType.V4 | IpType.V6 | IpType.Both;
    addChangeListener(listener: INetChangeListener): void;
}
