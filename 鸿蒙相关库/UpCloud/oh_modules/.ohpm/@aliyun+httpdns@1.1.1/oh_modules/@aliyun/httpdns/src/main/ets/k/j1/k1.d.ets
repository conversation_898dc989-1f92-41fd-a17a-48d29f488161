// @keepTs
// @ts-nocheck
import { IPreferences } from './Index';
import common from '@ohos.app.ability.common';
export declare class SystemPreferences implements IPreferences {
    private preferences;
    private logI;
    constructor(context: common.UIAbilityContext | undefined, accountId: string);
    get(key: string): Promise<string | undefined>;
    put(key: string, value: string | undefined): Promise<void>;
    clean(): Promise<void>;
}
