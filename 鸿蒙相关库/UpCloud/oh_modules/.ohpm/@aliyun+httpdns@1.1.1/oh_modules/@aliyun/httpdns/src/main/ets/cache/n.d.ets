// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { InnerHttpDnsResult, IpType } from "../f/Index";
import { ITime } from "../k/l/Index";
import { IRecordRepo } from './Index';
export declare class RecordRepo implements IRecordRepo {
    private defaultRepo;
    private time;
    private enableExpiredIp;
    private fixedIpHosts?;
    private sDnsRepoMap;
    constructor(time: ITime, d1: boolean, e1?: Array<string>);
    private getRepo;
    cleanMayChangeIp(t?: Array<string>, u?: boolean): void;
    getHostsWithoutFixedIp(): Map<string, IpType>;
    getResult(host: string, type: IpType, cacheKey?: string): InnerHttpDnsResult;
    put(records: HostRecord[], cacheKey?: string): void;
}
