// @keepTs
// @ts-nocheck
import { HttpDnsError } from "../g/h";
import { ITime } from "../k/l/Index";
import { RequestWatcher } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
export declare class ShiftServerWatch<R> extends RequestWatcher<RequestConfig, R> {
    private time;
    private begin;
    private serverIps;
    private index;
    constructor(time: ITime, i18: Array<string>);
    onStart(config: RequestConfig): RequestConfig;
    onFail(config: RequestConfig, error: HttpDnsError): HttpDnsError;
}
