// @keepTs
// @ts-nocheck
import { HttpDnsConfig, IHttpDnsService } from './Index';
/**
 * 获取HttpDns接口实例
 * @param accountId
 * @returns
 */
export declare function getHttpDnsService(accountId: string): Promise<IHttpDnsService>;
/**
 * 配置HttpDns实例，必须在getService之前调用，才能在初始化使用配置
 * 初始化之后 使用 IHttpDnsService.changeConfig() 方法修改配置
 * @param accountId
 * @param config
 */
export declare function configHttpDnsService(accountId: string, config: HttpDnsConfig): void;
/**
 * for test only
 * 模拟应用重启httpdns实例清空重启
 */
export declare function cleanService(): void;
export declare function cleanServiceWithAll(): Promise<void>;
