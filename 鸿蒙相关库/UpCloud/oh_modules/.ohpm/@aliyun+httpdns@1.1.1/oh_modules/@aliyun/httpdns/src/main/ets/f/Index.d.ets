// @keepTs
// @ts-nocheck
import common from '@ohos.app.ability.common';
import { REGION_STR } from "../g/j";
import { HttpDnsError } from "../g/h";
/**
 * 要解析的IP类型
 */
export declare enum IpType {
    V4 = 0,
    V6 = 1,
    Both = 2,
    /**
     * 根据网络栈自动判断
     */
    Auto = 3
}
/**
 * IP优选的端口配置
 */
export interface HostConfigItem {
    host: string;
    port: number;
}
/**
 * 自定义ttl的回调接口
 */
export interface ICacheTtlCustomizer {
    /**
     * 返回 自定义的ttl
     * @param host
     * @param type
     * @param serverTtlInSec httpdns服务返回的ttl
     * @returns
     */
    getCacheTtlInSec(host: string, type: IpType, serverTtlInSec: number): number;
}
/**
 * HttpDns配置
 */
export interface HttpDnsConfig {
    /**
     * 地区配置，区分 大陆 新加坡sg 香港hk 等
     */
    region?: string | Region;
    /**
     * 是否使用https请求解析域名
     */
    useHttps?: boolean;
    /**
     * 是否允许返回过期IP
     */
    enableExpiredIp?: boolean;
    /**
     * 是否开启本地缓存
     */
    enableDiskCache?: boolean;
    /**
     * 是否网络环境切换后自动解析域名
     */
    reResolveCachedHostsAfterNetworkChanged?: boolean;
    /**
     * 域名解析请求的超时配置
     */
    timeoutInMs?: number;
    /**
     * IP优选配置
     */
    hostConfigsForIpRanking?: Array<HostConfigItem>;
    /**
     * 固定IP的域名配置，可以减少配置域名的解析次数
     */
    hostsWithFixedIp?: Array<string>;
    /**
     * 自定义缓存接口
     */
    ttlCustomizer?: ICacheTtlCustomizer;
    /**
     * 加签密钥
     */
    secretKey?: string;
    /**
     * ability上下文，内部使用系统API时使用
     */
    context?: common.UIAbilityContext;
    /**
     * 是否开启可观测埋点
     */
    enableObservable?: boolean;
    /**
     * 业务自定义tag
     */
    bizTags?: Array<string>;
}
/**
 * HttpDns 内部使用的配置，基于用户的配置结合缺省配置实现
 */
export interface InitConfig extends HttpDnsConfig {
    region: REGION_STR;
    useHttps: boolean;
    enableExpiredIp: boolean;
    enableDiskCache: boolean;
    reResolveCachedHostsAfterNetworkChanged: boolean;
    timeoutInMs: number;
    hostConfigsForIpRanking?: Array<HostConfigItem>;
    hostsWithFixedIp?: Array<string>;
    ttlCustomizer?: ICacheTtlCustomizer;
    secretKey?: string;
    context?: common.UIAbilityContext;
    enableObservable: boolean;
    bizTags?: Array<string>;
}
/**
 * 域名解析的结果
 */
export interface HttpDnsResult {
    /**
     * 被解析的域名
     */
    host: string;
    /**
     * v4解析结果
     */
    ipv4s?: Array<string>;
    /**
     * v6解析结果
     */
    ipv6s?: Array<string>;
    /**
     * SDNS的额外参数，请参考 https://help.aliyun.com/document_detail/2766588.html
     */
    extra?: Record<string, string>;
    /**
     * SDNS的额外参数，字符串格式，请参考 https://help.aliyun.com/document_detail/2766588.html
     */
    extraStr?: string;
}
export declare enum ResultStatus {
    NoCache = 0,
    CacheAvailable = 1,
    CacheExpiredAllowed = 2,
    CacheExpiredNotAllowed = 3
}
export interface InnerHttpDnsResult {
    /**
     * 结果IP的状态
     */
    status: ResultStatus;
    /**
     * 要返回给用户的结果
     */
    result: HttpDnsResult;
    /**
     * 本次请求时，缓存的状态
     */
    cacheStatus?: ResultStatus;
    /**
     * 异常报错
     */
    error?: HttpDnsError;
}
/**
 * 目前支持的region
 */
export declare enum Region {
    MAINLAND = 1,
    HK = 2,
    SG = 3,
    DE = 4,
    US = 5
}
/**
 * HttpDns对外的主要接口
 * 控制降级的接口，暂时不提供，因为鸿蒙这边需要主动指定域名，而不是统一设置，所以不需要控制降级
 */
export interface IHttpDnsService {
    /**
     * 批量解析域名
     * @param hosts
     * @param requestIpType 缺省 是 V4
     * @returns
     */
    resolveHosts(hosts: Array<string>, requestIpType?: IpType): Promise<void>;
    /**
     * 解析域名，同步方法，没有缓存时，返回空
     * @param host
     * @param type
     * @returns
     */
    getHttpDnsResultSyncNonBlocking(host: string, type?: IpType): HttpDnsResult;
    /**
     * 解析域名，异步方法，没有缓存时，请求网络，返回结果，有缓存时，使用缓存
     * @param host
     * @param type
     * @returns
     */
    getHttpDnsResultAsync(host: string, type?: IpType): Promise<HttpDnsResult>;
    /**
     * 修改region， 区分 大陆 新加坡（sg） 香港（hk）。
     * @param region  缺省 是 大陆
     */
    changeRegion(region: string): Promise<void>;
    /**
     *
     * @param region
     * @returns
     */
    changeRegion(region: Region): Promise<void>;
    /**
     * 设置服务器时间，用于正确的加签
     * @param timeInSecond
     */
    setServerTimeInSecond(timeInSecond: number): void;
    /**
     * 清理缓存
     * @param hosts
     * @returns
     */
    cleanHostCache(hosts?: Array<string>): Promise<void>;
    /**
     * 获取sid， 用于排查问题
     * @returns
     */
    getSessionId(): string;
    /**
     * 设置 sdns的全局参数
     * @param params
     */
    setSDnsGlobalParams(params: Record<string, string>): void;
    /**
     * 清除全局参数
     */
    clearSDnsGlobalParams(): void;
    /**
     * 同步 sdns解析
     * @param host
     * @param params
     * @param cacheKey
     * @param type
     * @returns
     */
    getSDnsResultSyncNonBlocking(host: string, params: Record<string, string>, cacheKey: string, type?: IpType): HttpDnsResult;
    /**
     * 带参数的dns解析。 根据参数不同，可以返回不同的结果。
     * @param host
     * @param params
     * @param cacheKey
     * @param type
     * @returns
     */
    getSDnsResultAsync(host: string, params: Record<string, string>, cacheKey: string, type?: IpType): Promise<HttpDnsResult>;
}
