// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { IEventService } from './Index';
/**
 * 从调度服务的下行，获取事件配置
 */
export declare class EventConfigInterrupter extends RequestInterrupter<RequestConfig, SsResponse> {
    private eventService;
    constructor(r10: IEventService);
    run(q10: RequestChain<RequestConfig, SsResponse>, config: RequestConfig): Promise<SsResponse>;
}
