// @keepTs
// @ts-nocheck
import { HostRecord } from "../../g/j";
import { IDB } from './Index';
import common from '@ohos.app.ability.common';
export declare class SystemDB implements IDB {
    private context;
    private isEnable;
    private storeConfig;
    private logI;
    constructor(context: common.UIAbilityContext | undefined, account: string);
    private getDB;
    setEnable(enable: boolean): void;
    load(netId: string): Promise<HostRecord[]>;
    save(netId: string, records: HostRecord[]): Promise<void>;
    clean(h5?: Array<string>): Promise<void>;
}
