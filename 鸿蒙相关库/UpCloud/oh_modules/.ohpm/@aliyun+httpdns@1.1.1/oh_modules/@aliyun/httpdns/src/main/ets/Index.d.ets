// @keepTs
// @ts-nocheck
import { ILogger } from "./e/Index";
import { HttpDnsConfig, IHttpDnsService } from "./f/Index";
export declare namespace httpdns {
    /**
     * 获取HttpDns接口实例
     * @param accountId
     * @returns
     */
    function getService(accountId: string): Promise<IHttpDnsService>;
    /**
     * 配置HttpDns实例，必须在getService之前调用，才能在初始化使用配置
     * 初始化之后 使用 IHttpDnsService.changeConfig() 方法修改配置
     * @param accountId
     * @param config
     */
    function configService(accountId: string, config: HttpDnsConfig): void;
    /**
     * 开启HttpDns内部的HiLog输出
     */
    function enableHiLog(): void;
    /**
     * 添加日志接口，获取HttpDns日志
     * @param logger
     */
    function addLogger(n12: ILogger): void;
    /**
     * 移除日志接口
     * @param logger
     */
    function removeLogger(m12: ILogger): void;
}
