// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { InnerHttpDnsResult, IpType } from "../f/Index";
export interface ISingleRecordRepo {
    put(records: Array<HostRecord>): void;
    getResult(host: string, type: IpType): InnerHttpDnsResult;
    cleanMayChangeIp(hosts?: Array<string>): void;
    getHostsWithoutFixedIp(): Map<string, IpType>;
}
export interface IRecordRepo {
    put(records: Array<HostRecord>, cacheKey?: string): void;
    getResult(host: string, type: IpType, cacheKey?: string): InnerHttpDnsResult;
    cleanMayChangeIp(hosts?: Array<string>, includeSDns?: boolean): void;
    getHostsWithoutFixedIp(): Map<string, IpType>;
}
