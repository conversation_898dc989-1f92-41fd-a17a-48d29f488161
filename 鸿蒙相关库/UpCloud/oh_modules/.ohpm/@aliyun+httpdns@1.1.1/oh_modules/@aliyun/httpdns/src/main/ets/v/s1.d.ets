// @keepTs
// @ts-nocheck
import { ITime } from "../k/l/Index";
import { CallSdkApiEventOptions, CleanHostCacheOptions, ENetworkType, Event, EventRecord, PreResolveHostCacheOptions, QueryHttpDnsApiEventOptions, RemoteResolveEventOptions, UpdateRegionServerIpsOptions } from './Index';
export declare class EventFormat {
    private time;
    constructor(time: ITime);
    private toERequestIpType;
    private toECacheStatusForCallApi;
    private toECacheStatusForRemoteScene;
    private toCallSdkApiResult;
    private convertCallSdkApiTag;
    private convertRemoteResolveTag;
    private convertHttpResolveTag;
    private convertHttpDnsIps;
    callSdkApiToRecord(options: CallSdkApiEventOptions, networkType: ENetworkType): EventRecord;
    remoteResolveToRecord(options: RemoteResolveEventOptions, networkType: ENetworkType): EventRecord;
    httpResolveToRecord(options: QueryHttpDnsApiEventOptions, networkType: ENetworkType): EventRecord;
    batchResolveToRecord(options: PreResolveHostCacheOptions, networkType: ENetworkType): EventRecord;
    batchHttpResolveToRecord(options: QueryHttpDnsApiEventOptions, networkType: ENetworkType): EventRecord;
    cleanHostCacheToRecord(options: CleanHostCacheOptions, networkType: ENetworkType): EventRecord;
    updateRegionServerIpsToRecord(options: UpdateRegionServerIpsOptions, networkType: ENetworkType): EventRecord;
    reportingRateExceptionToRecord(s10: Event, networkType: ENetworkType): EventRecord;
}
