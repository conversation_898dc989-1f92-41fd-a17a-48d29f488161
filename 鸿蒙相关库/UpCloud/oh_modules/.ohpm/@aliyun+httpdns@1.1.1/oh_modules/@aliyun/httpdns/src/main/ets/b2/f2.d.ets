// @keepTs
// @ts-nocheck
import { LogI } from '@aliyun/logger';
import { INetworkManager } from "../k/u/Index";
import { RequestConfig } from "../o1/p1";
import { IServerIpService } from "../q1/Index";
import { InitConfig, IpType } from "../f/Index";
import { ISignService } from "../v1/Index";
export declare const logI: LogI;
export declare function getResolveConfig(config: InitConfig, i14: IServerIpService, j14: ISignService, network: INetworkManager, accountId: string, host: string, type: IpType.V4 | IpType.V6 | IpType.Both, params?: Record<string, string>): Promise<RequestConfig>;
export declare function getBatchResolveConfig(config: InitConfig, b14: IServerIpService, c14: ISignService, network: INetworkManager, accountId: string, host: Array<string>, type: IpType.V4 | IpType.V6 | IpType.Both, params?: Record<string, string>): Promise<RequestConfig>;
