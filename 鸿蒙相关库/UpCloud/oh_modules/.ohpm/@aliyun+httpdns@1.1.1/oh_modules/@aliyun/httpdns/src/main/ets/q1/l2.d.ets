// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { ServerIp } from "./k2";
/**
 * 如果失败了，就把结果设置为目标region的默认服务IP
 */
export declare class FallbackToDefaultServerIp extends RequestInterrupter<RequestConfig, SsResponse> {
    private fallbackServerIp;
    constructor(u15: ServerIp);
    run(t15: RequestChain<RequestConfig, SsResponse>, config: RequestConfig): Promise<SsResponse>;
}
