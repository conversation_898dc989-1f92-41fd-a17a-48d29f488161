// @keepTs
// @ts-nocheck
import { HttpDnsError } from "../g/h";
import { IInfo } from "../k/a1/Index";
import { IHttp } from "../k/u/Index";
export interface IRequestConfig {
    url(): string;
    timeoutInMs(): number;
    retryIndex?: number;
}
export declare class RequestChain<C extends IRequestConfig, R> {
    private list;
    private index;
    private finalRequest;
    constructor(list: Array<RequestInterrupter<C, R>>, request: o12<C, R>);
    process(config: C): Promise<R>;
}
export declare class RequestInterrupter<C extends IRequestConfig, R> {
    run(w12: RequestChain<C, R>, config: C): Promise<R>;
}
export declare class RequestWatcher<C extends IRequestConfig, R> extends RequestInterrupter<C, R> {
    onStart(config: C): C;
    onSuccess(config: C, result: R): R;
    onFail(config: C, error: HttpDnsError): HttpDnsError;
    run(u12: RequestChain<C, R>, config: C): Promise<R>;
}
export interface ResponseTranslator<T> {
    parse(response: string): T;
}
declare class o12<C extends IRequestConfig, R> {
    private http;
    private info;
    private b1;
    constructor(http: IHttp, info: IInfo, t12: ResponseTranslator<R>);
    run(config: C): Promise<R>;
}
export declare class RequestTask<C extends IRequestConfig, R> {
    private finalRequest;
    private list;
    constructor(http: IHttp, info: IInfo, q12: ResponseTranslator<R>);
    add(interrupt: RequestInterrupter<C, R>): RequestTask<C, R>;
    run(config: C): Promise<R>;
}
export {};
