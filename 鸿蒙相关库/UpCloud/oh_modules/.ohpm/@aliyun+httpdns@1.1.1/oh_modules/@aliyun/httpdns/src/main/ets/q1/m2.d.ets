// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { ISpeedUtil } from "../k/u/Index";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { ServerIp } from "./k2";
/**
 * 服务IP调度请求拦截器，给服务IP测速
 */
export declare class IpRankForServerIpInterrupter extends RequestInterrupter<RequestConfig, SsResponse> {
    private speedUtil;
    constructor(z15: ISpeedUtil);
    run(y15: RequestChain<RequestConfig, SsResponse>, config: RequestConfig): Promise<SsResponse>;
}
export declare function ipRankForServerIp(serverIp: ServerIp, v15: ISpeedUtil): Promise<ServerIp>;
