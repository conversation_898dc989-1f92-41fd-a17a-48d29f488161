// @keepTs
// @ts-nocheck
import { HttpOptions, IHttp } from './Index';
/**
 * 网络接口的系统实现
 * 注意这里只保证httpdns请求，其他的请求不一定可用。
 */
export declare class HttpImpl implements IHttp {
    private logI;
    private innerGet;
    private innerHttpsGet;
    get(url: string, headers?: Map<string, string>, options?: HttpOptions): Promise<string>;
    post(url: string, body: string, headers: Map<string, string>): Promise<void>;
}
