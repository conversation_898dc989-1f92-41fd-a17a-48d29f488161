// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { InnerHttpDnsResult, IpType } from "../f/Index";
import { ITime } from "../k/l/Index";
import { ISingleRecordRepo } from './Index';
export declare class SingleRecordRepo implements ISingleRecordRepo {
    private time;
    private enableExpiredIp;
    private fixedIpHosts;
    private resolvedHost;
    constructor(time: ITime, c2: boolean, d2?: Array<string>);
    private cache;
    private k;
    cleanMayChangeIp(u1?: Array<string>): void;
    getHostsWithoutFixedIp(): Map<string, IpType>;
    getResult(host: string, type: IpType): InnerHttpDnsResult;
    put(records: HostRecord[]): void;
}
