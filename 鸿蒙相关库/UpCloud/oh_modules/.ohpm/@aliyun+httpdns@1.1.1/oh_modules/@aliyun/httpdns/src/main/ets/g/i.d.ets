// @keepTs
// @ts-nocheck
import { IpType } from "../f/Index";
import { RegionDefaultConfig } from "./j";
/**
 * 中国大陆region标识
 */
export declare const REGION_MAINLAND = "";
/**
 * 中国香港region标识
 */
export declare const REGION_HK = "hk";
/**
 * 新加坡region标识
 */
export declare const REGION_SG = "sg";
/**
 * 德国region标识
 */
export declare const REGION_DE = "de";
/**
 * 美国region标识
 */
export declare const REGION_US = "us";
/**
 * 代表就近调度的region标识
 */
export declare const REGION_GLOBAL = "global";
/**
 * 预发测试region标识
 */
export declare const REGION_TEST = "test";
/**
 * 配置到这里才算是内置的region
 */
export declare const REGION_CONFIGS: RegionDefaultConfig[];
export declare const SDK_DOMAIN = 32768;
export declare const SDK_VERSION = "1.1.1";
export declare const DEFAULT_REGION = "";
export declare const DEFAULT_USE_HTTPS = false;
export declare const DEFAULT_EXPIRED_IP = true;
export declare const DEFAULT_DISK_CACHE = false;
export declare const DEFAULT_RE_RESOLVE_CACHED_HOSTS_AFTER_NETWORK_CHANGED = true;
export declare const DEFAULT_TIMEOUT = 2000;
export declare const DEFAULT_ENABLE_OBSERVABLE = true;
/**
 * 默认解析重试次数
 */
export declare const DEFAULT_MAX_TIMES = 2;
export declare const DEFAULT_SERVER_IP_RANK_PORT = 80;
export declare const INIT_SERVER_DOMAIN_ENDS = "aliyuncs.com";
/**
 * 用户服务level不匹配
 * （用户有不同的level，高level有一些专用的服务节点，如果一个低level的用户，向一个高level专用的服务节点请求，就会返回此错误）
 * <p>
 * 需要重试 切换服务IP
 */
export declare const SERVER_CODE_SERVICE_LEVEL_DENY = "ServiceLevelDeny";
/**
 * 未用签名访问
 * 不重试 不切换
 * 生成空解析 缓存1小时
 */
export declare const SERVER_CODE_UNSIGNED = "UnsignedInterfaceDisabled";
/**
 * 签名过期
 * 不重试 不切换 （sdk逻辑保证不应该过期）
 */
export declare const SERVER_CODE_SIGNATURE_EXPIRED = "SignatureExpired";
/**
 * 签名验证失败
 * 不重试 不切换
 */
export declare const SERVER_CODE_INVALID_SIGNATURE = "InvalidSignature";
/**
 * 账户服务level缺失
 * 不重试 不切换
 * 生成空解析 缓存1小时
 */
export declare const SERVER_CODE_INVALID_ACCOUNT = "InvalidAccount";
/**
 * 账户不存在或者禁用
 * 不重试 不切换
 * 生成空解析 缓存1小时
 */
export declare const SERVER_CODE_ACCOUNT_NOT_EXISTS = "AccountNotExists";
/**
 * 签名有效时间过长
 * 不重试 不切换
 */
export declare const SERVER_CODE_INVALID_DURATION = "InvalidDuration";
/**
 * 无效域名
 * 不重试 不切换
 */
export declare const SERVER_CODE_INVALID_HOST = "InvalidHost";
/**
 * 特定网络环境下，默认的服务IP类型
 * @param netType
 * @returns
 */
export declare function defaultServerIpType(m2: IpType.V4 | IpType.V6 | IpType.Both): IpType.V4 | IpType.V6;
