// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { ITime } from "../k/l/Index";
import { IEventService } from "../v/Index";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
export default class AddEventInterrupter extends RequestInterrupter<RequestConfig, Array<HostRecord>> {
    private time;
    private eventService;
    constructor(time: ITime, j13: IEventService);
    private getRequestIpType;
    private getHost;
    run(b13: RequestChain<RequestConfig, HostRecord[]>, config: RequestConfig): Promise<HostRecord[]>;
}
