{"app": {"bundleName": "com.aliyun.ams.httpdns.demo2", "versionCode": 1000002, "versionName": "1.0.2", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "httpdns", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.GET_NETWORK_INFO"}], "packageName": "@aliyun/httpdns", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}