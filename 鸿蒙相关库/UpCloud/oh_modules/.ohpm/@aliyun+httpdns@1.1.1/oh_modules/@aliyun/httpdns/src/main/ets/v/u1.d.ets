// @keepTs
// @ts-nocheck
import { EventRecord } from './Index';
import { ITime } from "../k/l/Index";
export declare class FullError extends Error {
}
export declare class LimitedError extends Error {
}
export declare class EventQueue {
    /**
     * 队列存储最大值
     */
    private maxSize;
    /**
     * 限流阈值
     */
    private rateLimitingThreshold;
    /**
     * 限流判断的时间窗口
     */
    private rateLimitingThresholdTime;
    private queue;
    private slidingTimeWindow;
    private eventAggregate;
    private logI;
    constructor(time: ITime);
    push(record: EventRecord): void;
    getSize(): number;
    get(size: number): Array<EventRecord>;
}
