// @keepTs
// @ts-nocheck
import { REGION_STR } from "../g/j";
import { IpType } from "../f/Index";
/**
 * 记录一组服务IP的状态，目前正在用的是哪个，哪些可用，哪些不可用
 */
export declare class ServerIpStatus {
    /**
     * 服务IP列表
     */
    private ips;
    /**
     * 服务IP当前使用的Index
     */
    private index;
    /**
     * 服务IP的状态, 缺省可用， false不可用
     */
    private status;
    constructor(s22: Array<string>);
    /**
     * 获取当前的服务IP
     * @returns
     */
    getCurrentServerIp(): string;
    /**
     * 判断服务IP是否可用
     * @param serverIp
     * @returns
     */
    isServerIpGood(serverIp: string): boolean;
    getAllGoodServerIp(): string[];
    getAllServerIp(): string[];
    private setStatus;
    private moveToNextGoodServerIp;
    setStatusAndPrepareGoodServerIp(serverIp: string, status: boolean): void;
    isEqual(o16: string[] | ServerIpStatus): boolean;
    static FromJson(data: string): ServerIpStatus;
    toJson(): string;
}
/**
 * 服务IP，包括一些状态、IPv4、IPv6
 */
export declare class ServerIp {
    /**
     * 上次更新时间
     */
    private lt;
    /**
     * V4 服务IP
     */
    v4: ServerIpStatus;
    /**
     * V6 服务IP
     */
    v6: ServerIpStatus;
    constructor(j16: number, k16: ServerIpStatus, l16: ServerIpStatus);
    needUpdate(now: number, i16: number): boolean;
    get(ipType: IpType.V4 | IpType.V6): ServerIpStatus;
    getLastUpdateTime(): number;
    static FromJson(data: string): ServerIp;
    toJson(): string;
    static getDefaultServer(region: REGION_STR): ServerIp;
}
