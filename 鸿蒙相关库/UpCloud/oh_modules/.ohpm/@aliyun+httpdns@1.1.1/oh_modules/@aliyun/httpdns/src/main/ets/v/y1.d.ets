// @keepTs
// @ts-nocheck
import { EventConfig, SsResponse } from "../g/j";
import { IInfo } from "../k/a1/Index";
import { IHttp, INetworkManager } from "../k/u/Index";
import { IPreferences } from "../k/j1/Index";
import { ITime } from "../k/l/Index";
import { IRandom } from "../k/m1/Index";
import { RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { IServerChangeListener } from "../q1/Index";
import { InitConfig } from "../f/Index";
import { ISignService } from "../v1/Index";
import { CallSdkApiEventOptions, CleanHostCacheOptions, IEventService, PreResolveHostCacheOptions, QueryHttpDnsApiEventOptions, RemoteResolveEventOptions, UpdateRegionServerIpsOptions } from './Index';
export declare class EventService implements IEventService, IServerChangeListener {
    private eventFormat;
    private eventQueue;
    private worker;
    private reporter;
    private network;
    private random;
    private preference;
    private userEnable;
    private selfEnable;
    private serverConfig;
    private serverEnable;
    private configCache;
    private selectedRegion;
    private logI;
    constructor(p22: boolean, account: string, config: InitConfig, time: ITime, http: IHttp, network: INetworkManager, info: IInfo, random: IRandom, q22: IPreferences, r22: ISignService);
    private isEnable;
    init(): Promise<void>;
    private addRecord;
    addApiResolveEvent(options: CallSdkApiEventOptions): Promise<void>;
    addRemoteResolveEvent(options: RemoteResolveEventOptions): Promise<void>;
    addQueryHttpDnsApiEvent(options: QueryHttpDnsApiEventOptions): Promise<void>;
    addPreResolveHostCacheEvent(options: PreResolveHostCacheOptions): Promise<void>;
    addBatchQueryHttpDnsApiEvent(options: QueryHttpDnsApiEventOptions): Promise<void>;
    addCleanHostCacheEvent(options: CleanHostCacheOptions): Promise<void>;
    addUpdateRegionServerIpsEvent(options: UpdateRegionServerIpsOptions): Promise<void>;
    private resetConfig;
    private doChangeConfig;
    onServerChange(h11: boolean, region: string): void;
    updateServerConfig(region: string, config: EventConfig): void;
    getInterrupter(): RequestInterrupter<RequestConfig, SsResponse>;
}
