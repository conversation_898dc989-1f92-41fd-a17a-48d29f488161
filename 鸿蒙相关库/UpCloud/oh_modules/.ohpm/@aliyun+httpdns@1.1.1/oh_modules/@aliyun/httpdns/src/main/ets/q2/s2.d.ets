// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { SwitchService } from './Index';
/**
 * 从调度服务的下行，获取是否禁用httpdns
 */
export declare class SwitchInterrupter extends RequestInterrupter<RequestConfig, SsResponse> {
    private service;
    constructor(service: SwitchService);
    run(k22: RequestChain<RequestConfig, SsResponse>, config: RequestConfig): Promise<SsResponse>;
}
