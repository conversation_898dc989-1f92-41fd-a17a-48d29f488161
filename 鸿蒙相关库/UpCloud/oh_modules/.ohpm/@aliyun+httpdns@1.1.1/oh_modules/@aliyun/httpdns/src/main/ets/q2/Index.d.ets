// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { IPreferences } from "../k/j1/Index";
import { RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { ISsInterrupterProvider } from "../q1/Index";
/**
 * 远程开关，
 * 服务可以通过调度接口下发字段禁用httpdns
 */
export interface ISwitchService extends ISsInterrupterProvider {
    /**
     * 读取之前的状态
     * @returns
     */
    init(): Promise<void>;
    /**
     * 是否启用
     * @returns
     */
    isEnable(): boolean;
}
export declare class SwitchService implements ISwitchService {
    private preferences;
    private enable;
    constructor(preferences: IPreferences);
    init(): Promise<void>;
    isEnable(): boolean;
    getInterrupter(): RequestInterrupter<RequestConfig, SsResponse>;
    updateStatus(enable: boolean): void;
}
