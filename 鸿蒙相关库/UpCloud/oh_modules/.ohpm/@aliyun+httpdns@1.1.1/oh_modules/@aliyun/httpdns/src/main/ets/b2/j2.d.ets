// @keepTs
// @ts-nocheck
import { InitConfig, IpType } from "../f/Index";
import { HostRecord } from "../g/j";
import { IResolveService } from './Index';
import { IHttp, INetworkManager, ISpeedUtil } from "../k/u/Index";
import { ITime } from "../k/l/Index";
import { IServerChangeListener, IServerIpService } from "../q1/Index";
import { ISignService } from "../v1/Index";
import { IEventService } from "../v/Index";
import { IInfo } from "../k/a1/Index";
export declare class ResolveMode {
    private status;
    private time;
    private lastRequestTime;
    constructor(time: ITime);
    canRequest(): boolean;
    down(): void;
    reset(): void;
    isSniffMode(): boolean;
}
export declare class ResolveService implements IResolveService, IServerChangeListener {
    private accountId;
    private config;
    private http;
    private info;
    private time;
    private serverIpService;
    private signService;
    private network;
    private eventService;
    private speedUtil;
    private mode;
    private globalSDnsParams;
    constructor(accountId: string, config: InitConfig, http: IHttp, info: IInfo, time: ITime, l22: IServerIpService, m22: ISignService, network: INetworkManager, n22: ISpeedUtil, o22: IEventService);
    onServerChange(h15: boolean, region: string): void;
    setSDnsGlobalParams(params: Record<string, string>): void;
    clearSDnsGlobalParams(): void;
    private combineParams;
    resolve(host: string | Array<string>, type: IpType.V4 | IpType.V6 | IpType.Both, a15: boolean, params?: Record<string, string>): Promise<HostRecord[]>;
}
