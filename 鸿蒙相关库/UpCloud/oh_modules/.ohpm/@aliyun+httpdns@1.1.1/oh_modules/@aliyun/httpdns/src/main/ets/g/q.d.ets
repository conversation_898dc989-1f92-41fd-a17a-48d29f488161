// @keepTs
// @ts-nocheck
import { ISpeedUtil } from "../k/u/Index";
import { Region, ResultStatus } from "../f/Index";
import { REGION_STR } from "./j";
import { ECacheStatusForCallApi, ECacheStatusForRemoteScene } from "../v/Index";
import { ValueType } from '@kit.ArkData';
/**
 * 简单的判断是否是host，ipv4 也会通过，ipv6地址因为有:\不会通过
 * @param host
 * @returns
 */
export declare function isHost(host: string): boolean;
export declare function isInitServerDomain(ip: string): boolean;
export declare function recordMerge(from: object, to: Record<string, string>): void;
export declare function recordCombine(from?: Record<string, string>, to?: Record<string, string>): Record<string, string>;
export declare const decodeHtml: (str: string) => string;
export declare function fixRegion(region?: string | Region): REGION_STR;
/**
 * 判断数组是否相同，元素的顺序也要相同
 * @param actual
 * @param expected
 * @returns
 */
export declare function isArrayEqual<T>(g4: Array<T> | null | undefined, expected: Array<T> | null | undefined): boolean;
export declare function testSpeed(r3: ISpeedUtil, s3: Array<string>, port: number): Promise<Array<string>>;
export declare function convertStatusForCacheScene(status: ResultStatus): ECacheStatusForCallApi;
export declare function convertStatusForRemoteScene(status: ResultStatus): ECacheStatusForRemoteScene;
export declare function checkTag(tag: string): boolean;
export declare function tagPreCheck(tags?: Array<string>): Array<string> | undefined;
export declare function addTagsToQuery(query: Record<string, ValueType>, tags?: Array<string>): void;
export declare function getQueryStr(query: Record<string, ValueType>): string;
