# HTTPDNS
HTTPDNS是面向多端应用（移动端APP，PC客户端应用）的域名解析服务，具有域名防劫持、精准调度、实时解析生效的特性。

这里是依赖HTTPDNS服务，提供的鸿蒙SDK，辅助鸿蒙开发者更好的使用HTTPDNS服务

## 下载安装

```shell
ohpm install @aliyun/httpdns
```

## 使用

### 配置

```typescript
import { AbilityConstant, UIAbility, Want } from '@kit.AbilityKit';
import { httpdns } from '@aliyun/httpdns';

const ACCOUNT_ID = '这里需要替换为阿里云HTTPDNS控制台的Account ID'
        
export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    // ************* 初始化配置 begin *************
    httpdns.configService(ACCOUNT_ID, {
      context: this.context,
    });
    // ************* 初始化配置 end *************
  }
  // 省略其它代码
}
```
**必须**在使用HTTPDNS服务之前，先进行配置

### 在网络请求中使用HTTPDNS服务

```typescript
import http from '@ohos.net.http';
import connection from '@ohos.net.connection';
import Url from '@ohos.url';
import { httpdns, IpType } from '@aliyun/httpdns';
        
const ACCOUNT_ID = '这里需要替换为阿里云HTTPDNS控制台的Account ID'
        
// 封装网络请求
export async function requestWithHttpDns(url: string, options: http.HttpRequestOptions): Promise<http.HttpResponse> {
  let urlObject = Url.URL.parseURL(url);
  const host = urlObject.hostname;
  // ************* HTTPDNS解析获取域名 begin *************
  const httpdnsService = await httpdns.getService(ACCOUNT_ID);
  const result = await httpdnsService.getHttpDnsResultAsync(host, IpType.Auto);
  // ************* HTTPDNS解析获取域名 end *************
  // ************* 通过系统API设置DNS规则 begin *************
  try {
    await connection.removeCustomDnsRule(host);
  } catch (ignored) {
  }
  if ((result.ipv4s?.length ?? 0) > 0) {
    await connection.addCustomDnsRule(host, result.ipv4s);
  } else if ((result.ipv6s?.length ?? 0) > 0) {
    await connection.addCustomDnsRule(host, result.ipv6s);
  } else {
    console.log(`httpdns解析没有结果，不设置dns`);
  }
  // ************* 通过系统API设置DNS规则 begin *************
  // ************* 通过系统API进行网络请求 begin *************
  const httpRequest = http.createHttp();
  return httpRequest.request(url, options);
  // ************* 通过系统API进行网络请求 end *************
}

// 调用网络请求
requestWithHttpDns('https://www.aliyun.com/', {method: http.RequestMethod.GET})
```

### 更多定制能力
请参考[阿里云官方文档](https://help.aliyun.com/document_detail/2766017.html)