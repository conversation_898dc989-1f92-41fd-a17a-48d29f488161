// @keepTs
// @ts-nocheck
import { IHttp, INetworkManager, ISpeedUtil } from "../k/u/Index";
import { IPreferences } from "../k/j1/Index";
import { ITime } from "../k/l/Index";
import { InitConfig, IpType, Region } from "../f/Index";
import { IServerChangeListener, IServerIpService, ISsInterrupterProvider } from './Index';
import { IEventService, ECallFrom } from "../v/Index";
import { IInfo } from "../k/a1/Index";
export declare class ServerIpService implements IServerIpService {
    private accountId;
    private config;
    private time;
    private http;
    private info;
    private preferences;
    private region;
    private serverIp;
    private listeners;
    private cache;
    private network;
    private speedUtil;
    private eventService;
    private interrupterProviderList;
    constructor(accountId: string, config: InitConfig, time: ITime, http: IHttp, preferences: IPreferences, network: INetworkManager, b18: IEventService, c18: ISpeedUtil, info: IInfo);
    private getUpdateServerIps;
    registerServerChangeListener(listener: IServerChangeListener): void;
    private setRegionWithEvent;
    private doSetRegion;
    setRegion(from: ECallFrom, region?: string | Region, force?: boolean): Promise<void>;
    private loadServerIpFromCache;
    private updateServerIpEvery24Hour;
    init(): Promise<void>;
    getServerIpByNetStack(): Promise<string>;
    getServerIp(ipType?: IpType.V4 | IpType.V6): string;
    markAndGetNextServerIp(a17: string, ipType?: IpType.V4 | IpType.V6): string;
    markServerIpStatus(serverIp: string, z16: boolean, ipType?: IpType.V4 | IpType.V6): void;
    addInterrupter(y16: ISsInterrupterProvider): void;
}
