// @keepTs
// @ts-nocheck
/**
 * 单次解析网络接口的下行结构
 */
import { radio } from '@kit.TelephonyKit';
export interface Response {
    host?: string;
    ips?: Array<string>;
    ipsv6?: Array<string>;
    ttl?: number;
    extra?: string;
}
/**
 * 批量解析结果中，代表IPv4，也在HostRecord使用
 */
export declare const TYPE_V4 = 1;
/**
 * 批量解析结果中，代表IPv6，也在HostRecord使用
 */
export declare const TYPE_V6 = 28;
/**
 * IP的类型，主要用于解析下行和内部存储
 */
export type IP_TYPE = 1 | 28;
/**
 * 批量解析结果中，单个域名的结果
 */
export interface BatchItem {
    host?: string;
    ips?: Array<string>;
    ttl?: number;
    type?: IP_TYPE;
}
/**
 * 批量解析 网络下行结构
 */
export interface BatchResponse {
    dns?: Array<BatchItem>;
}
/**
 * 可观测埋点配置
 */
export interface EventConfig {
    enable?: boolean;
    sample_ratio?: number;
    endpoint?: string;
    batch_report_max_size?: number;
    batch_report_interval_time?: number;
    max_reports_per_minute?: number;
}
/**
 * 服务调度接口下行
 */
export interface SsResponse {
    service_status?: 'disable';
    service_ip?: Array<string>;
    service_ipv6?: Array<string>;
    localData?: boolean;
    statistics?: EventConfig;
}
/**
 * 异常时下发的数据
 */
export interface ErrorResponse {
    code: string;
}
/**
 * SDK 内部统一的存储结构
 */
export interface HostRecord {
    host: string;
    ips: Array<string>;
    ipType: IP_TYPE;
    queryTime: number;
    ttl: number;
    cacheKey?: string;
    extra?: string;
    region?: string;
    fromDB?: boolean;
}
/**
 * region默认配置的数据结构
 */
export interface RegionDefaultConfig {
    /**
     * region的标识
     */
    key: REGION_STR;
    /**
     * 默认服务IP，IPv4类型
     */
    server_ips_IPv4: Array<string>;
    /**
     * 默认服务IP，IPv6类型
     */
    server_ips_IPv6: Array<string>;
    /**
     * 兜底的调度服务IP，IPv4类型
     */
    fallback_update_server_ips_IPv4: Array<string>;
    /**
     * 兜底的调度服务IP，IPv6类型
     */
    fallback_update_server_ips_IPv6: Array<string>;
}
export type REGION_STR = "" | "hk" | "sg" | "de" | "us" | "test";
export type ORIGINAL_NETWORK_TYPE = radio.NetworkType | 'WIFI' | 'ETHERNET' | 'VPN' | 'UNKNOWN';
