// @keepTs
// @ts-nocheck
import { IMDUtil } from "../k/c1/Index";
import { ITime } from "../k/l/Index";
import { ISignService, SignResult } from './Index';
export declare class SignService implements ISignService {
    private offset;
    private mdUtil;
    private time;
    private accountId;
    private secretKey?;
    constructor(h22: IMDUtil, time: ITime, accountId: string, i22?: string);
    sign(host: string): Promise<SignResult>;
    signForMetrics(body: string): Promise<SignResult>;
    setCurrentServerTime(b22: number): void;
}
