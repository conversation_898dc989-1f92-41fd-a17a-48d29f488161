// @keepTs
// @ts-nocheck
import { IDB } from "../k/y/Index";
import { IHttp, INetChangeListener, INetworkManager, ISpeedUtil } from "../k/u/Index";
import { IPreferences } from "../k/j1/Index";
import { IServerChangeListener } from "../q1/Index";
import { ITime } from "../k/l/Index";
import { HttpDnsConfig, HttpDnsResult, IHttpDnsService, InitConfig, IpType, Region } from './Index';
import { IMDUtil } from "../k/c1/Index";
import { IInfo } from "../k/a1/Index";
import { IRandom } from "../k/m1/Index";
/**
 * 模块配置，主要方便测试，
 * 正常运行会使用内部实现
 */
interface Options {
    accountId: string;
    http?: IHttp;
    time?: ITime;
    db?: IDB;
    network?: INetworkManager;
    preferences?: IPreferences;
    mdUtil?: IMDUtil;
    speedUtil?: ISpeedUtil;
    info?: IInfo;
    random?: IRandom;
    config?: HttpDnsConfig;
}
export declare function getInitConfig(config?: HttpDnsConfig): InitConfig;
export declare class HttpDnsServiceImpl implements IHttpDnsService, IServerChangeListener, INetChangeListener {
    private config;
    private http;
    private time;
    private repo;
    private resolveService;
    private db;
    private network;
    private preferences;
    private mdUtil;
    private speedUtil;
    private serverIpService;
    private signService;
    private switchService;
    private eventService;
    private logI;
    private lock;
    constructor(options: Options);
    private loadRecordCache;
    init(): Promise<void>;
    onServerChange(s21: boolean, region: string): void;
    onNewNetwork(): void;
    private convertIpType;
    private convertIpTypeSync;
    private resolveWithLock;
    private resolve;
    private syncNonBlockingResolveWithEvent;
    private syncNonBlockingResolve;
    private asyncResolveWithEvent;
    private asyncResolve;
    private doResolveHosts;
    private resolveHostsWithEvent;
    resolveHosts(n19: string[], o19?: IpType): Promise<void>;
    getHttpDnsResultSyncNonBlocking(host: string, type?: IpType): HttpDnsResult;
    getHttpDnsResultAsync(host: string, type?: IpType): Promise<HttpDnsResult>;
    changeRegion(region: Region): Promise<void>;
    changeRegion(region: string): Promise<void>;
    setServerTimeInSecond(a19: number): void;
    private doCleanHostCache;
    private cleanHostCacheWithEvent;
    cleanHostCache(t18?: string[]): Promise<void>;
    getSessionId(): string;
    setSDnsGlobalParams(params: Record<string, string>): void;
    clearSDnsGlobalParams(): void;
    getSDnsResultSyncNonBlocking(host: string, params: Record<string, string>, cacheKey: string, type?: IpType): HttpDnsResult;
    getSDnsResultAsync(host: string, params: Record<string, string>, cacheKey: string, type?: IpType): Promise<HttpDnsResult>;
    cleanEnvAll(): Promise<void>;
}
export {};
