// @keepTs
// @ts-nocheck
import { SdkError } from '@aliyun/error';
import { BusinessError } from '@kit.BasicServicesKit';
import { LogI } from '@aliyun/logger';
/**
 * httpdns SDK的异常
 * 有两个参数 name 错误码， message 错误信息
 */
export declare class HttpDnsError extends SdkError {
    constructor(code: string, msg: string, s2?: string);
}
export declare function convert(e: Error | HttpDnsError): HttpDnsError;
export declare function logErr(o2: LogI, e: Error | HttpDnsError, msg?: string | (() => string)): void;
export declare class SystemError extends HttpDnsError {
    constructor(n2: BusinessError | undefined, code?: string, msg?: string);
}
/**
 * 内部错误，不应该出现，需要修复
 */
export declare class InnerError extends HttpDnsError {
    constructor(msg: string);
}
/**
 * 未知错误，应该定位解决
 */
export declare class UnknownError extends HttpDnsError {
    constructor(msg: string);
}
export declare class DisableError extends HttpDnsError {
    constructor();
}
export declare class SniffError extends HttpDnsError {
    constructor();
}
export declare class SDnsCacheKeyError extends HttpDnsError {
    constructor();
}
export declare class SDnsInvalidKeyError extends HttpDnsError {
    constructor(key: string);
}
export declare class SDnsInvalidValueError extends HttpDnsError {
    constructor(value: string);
}
export declare class SDnsArgsTooLongError extends HttpDnsError {
    constructor();
}
/**
 * 请求返回非200
 */
export declare class HttpCodeError extends HttpDnsError {
    constructor(code: number, msg: string);
}
/**
 * 网络请求异常，包括超时、等等
 */
export declare class HttpRequestError extends HttpDnsError {
    constructor(msg: string);
}
/**
 * httpdns 服务返回的错误信息
 */
export declare class ServerError extends HttpDnsError {
    constructor(msg: string);
}
export declare class DBNotContextError extends HttpDnsError {
    constructor();
}
export declare class GetDBError extends SystemError {
    constructor(e: BusinessError);
}
export declare class LoadDBError extends SystemError {
    constructor(e: BusinessError);
}
export declare class SaveDBError extends SystemError {
    constructor(e: BusinessError);
}
export declare class CleanDBError extends SystemError {
    constructor(e: BusinessError);
}
export declare class GetPreferencesError extends SystemError {
    constructor(e: BusinessError);
}
export declare class ReadPreferencesError extends SystemError {
    constructor(e: BusinessError);
}
export declare class WritePreferencesError extends SystemError {
    constructor(e: BusinessError);
}
