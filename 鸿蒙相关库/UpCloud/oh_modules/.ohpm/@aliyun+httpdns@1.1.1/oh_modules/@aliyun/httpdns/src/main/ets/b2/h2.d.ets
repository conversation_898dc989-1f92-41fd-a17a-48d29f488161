// @keepTs
// @ts-nocheck
import { HttpDnsError } from "../g/h";
import { ITime } from "../k/l/Index";
import { RequestWatcher } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { IServerIpService } from "../q1/Index";
import { ResolveMode } from "./j2";
export declare class ShiftServerWatch<R> extends RequestWatcher<RequestConfig, R> {
    private time;
    private begin;
    private serverIpService;
    private mode;
    constructor(time: ITime, p15: IServerIpService, mode: ResolveMode);
    onStart(config: RequestConfig): RequestConfig;
    onSuccess(config: RequestConfig, result: R): R;
    onFail(config: RequestConfig, error: HttpDnsError): HttpDnsError;
}
