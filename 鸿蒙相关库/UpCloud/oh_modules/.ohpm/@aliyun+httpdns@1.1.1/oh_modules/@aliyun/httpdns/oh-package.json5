{"name": "@aliyun/httpdns", "version": "1.1.1", "description": "get ip of host over http request", "keywords": ["<PERSON><PERSON><PERSON>", "EMAS", "HTTPDNS"], "homepage": "https://help.aliyun.com/document_detail/435220.html", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@aliyun/logger": "1.0.2", "@aliyun/error": "1.0.2"}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "declarationEntry": [], "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true}