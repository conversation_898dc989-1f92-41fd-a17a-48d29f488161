// @keepTs
// @ts-nocheck
import { EventConfig, HostRecord, ORIGINAL_NETWORK_TYPE } from "../g/j";
import { HttpDnsError } from "../g/h";
import { HttpDnsResult, InnerHttpDnsResult } from "../f/Index";
import { ENetworkType, EResultIpType, EStatusCode } from './Index';
export declare function getResultIpTypeByHostRecord(result: HostRecord[] | undefined): EResultIpType;
export declare function getApiResultIpType(result: HttpDnsResult | undefined): EResultIpType;
export declare function convertNetworkType(networkType: ORIGINAL_NETWORK_TYPE): ENetworkType;
export declare function getStatusCodeByErrorHostRecord(error: HttpDnsError | undefined, records: HostRecord[] | undefined): EStatusCode;
export declare function getApiStatusCode(result: InnerHttpDnsResult): EStatusCode;
export declare function getErrorCode(error?: HttpDnsError): string;
export declare function getEventConfig(config?: Partial<EventConfig>): EventConfig;
