// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { ITime } from "../k/l/Index";
import { ResponseTranslator } from "../o1/Index";
import { IpType } from "../f/Index";
export default class ResolveRespTranslator implements ResponseTranslator<Array<HostRecord>> {
    private time;
    private host;
    private type;
    constructor(time: ITime, host: string, type: IpType.V4 | IpType.V6 | IpType.Both);
    parse(response: string): Array<HostRecord>;
}
