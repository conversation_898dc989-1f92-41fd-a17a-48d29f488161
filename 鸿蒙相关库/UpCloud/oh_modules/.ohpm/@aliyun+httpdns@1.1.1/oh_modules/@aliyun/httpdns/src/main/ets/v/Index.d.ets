// @keepTs
// @ts-nocheck
import { IpType, ResultStatus } from "../f/Index";
import { EventConfig } from "../g/j";
import { ISsInterrupterProvider } from "../q1/Index";
/**
 * 埋点事件定义
 */
export declare enum Event {
    PreResolveHostCache = 1,
    BatchQueryHttpDnsApi = 2,
    QueryHttpDnsApi = 3,
    GetResultFromRemote = 4,
    CallSdkApi = 5,
    CleanHostCache = 6,
    UpdateRegionServerIps = 7,
    ReportingRateException = 8
}
/**
 * API类型场景
 */
export declare enum EApiScene {
    Async = 8,
    SyncNonBlocking = 12
}
/**
 * GetResultFromRemote 事件 缓存场景
 */
export declare enum ECacheStatusForRemoteScene {
    NoCache = 0,
    CacheExpired = 16
}
/**
 * CallSdkApi 事件 缓存场景
 */
export declare enum ECacheStatusForCallApi {
    NoCache = 0,
    CacheNotExpired = 16,
    ExpiredAndNotUse = 32,
    ExpiredAndUsed = 48
}
/**
 * 查询的IP类型
 */
export declare enum ERequestIpType {
    auto = 0,
    v4 = 1,
    v6 = 2,
    both = 3
}
/**
 * 网络类型
 */
export declare enum ENetworkType {
    WIFI = 1,
    MOBILE_5G = 2,
    MOBILE_4G = 3,
    MOBILE_3G = 4,
    UNKNOWN = 5,
    OTHERS = 6
}
/**
 * 解析结果中ip的类型情况
 */
export declare enum EResultIpType {
    none = 0,
    v4 = 1,
    v6 = 2,
    both = 3
}
/**
 * 事件 StatusCode
 */
export declare enum EStatusCode {
    SUCCESS = 200,
    NO_CONTENT = 204,
    NETWORK_ISSUE = -1,
    SERVER_ISSUE = -2,
    OTHER_ISSUE = -3
}
export declare enum ERetry {
    No = 0,
    Yes = 16
}
export declare enum EHttpMode {
    Http = 0,
    Https = 4
}
export declare enum ESignMode {
    None = 0,
    Has = 8
}
export declare enum EBatchScene {
    UserCall = 1,
    NetworkChange = 2
}
export declare enum ECleanMode {
    All = 1,
    Some = 2
}
export declare enum ECallFrom {
    RequestFailUpdate = 1,
    UserChangeRegion = 2,
    Init = 3
}
/**
 * Api解析埋点的入参
 */
export interface CallSdkApiEventOptions {
    requestIpType: IpType;
    apiScene: EApiScene;
    cacheStatus: ResultStatus;
    host: string;
    serverIp: string;
    costInMs: number;
    resultIpType: EResultIpType;
    statusCode: EStatusCode | number;
    errorCode: string;
    resultIPv4?: string[];
    resultIPv6?: string[];
}
/**
 * Sdk内部请求解析埋点的入参
 */
export interface RemoteResolveEventOptions {
    requestIpType: IpType;
    apiScene: EApiScene;
    cacheStatus: ResultStatus;
    host: string;
    costInMs: number;
    resultIpType: EResultIpType;
    statusCode: EStatusCode | number;
    errorCode: string;
    resultIPv4?: string[];
    resultIPv6?: string[];
}
/**
 * Sdk内部请求解析埋点的入参
 */
export interface QueryHttpDnsApiEventOptions {
    retry: ERetry;
    requestIpType: IpType;
    httpMode: EHttpMode;
    signMode: ESignMode;
    host: string;
    serverIp: string;
    costInMs: number;
    resultIpType: EResultIpType;
    statusCode: EStatusCode | number;
    errorCode: string;
    resultIPv4?: string[];
    resultIPv6?: string[];
}
/**
 * 调用批量解析事件
 */
export interface PreResolveHostCacheOptions {
    scene: EBatchScene;
    serverIp: string;
    costInMs: number;
    resultIpType: EResultIpType;
    statusCode: EStatusCode | number;
    errorCode: string;
}
export interface CleanHostCacheOptions {
    cleanMode: ECleanMode;
    costInMs: number;
    statusCode: EStatusCode | number;
}
export interface UpdateRegionServerIpsOptions {
    scene: ECallFrom;
    costInMs: number;
    statusCode: EStatusCode | number;
}
/**
 * 埋点服务对外提供的接口
 */
export interface IEventService extends ISsInterrupterProvider {
    /**
     * 启动埋点
     */
    init(): Promise<void>;
    /**
     * 添加API解析事件
     * @param options
     */
    addApiResolveEvent(options: CallSdkApiEventOptions): Promise<void>;
    /**
     * 添加SDK内部请求服务解析事件
     * @param options
     */
    addRemoteResolveEvent(options: RemoteResolveEventOptions): Promise<void>;
    /**
     * 添加单次网络解析事件
     * @param options
     */
    addQueryHttpDnsApiEvent(options: QueryHttpDnsApiEventOptions): Promise<void>;
    /**
     * 添加批量解析API调用事件
     * @param options
     * @returns
     */
    addPreResolveHostCacheEvent(options: PreResolveHostCacheOptions): Promise<void>;
    /**
     * 添加单次批量网络解析事件
     * @param options
     */
    addBatchQueryHttpDnsApiEvent(options: QueryHttpDnsApiEventOptions): Promise<void>;
    /**
     * 添加清除缓存事件
     * @param options
     * @returns
     */
    addCleanHostCacheEvent(options: CleanHostCacheOptions): Promise<void>;
    /**
     * 添加更新服务ip事件
     * @param options
     * @returns
     */
    addUpdateRegionServerIpsEvent(options: UpdateRegionServerIpsOptions): Promise<void>;
    /**
     * 更新服务配置
     * @param config
     */
    updateServerConfig(region: string, config: EventConfig): void;
}
/**
 * 内部存储的埋点记录
 */
export interface EventRecord {
    eventName: Event;
    tag: number;
    hostName: string;
    timestamp: number;
    serverIp: string;
    costTime: number;
    networkType: ENetworkType;
    ipType: EResultIpType | '';
    statusCode: EStatusCode | number;
    errorCode: string;
    count: number;
    httpDnsIps: string;
    localDnsIps: string;
}
