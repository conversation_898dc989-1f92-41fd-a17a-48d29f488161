// @keepTs
// @ts-nocheck
import { IpType } from "../../f/Index";
import { ORIGINAL_NETWORK_TYPE } from "../../g/j";
export interface HttpOptions {
    timeoutInMs: number;
}
/**
 * 外部http实现接口
 */
export interface IHttp {
    get(url: string, headers?: Map<string, string>, options?: HttpOptions): Promise<string>;
    post(url: string, body: string, headers: Map<string, string>): Promise<void>;
}
export interface INetChangeListener {
    onNewNetwork(): void;
}
export interface INetworkManager {
    getNetType(): Promise<string>;
    getOriginalNetType(): Promise<ORIGINAL_NETWORK_TYPE>;
    getNetStack(): Promise<IpType.V4 | IpType.V6 | IpType.Both>;
    getNetStackCache(): IpType.V4 | IpType.V6 | IpType.Both;
    addChangeListener(listener: INetChangeListener): void;
}
export interface ISpeedUtil {
    connectTest(ip: string, port: number): Promise<number>;
}
