// @keepTs
// @ts-nocheck
import { ValueType } from '@kit.ArkData';
import { IpType } from "../f/Index";
import { IRequestConfig } from './Index';
interface RequestOptions {
    schema?: 'http' | 'https';
    ipType: IpType.V4 | IpType.V6;
    serverIpOrHost: string;
    path?: string;
    query?: Record<string, ValueType>;
    timeoutInMs?: number;
}
export declare class RequestConfig implements IRequestConfig {
    private schema;
    private serverIpType;
    private serverIpOrHost;
    private path;
    private query;
    private _timeoutInMs;
    retryIndex?: number | undefined;
    private extra;
    constructor(options: RequestOptions);
    getExtra(key: string): ValueType | undefined;
    setExtra(key: string, value: ValueType | undefined): void;
    private get host();
    private get queryStr();
    getQuery(): Record<string, ValueType>;
    getSchema(): 'http' | 'https';
    getPath(): string;
    serverIp(): string;
    needIpType(): IpType.V4 | IpType.V6;
    updateServerIp(x12: string): void;
    url(): string;
    timeoutInMs(): number;
}
export {};
