// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { ISpeedUtil } from "../k/u/Index";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { HostConfigItem } from "../f/Index";
export default class IPRankInterrupter extends RequestInterrupter<RequestConfig, Array<HostRecord>> {
    private speedUtil;
    private ipRankItems;
    constructor(v13: ISpeedUtil, w13?: Array<HostConfigItem>);
    test(record: HostRecord): Promise<void>;
    run(o13: RequestChain<RequestConfig, HostRecord[]>, config: RequestConfig): Promise<HostRecord[]>;
}
