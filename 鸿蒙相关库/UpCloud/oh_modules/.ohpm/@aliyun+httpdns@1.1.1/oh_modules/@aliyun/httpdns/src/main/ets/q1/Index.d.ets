// @keepTs
// @ts-nocheck
import { SsResponse } from "../g/j";
import { ECallFrom } from "../v/Index";
import { RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { IpType, Region } from "../f/Index";
export declare const KEY_EXTRA_REGION = "region";
export interface IServerChangeListener {
    onServerChange(regionChanged: boolean, region: string): void;
}
export interface ISsInterrupterProvider {
    getInterrupter(): RequestInterrupter<RequestConfig, SsResponse>;
}
/**
 * 服务IP 管理调度服务
 */
export interface IServerIpService {
    /**
     * 初始化，涉及从缓存中读取相关信息、定时更新
     * @returns
     */
    init(): Promise<void>;
    /**
     * 根据网络栈 获取当前的服务IP
     * @returns
     */
    getServerIpByNetStack(): Promise<string>;
    /**
     * 获取当前的服务IP
     * @returns
     */
    getServerIp(ipType?: IpType.V4 | IpType.V6): string;
    /**
     * 获取下一个可用的服务IP
     * 这里表示当前的服务IP不可用，需要切换下一个
     * @returns
     */
    markAndGetNextServerIp(currentServerIp: string, ipType?: IpType.V4 | IpType.V6): string;
    /**
     * 标记服务IP状态，当有些情况下服务IP恢复正常可用时调用
     * @param serverIp
     * @param isOk
     */
    markServerIpStatus(serverIp: string, isOk: boolean, ipType?: IpType.V4 | IpType.V6): void;
    /**
     * 更新region
     * @param region
     * @returns
     */
    setRegion(from: ECallFrom, region?: string | Region, force?: boolean): Promise<void>;
    /**
     * 修改服务IP成功后的事件回调
     * @param listener
     */
    registerServerChangeListener(listener: IServerChangeListener): void;
    /**
     * 有些服务是通过调度接口传递数据的，提供拦截器注入的接口
     * @param provider
     */
    addInterrupter(provider: ISsInterrupterProvider): void;
}
