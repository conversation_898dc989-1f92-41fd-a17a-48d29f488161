// @keepTs
// @ts-nocheck
import hilog from '@ohos.hilog';
export interface ILogger {
    log(level: hilog.LogLevel, msg: string): void;
}
/**
 * 开启HttpDns内部的HiLog输出
 */
export declare function enableHiLog(): void;
/**
 * 添加日志接口，获取HttpDns日志
 * @param logger
 */
export declare function addLogger(q4: ILogger): void;
/**
 * 移除日志接口
 * @param logger
 */
export declare function removeLogger(o4: ILogger): void;
