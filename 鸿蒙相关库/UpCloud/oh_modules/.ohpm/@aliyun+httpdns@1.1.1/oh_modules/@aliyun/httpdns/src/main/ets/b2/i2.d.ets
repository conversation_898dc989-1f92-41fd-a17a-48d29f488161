// @keepTs
// @ts-nocheck
import { HostRecord } from "../g/j";
import { RequestChain, RequestInterrupter } from "../o1/Index";
import { RequestConfig } from "../o1/p1";
import { ICacheTtlCustomizer } from "../f/Index";
export default class TtlInterrupter extends RequestInterrupter<RequestConfig, Array<HostRecord>> {
    private ttlChanger?;
    constructor(s15?: ICacheTtlCustomizer);
    run(q15: RequestChain<RequestConfig, HostRecord[]>, config: RequestConfig): Promise<HostRecord[]>;
}
