// @keepTs
// @ts-nocheck
import { ITime } from "../k/l/Index";
import { EventQueue } from "./u1";
import { EventReporter } from "./w1";
export declare class EventWorker {
    private sizeThreshold;
    private timeThreshold;
    private loopDelay;
    private queue;
    private reporter;
    private time;
    private slidingTimeWindow;
    private runCommand;
    private isRunning;
    private safelyStopCalled;
    private lastReportTime;
    private logI;
    constructor(u22: EventQueue, v22: EventReporter, time: ITime);
    private waitForNextLoop;
    private startDoWork;
    start(): void;
    safelyStop(): void;
    stop(): void;
    updateBatchReportMaxSize(s11?: number, t11?: number, u11?: number): void;
}
