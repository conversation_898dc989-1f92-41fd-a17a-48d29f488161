// @keepTs
// @ts-nocheck
import socket from '@ohos.net.socket';
import { AsyncCallback, BusinessError, Callback } from '@kit.BasicServicesKit';
import { HttpOptions } from './Index';
export declare class HttpsRequest {
    private port;
    private ALPNProtocols;
    private callback;
    private isClose;
    private socket;
    private secureOptions;
    private url;
    private host;
    private pathname;
    private search;
    private headers?;
    private options?;
    constructor(url: string, headers?: Map<string, string>, options?: HttpOptions);
    /**
     * Initiates an HTTP request to a given URL.
     *
     * @param url URL for initiating an HTTP request.
     * @param callback Returns {@link HttpResponse}.
     * @permission ohos.permission.INTERNET
     */
    request(callback: AsyncCallback<HttpResponse | undefined>): void;
    /**
     * Destroys an HTTP request.
     */
    destroy(): void;
    on(type: string, callback: Callback<BusinessError | socket.SocketMessageInfo | null>): void;
    off(type: string, callback: Callback<BusinessError | socket.SocketMessageInfo | null>): void;
    private dealSuccessResult;
    private dealFailResult;
    private bind;
    private connect;
    private send;
    private sendBody;
}
export declare class HttpResponse {
    result?: string;
    responseCode?: number;
}
