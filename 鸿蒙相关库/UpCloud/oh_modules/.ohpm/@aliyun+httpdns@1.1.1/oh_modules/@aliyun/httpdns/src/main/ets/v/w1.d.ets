// @keepTs
// @ts-nocheck
import { IInfo } from "../k/a1/Index";
import { IHttp } from "../k/u/Index";
import { EventRecord } from './Index';
import { ISignService } from "../v1/Index";
import { InitConfig } from "../f/Index";
/**
 * 上报埋点
 */
export declare class EventReporter {
    private account;
    private config;
    private http;
    private info;
    private signService;
    private baseUrl;
    private requestIndex;
    private logI;
    constructor(account: string, config: InitConfig, http: IHttp, info: IInfo, t22: ISignService);
    private recordToString;
    private createRequestId;
    updateBaseUrl(baseUrl: string): void;
    report(records: Array<EventRecord>): Promise<void>;
}
