{"version": 3, "file": "transform-fn-params.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/metadata/transform-fn-params.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TransformationType } from '../../enums';\nimport { ClassTransformOptions } from '../class-transformer-options.interface';\n\nexport interface TransformFnParams {\n  value: any;\n  key: string;\n  obj: any;\n  type: TransformationType;\n  options: ClassTransformOptions;\n}\n"]}