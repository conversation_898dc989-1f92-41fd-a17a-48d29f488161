{"version": 3, "file": "class-transformer.umd.min.js", "sources": ["../esm5/enums/transformation-type.enum.js", "../esm5/MetadataStorage.js", "../esm5/storage.js", "../esm5/TransformOperationExecutor.js", "../esm5/utils/is-promise.util.js", "../esm5/utils/get-global.util.js", "../esm5/constants/default-options.constant.js", "../esm5/ClassTransformer.js", "../esm5/index.js", "../esm5/decorators/exclude.decorator.js", "../esm5/decorators/expose.decorator.js", "../esm5/decorators/transform.decorator.js", "../esm5/decorators/transform-instance-to-instance.decorator.js", "../esm5/decorators/transform-instance-to-plain.decorator.js", "../esm5/decorators/transform-plain-to-instance.decorator.js", "../esm5/decorators/type.decorator.js"], "sourcesContent": ["export var TransformationType;\n(function (TransformationType) {\n    TransformationType[TransformationType[\"PLAIN_TO_CLASS\"] = 0] = \"PLAIN_TO_CLASS\";\n    TransformationType[TransformationType[\"CLASS_TO_PLAIN\"] = 1] = \"CLASS_TO_PLAIN\";\n    TransformationType[TransformationType[\"CLASS_TO_CLASS\"] = 2] = \"CLASS_TO_CLASS\";\n})(TransformationType || (TransformationType = {}));\n//# sourceMappingURL=transformation-type.enum.js.map", "import { TransformationType } from './enums';\n/**\n * Storage all library metadata.\n */\nvar MetadataStorage = /** @class */ (function () {\n    function MetadataStorage() {\n        // -------------------------------------------------------------------------\n        // Properties\n        // -------------------------------------------------------------------------\n        this._typeMetadatas = new Map();\n        this._transformMetadatas = new Map();\n        this._exposeMetadatas = new Map();\n        this._excludeMetadatas = new Map();\n        this._ancestorsMap = new Map();\n    }\n    // -------------------------------------------------------------------------\n    // Adder Methods\n    // -------------------------------------------------------------------------\n    MetadataStorage.prototype.addTypeMetadata = function (metadata) {\n        if (!this._typeMetadatas.has(metadata.target)) {\n            this._typeMetadatas.set(metadata.target, new Map());\n        }\n        this._typeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n    };\n    MetadataStorage.prototype.addTransformMetadata = function (metadata) {\n        if (!this._transformMetadatas.has(metadata.target)) {\n            this._transformMetadatas.set(metadata.target, new Map());\n        }\n        if (!this._transformMetadatas.get(metadata.target).has(metadata.propertyName)) {\n            this._transformMetadatas.get(metadata.target).set(metadata.propertyName, []);\n        }\n        this._transformMetadatas.get(metadata.target).get(metadata.propertyName).push(metadata);\n    };\n    MetadataStorage.prototype.addExposeMetadata = function (metadata) {\n        if (!this._exposeMetadatas.has(metadata.target)) {\n            this._exposeMetadatas.set(metadata.target, new Map());\n        }\n        this._exposeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n    };\n    MetadataStorage.prototype.addExcludeMetadata = function (metadata) {\n        if (!this._excludeMetadatas.has(metadata.target)) {\n            this._excludeMetadatas.set(metadata.target, new Map());\n        }\n        this._excludeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n    };\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n    MetadataStorage.prototype.findTransformMetadatas = function (target, propertyName, transformationType) {\n        return this.findMetadatas(this._transformMetadatas, target, propertyName).filter(function (metadata) {\n            if (!metadata.options)\n                return true;\n            if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true)\n                return true;\n            if (metadata.options.toClassOnly === true) {\n                return (transformationType === TransformationType.CLASS_TO_CLASS ||\n                    transformationType === TransformationType.PLAIN_TO_CLASS);\n            }\n            if (metadata.options.toPlainOnly === true) {\n                return transformationType === TransformationType.CLASS_TO_PLAIN;\n            }\n            return true;\n        });\n    };\n    MetadataStorage.prototype.findExcludeMetadata = function (target, propertyName) {\n        return this.findMetadata(this._excludeMetadatas, target, propertyName);\n    };\n    MetadataStorage.prototype.findExposeMetadata = function (target, propertyName) {\n        return this.findMetadata(this._exposeMetadatas, target, propertyName);\n    };\n    MetadataStorage.prototype.findExposeMetadataByCustomName = function (target, name) {\n        return this.getExposedMetadatas(target).find(function (metadata) {\n            return metadata.options && metadata.options.name === name;\n        });\n    };\n    MetadataStorage.prototype.findTypeMetadata = function (target, propertyName) {\n        return this.findMetadata(this._typeMetadatas, target, propertyName);\n    };\n    MetadataStorage.prototype.getStrategy = function (target) {\n        var excludeMap = this._excludeMetadatas.get(target);\n        var exclude = excludeMap && excludeMap.get(undefined);\n        var exposeMap = this._exposeMetadatas.get(target);\n        var expose = exposeMap && exposeMap.get(undefined);\n        if ((exclude && expose) || (!exclude && !expose))\n            return 'none';\n        return exclude ? 'excludeAll' : 'exposeAll';\n    };\n    MetadataStorage.prototype.getExposedMetadatas = function (target) {\n        return this.getMetadata(this._exposeMetadatas, target);\n    };\n    MetadataStorage.prototype.getExcludedMetadatas = function (target) {\n        return this.getMetadata(this._excludeMetadatas, target);\n    };\n    MetadataStorage.prototype.getExposedProperties = function (target, transformationType) {\n        return this.getExposedMetadatas(target)\n            .filter(function (metadata) {\n            if (!metadata.options)\n                return true;\n            if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true)\n                return true;\n            if (metadata.options.toClassOnly === true) {\n                return (transformationType === TransformationType.CLASS_TO_CLASS ||\n                    transformationType === TransformationType.PLAIN_TO_CLASS);\n            }\n            if (metadata.options.toPlainOnly === true) {\n                return transformationType === TransformationType.CLASS_TO_PLAIN;\n            }\n            return true;\n        })\n            .map(function (metadata) { return metadata.propertyName; });\n    };\n    MetadataStorage.prototype.getExcludedProperties = function (target, transformationType) {\n        return this.getExcludedMetadatas(target)\n            .filter(function (metadata) {\n            if (!metadata.options)\n                return true;\n            if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true)\n                return true;\n            if (metadata.options.toClassOnly === true) {\n                return (transformationType === TransformationType.CLASS_TO_CLASS ||\n                    transformationType === TransformationType.PLAIN_TO_CLASS);\n            }\n            if (metadata.options.toPlainOnly === true) {\n                return transformationType === TransformationType.CLASS_TO_PLAIN;\n            }\n            return true;\n        })\n            .map(function (metadata) { return metadata.propertyName; });\n    };\n    MetadataStorage.prototype.clear = function () {\n        this._typeMetadatas.clear();\n        this._exposeMetadatas.clear();\n        this._excludeMetadatas.clear();\n        this._ancestorsMap.clear();\n    };\n    // -------------------------------------------------------------------------\n    // Private Methods\n    // -------------------------------------------------------------------------\n    MetadataStorage.prototype.getMetadata = function (metadatas, target) {\n        var metadataFromTargetMap = metadatas.get(target);\n        var metadataFromTarget;\n        if (metadataFromTargetMap) {\n            metadataFromTarget = Array.from(metadataFromTargetMap.values()).filter(function (meta) { return meta.propertyName !== undefined; });\n        }\n        var metadataFromAncestors = [];\n        for (var _i = 0, _a = this.getAncestors(target); _i < _a.length; _i++) {\n            var ancestor = _a[_i];\n            var ancestorMetadataMap = metadatas.get(ancestor);\n            if (ancestorMetadataMap) {\n                var metadataFromAncestor = Array.from(ancestorMetadataMap.values()).filter(function (meta) { return meta.propertyName !== undefined; });\n                metadataFromAncestors.push.apply(metadataFromAncestors, metadataFromAncestor);\n            }\n        }\n        return metadataFromAncestors.concat(metadataFromTarget || []);\n    };\n    MetadataStorage.prototype.findMetadata = function (metadatas, target, propertyName) {\n        var metadataFromTargetMap = metadatas.get(target);\n        if (metadataFromTargetMap) {\n            var metadataFromTarget = metadataFromTargetMap.get(propertyName);\n            if (metadataFromTarget) {\n                return metadataFromTarget;\n            }\n        }\n        for (var _i = 0, _a = this.getAncestors(target); _i < _a.length; _i++) {\n            var ancestor = _a[_i];\n            var ancestorMetadataMap = metadatas.get(ancestor);\n            if (ancestorMetadataMap) {\n                var ancestorResult = ancestorMetadataMap.get(propertyName);\n                if (ancestorResult) {\n                    return ancestorResult;\n                }\n            }\n        }\n        return undefined;\n    };\n    MetadataStorage.prototype.findMetadatas = function (metadatas, target, propertyName) {\n        var metadataFromTargetMap = metadatas.get(target);\n        var metadataFromTarget;\n        if (metadataFromTargetMap) {\n            metadataFromTarget = metadataFromTargetMap.get(propertyName);\n        }\n        var metadataFromAncestorsTarget = [];\n        for (var _i = 0, _a = this.getAncestors(target); _i < _a.length; _i++) {\n            var ancestor = _a[_i];\n            var ancestorMetadataMap = metadatas.get(ancestor);\n            if (ancestorMetadataMap) {\n                if (ancestorMetadataMap.has(propertyName)) {\n                    metadataFromAncestorsTarget.push.apply(metadataFromAncestorsTarget, ancestorMetadataMap.get(propertyName));\n                }\n            }\n        }\n        return metadataFromAncestorsTarget\n            .slice()\n            .reverse()\n            .concat((metadataFromTarget || []).slice().reverse());\n    };\n    MetadataStorage.prototype.getAncestors = function (target) {\n        if (!target)\n            return [];\n        if (!this._ancestorsMap.has(target)) {\n            var ancestors = [];\n            for (var baseClass = Object.getPrototypeOf(target.prototype.constructor); typeof baseClass.prototype !== 'undefined'; baseClass = Object.getPrototypeOf(baseClass.prototype.constructor)) {\n                ancestors.push(baseClass);\n            }\n            this._ancestorsMap.set(target, ancestors);\n        }\n        return this._ancestorsMap.get(target);\n    };\n    return MetadataStorage;\n}());\nexport { MetadataStorage };\n//# sourceMappingURL=MetadataStorage.js.map", "import { MetadataStorage } from './MetadataStorage';\n/**\n * Default metadata storage is used as singleton and can be used to storage all metadatas.\n */\nexport var defaultMetadataStorage = new MetadataStorage();\n//# sourceMappingURL=storage.js.map", "var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { defaultMetadataStorage } from './storage';\nimport { TransformationType } from './enums';\nimport { getGlobal, isPromise } from './utils';\nfunction instantiateArrayType(arrayType) {\n    var array = new arrayType();\n    if (!(array instanceof Set) && !('push' in array)) {\n        return [];\n    }\n    return array;\n}\nvar TransformOperationExecutor = /** @class */ (function () {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n    function TransformOperationExecutor(transformationType, options) {\n        this.transformationType = transformationType;\n        this.options = options;\n        // -------------------------------------------------------------------------\n        // Private Properties\n        // -------------------------------------------------------------------------\n        this.recursionStack = new Set();\n    }\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n    TransformOperationExecutor.prototype.transform = function (source, value, targetType, arrayType, isMap, level) {\n        var _this = this;\n        if (level === void 0) { level = 0; }\n        if (Array.isArray(value) || value instanceof Set) {\n            var newValue_1 = arrayType && this.transformationType === TransformationType.PLAIN_TO_CLASS\n                ? instantiateArrayType(arrayType)\n                : [];\n            value.forEach(function (subValue, index) {\n                var subSource = source ? source[index] : undefined;\n                if (!_this.options.enableCircularCheck || !_this.isCircular(subValue)) {\n                    var realTargetType = void 0;\n                    if (typeof targetType !== 'function' &&\n                        targetType &&\n                        targetType.options &&\n                        targetType.options.discriminator &&\n                        targetType.options.discriminator.property &&\n                        targetType.options.discriminator.subTypes) {\n                        if (_this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                            realTargetType = targetType.options.discriminator.subTypes.find(function (subType) {\n                                return subType.name === subValue[targetType.options.discriminator.property];\n                            });\n                            var options = { newObject: newValue_1, object: subValue, property: undefined };\n                            var newType = targetType.typeFunction(options);\n                            realTargetType === undefined ? (realTargetType = newType) : (realTargetType = realTargetType.value);\n                            if (!targetType.options.keepDiscriminatorProperty)\n                                delete subValue[targetType.options.discriminator.property];\n                        }\n                        if (_this.transformationType === TransformationType.CLASS_TO_CLASS) {\n                            realTargetType = subValue.constructor;\n                        }\n                        if (_this.transformationType === TransformationType.CLASS_TO_PLAIN) {\n                            subValue[targetType.options.discriminator.property] = targetType.options.discriminator.subTypes.find(function (subType) { return subType.value === subValue.constructor; }).name;\n                        }\n                    }\n                    else {\n                        realTargetType = targetType;\n                    }\n                    var value_1 = _this.transform(subSource, subValue, realTargetType, undefined, subValue instanceof Map, level + 1);\n                    if (newValue_1 instanceof Set) {\n                        newValue_1.add(value_1);\n                    }\n                    else {\n                        newValue_1.push(value_1);\n                    }\n                }\n                else if (_this.transformationType === TransformationType.CLASS_TO_CLASS) {\n                    if (newValue_1 instanceof Set) {\n                        newValue_1.add(subValue);\n                    }\n                    else {\n                        newValue_1.push(subValue);\n                    }\n                }\n            });\n            return newValue_1;\n        }\n        else if (targetType === String && !isMap) {\n            if (value === null || value === undefined)\n                return value;\n            return String(value);\n        }\n        else if (targetType === Number && !isMap) {\n            if (value === null || value === undefined)\n                return value;\n            return Number(value);\n        }\n        else if (targetType === Boolean && !isMap) {\n            if (value === null || value === undefined)\n                return value;\n            return Boolean(value);\n        }\n        else if ((targetType === Date || value instanceof Date) && !isMap) {\n            if (value instanceof Date) {\n                return new Date(value.valueOf());\n            }\n            if (value === null || value === undefined)\n                return value;\n            return new Date(value);\n        }\n        else if (!!getGlobal().Buffer && (targetType === Buffer || value instanceof Buffer) && !isMap) {\n            if (value === null || value === undefined)\n                return value;\n            return Buffer.from(value);\n        }\n        else if (isPromise(value) && !isMap) {\n            return new Promise(function (resolve, reject) {\n                value.then(function (data) { return resolve(_this.transform(undefined, data, targetType, undefined, undefined, level + 1)); }, reject);\n            });\n        }\n        else if (!isMap && value !== null && typeof value === 'object' && typeof value.then === 'function') {\n            // Note: We should not enter this, as promise has been handled above\n            // This option simply returns the Promise preventing a JS error from happening and should be an inaccessible path.\n            return value; // skip promise transformation\n        }\n        else if (typeof value === 'object' && value !== null) {\n            // try to guess the type\n            if (!targetType && value.constructor !== Object /* && TransformationType === TransformationType.CLASS_TO_PLAIN*/)\n                if (!Array.isArray(value) && value.constructor === Array) {\n                    // Somebody attempts to convert special Array like object to Array, eg:\n                    // const evilObject = { '100000000': '100000000', __proto__: [] };\n                    // This could be used to cause Denial-of-service attack so we don't allow it.\n                    // See prevent-array-bomb.spec.ts for more details.\n                }\n                else {\n                    // We are good we can use the built-in constructor\n                    targetType = value.constructor;\n                }\n            if (!targetType && source)\n                targetType = source.constructor;\n            if (this.options.enableCircularCheck) {\n                // add transformed type to prevent circular references\n                this.recursionStack.add(value);\n            }\n            var keys = this.getKeys(targetType, value, isMap);\n            var newValue = source ? source : {};\n            if (!source &&\n                (this.transformationType === TransformationType.PLAIN_TO_CLASS ||\n                    this.transformationType === TransformationType.CLASS_TO_CLASS)) {\n                if (isMap) {\n                    newValue = new Map();\n                }\n                else if (targetType) {\n                    newValue = new targetType();\n                }\n                else {\n                    newValue = {};\n                }\n            }\n            var _loop_1 = function (key) {\n                if (key === '__proto__' || key === 'constructor') {\n                    return \"continue\";\n                }\n                var valueKey = key;\n                var newValueKey = key, propertyName = key;\n                if (!this_1.options.ignoreDecorators && targetType) {\n                    if (this_1.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                        var exposeMetadata = defaultMetadataStorage.findExposeMetadataByCustomName(targetType, key);\n                        if (exposeMetadata) {\n                            propertyName = exposeMetadata.propertyName;\n                            newValueKey = exposeMetadata.propertyName;\n                        }\n                    }\n                    else if (this_1.transformationType === TransformationType.CLASS_TO_PLAIN ||\n                        this_1.transformationType === TransformationType.CLASS_TO_CLASS) {\n                        var exposeMetadata = defaultMetadataStorage.findExposeMetadata(targetType, key);\n                        if (exposeMetadata && exposeMetadata.options && exposeMetadata.options.name) {\n                            newValueKey = exposeMetadata.options.name;\n                        }\n                    }\n                }\n                // get a subvalue\n                var subValue = undefined;\n                if (this_1.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                    /**\n                     * This section is added for the following report:\n                     * https://github.com/typestack/class-transformer/issues/596\n                     *\n                     * We should not call functions or constructors when transforming to class.\n                     */\n                    subValue = value[valueKey];\n                }\n                else {\n                    if (value instanceof Map) {\n                        subValue = value.get(valueKey);\n                    }\n                    else if (value[valueKey] instanceof Function) {\n                        subValue = value[valueKey]();\n                    }\n                    else {\n                        subValue = value[valueKey];\n                    }\n                }\n                // determine a type\n                var type = undefined, isSubValueMap = subValue instanceof Map;\n                if (targetType && isMap) {\n                    type = targetType;\n                }\n                else if (targetType) {\n                    var metadata_1 = defaultMetadataStorage.findTypeMetadata(targetType, propertyName);\n                    if (metadata_1) {\n                        var options = { newObject: newValue, object: value, property: propertyName };\n                        var newType = metadata_1.typeFunction ? metadata_1.typeFunction(options) : metadata_1.reflectedType;\n                        if (metadata_1.options &&\n                            metadata_1.options.discriminator &&\n                            metadata_1.options.discriminator.property &&\n                            metadata_1.options.discriminator.subTypes) {\n                            if (!(value[valueKey] instanceof Array)) {\n                                if (this_1.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                                    type = metadata_1.options.discriminator.subTypes.find(function (subType) {\n                                        if (subValue && subValue instanceof Object && metadata_1.options.discriminator.property in subValue) {\n                                            return subType.name === subValue[metadata_1.options.discriminator.property];\n                                        }\n                                    });\n                                    type === undefined ? (type = newType) : (type = type.value);\n                                    if (!metadata_1.options.keepDiscriminatorProperty) {\n                                        if (subValue && subValue instanceof Object && metadata_1.options.discriminator.property in subValue) {\n                                            delete subValue[metadata_1.options.discriminator.property];\n                                        }\n                                    }\n                                }\n                                if (this_1.transformationType === TransformationType.CLASS_TO_CLASS) {\n                                    type = subValue.constructor;\n                                }\n                                if (this_1.transformationType === TransformationType.CLASS_TO_PLAIN) {\n                                    if (subValue) {\n                                        subValue[metadata_1.options.discriminator.property] = metadata_1.options.discriminator.subTypes.find(function (subType) { return subType.value === subValue.constructor; }).name;\n                                    }\n                                }\n                            }\n                            else {\n                                type = metadata_1;\n                            }\n                        }\n                        else {\n                            type = newType;\n                        }\n                        isSubValueMap = isSubValueMap || metadata_1.reflectedType === Map;\n                    }\n                    else if (this_1.options.targetMaps) {\n                        // try to find a type in target maps\n                        this_1.options.targetMaps\n                            .filter(function (map) { return map.target === targetType && !!map.properties[propertyName]; })\n                            .forEach(function (map) { return (type = map.properties[propertyName]); });\n                    }\n                    else if (this_1.options.enableImplicitConversion &&\n                        this_1.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                        // if we have no registererd type via the @Type() decorator then we check if we have any\n                        // type declarations in reflect-metadata (type declaration is emited only if some decorator is added to the property.)\n                        var reflectedType = Reflect.getMetadata('design:type', targetType.prototype, propertyName);\n                        if (reflectedType) {\n                            type = reflectedType;\n                        }\n                    }\n                }\n                // if value is an array try to get its custom array type\n                var arrayType_1 = Array.isArray(value[valueKey])\n                    ? this_1.getReflectedType(targetType, propertyName)\n                    : undefined;\n                // const subValueKey = TransformationType === TransformationType.PLAIN_TO_CLASS && newKeyName ? newKeyName : key;\n                var subSource = source ? source[valueKey] : undefined;\n                // if its deserialization then type if required\n                // if we uncomment this types like string[] will not work\n                // if (this.transformationType === TransformationType.PLAIN_TO_CLASS && !type && subValue instanceof Object && !(subValue instanceof Date))\n                //     throw new Error(`Cannot determine type for ${(targetType as any).name }.${propertyName}, did you forget to specify a @Type?`);\n                // if newValue is a source object that has method that match newKeyName then skip it\n                if (newValue.constructor.prototype) {\n                    var descriptor = Object.getOwnPropertyDescriptor(newValue.constructor.prototype, newValueKey);\n                    if ((this_1.transformationType === TransformationType.PLAIN_TO_CLASS ||\n                        this_1.transformationType === TransformationType.CLASS_TO_CLASS) &&\n                        // eslint-disable-next-line @typescript-eslint/unbound-method\n                        ((descriptor && !descriptor.set) || newValue[newValueKey] instanceof Function))\n                        return \"continue\";\n                }\n                if (!this_1.options.enableCircularCheck || !this_1.isCircular(subValue)) {\n                    var transformKey = this_1.transformationType === TransformationType.PLAIN_TO_CLASS ? newValueKey : key;\n                    var finalValue = void 0;\n                    if (this_1.transformationType === TransformationType.CLASS_TO_PLAIN) {\n                        // Get original value\n                        finalValue = value[transformKey];\n                        // Apply custom transformation\n                        finalValue = this_1.applyCustomTransformations(finalValue, targetType, transformKey, value, this_1.transformationType);\n                        // If nothing change, it means no custom transformation was applied, so use the subValue.\n                        finalValue = value[transformKey] === finalValue ? subValue : finalValue;\n                        // Apply the default transformation\n                        finalValue = this_1.transform(subSource, finalValue, type, arrayType_1, isSubValueMap, level + 1);\n                    }\n                    else {\n                        if (subValue === undefined && this_1.options.exposeDefaultValues) {\n                            // Set default value if nothing provided\n                            finalValue = newValue[newValueKey];\n                        }\n                        else {\n                            finalValue = this_1.transform(subSource, subValue, type, arrayType_1, isSubValueMap, level + 1);\n                            finalValue = this_1.applyCustomTransformations(finalValue, targetType, transformKey, value, this_1.transformationType);\n                        }\n                    }\n                    if (finalValue !== undefined || this_1.options.exposeUnsetFields) {\n                        if (newValue instanceof Map) {\n                            newValue.set(newValueKey, finalValue);\n                        }\n                        else {\n                            newValue[newValueKey] = finalValue;\n                        }\n                    }\n                }\n                else if (this_1.transformationType === TransformationType.CLASS_TO_CLASS) {\n                    var finalValue = subValue;\n                    finalValue = this_1.applyCustomTransformations(finalValue, targetType, key, value, this_1.transformationType);\n                    if (finalValue !== undefined || this_1.options.exposeUnsetFields) {\n                        if (newValue instanceof Map) {\n                            newValue.set(newValueKey, finalValue);\n                        }\n                        else {\n                            newValue[newValueKey] = finalValue;\n                        }\n                    }\n                }\n            };\n            var this_1 = this;\n            // traverse over keys\n            for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\n                var key = keys_1[_i];\n                _loop_1(key);\n            }\n            if (this.options.enableCircularCheck) {\n                this.recursionStack.delete(value);\n            }\n            return newValue;\n        }\n        else {\n            return value;\n        }\n    };\n    TransformOperationExecutor.prototype.applyCustomTransformations = function (value, target, key, obj, transformationType) {\n        var _this = this;\n        var metadatas = defaultMetadataStorage.findTransformMetadatas(target, key, this.transformationType);\n        // apply versioning options\n        if (this.options.version !== undefined) {\n            metadatas = metadatas.filter(function (metadata) {\n                if (!metadata.options)\n                    return true;\n                return _this.checkVersion(metadata.options.since, metadata.options.until);\n            });\n        }\n        // apply grouping options\n        if (this.options.groups && this.options.groups.length) {\n            metadatas = metadatas.filter(function (metadata) {\n                if (!metadata.options)\n                    return true;\n                return _this.checkGroups(metadata.options.groups);\n            });\n        }\n        else {\n            metadatas = metadatas.filter(function (metadata) {\n                return !metadata.options || !metadata.options.groups || !metadata.options.groups.length;\n            });\n        }\n        metadatas.forEach(function (metadata) {\n            value = metadata.transformFn({ value: value, key: key, obj: obj, type: transformationType, options: _this.options });\n        });\n        return value;\n    };\n    // preventing circular references\n    TransformOperationExecutor.prototype.isCircular = function (object) {\n        return this.recursionStack.has(object);\n    };\n    TransformOperationExecutor.prototype.getReflectedType = function (target, propertyName) {\n        if (!target)\n            return undefined;\n        var meta = defaultMetadataStorage.findTypeMetadata(target, propertyName);\n        return meta ? meta.reflectedType : undefined;\n    };\n    TransformOperationExecutor.prototype.getKeys = function (target, object, isMap) {\n        var _this = this;\n        // determine exclusion strategy\n        var strategy = defaultMetadataStorage.getStrategy(target);\n        if (strategy === 'none')\n            strategy = this.options.strategy || 'exposeAll'; // exposeAll is default strategy\n        // get all keys that need to expose\n        var keys = [];\n        if (strategy === 'exposeAll' || isMap) {\n            if (object instanceof Map) {\n                keys = Array.from(object.keys());\n            }\n            else {\n                keys = Object.keys(object);\n            }\n        }\n        if (isMap) {\n            // expose & exclude do not apply for map keys only to fields\n            return keys;\n        }\n        /**\n         * If decorators are ignored but we don't want the extraneous values, then we use the\n         * metadata to decide which property is needed, but doesn't apply the decorator effect.\n         */\n        if (this.options.ignoreDecorators && this.options.excludeExtraneousValues && target) {\n            var exposedProperties = defaultMetadataStorage.getExposedProperties(target, this.transformationType);\n            var excludedProperties = defaultMetadataStorage.getExcludedProperties(target, this.transformationType);\n            keys = __spreadArray(__spreadArray([], exposedProperties, true), excludedProperties, true);\n        }\n        if (!this.options.ignoreDecorators && target) {\n            // add all exposed to list of keys\n            var exposedProperties = defaultMetadataStorage.getExposedProperties(target, this.transformationType);\n            if (this.transformationType === TransformationType.PLAIN_TO_CLASS) {\n                exposedProperties = exposedProperties.map(function (key) {\n                    var exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n                    if (exposeMetadata && exposeMetadata.options && exposeMetadata.options.name) {\n                        return exposeMetadata.options.name;\n                    }\n                    return key;\n                });\n            }\n            if (this.options.excludeExtraneousValues) {\n                keys = exposedProperties;\n            }\n            else {\n                keys = keys.concat(exposedProperties);\n            }\n            // exclude excluded properties\n            var excludedProperties_1 = defaultMetadataStorage.getExcludedProperties(target, this.transformationType);\n            if (excludedProperties_1.length > 0) {\n                keys = keys.filter(function (key) {\n                    return !excludedProperties_1.includes(key);\n                });\n            }\n            // apply versioning options\n            if (this.options.version !== undefined) {\n                keys = keys.filter(function (key) {\n                    var exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n                    if (!exposeMetadata || !exposeMetadata.options)\n                        return true;\n                    return _this.checkVersion(exposeMetadata.options.since, exposeMetadata.options.until);\n                });\n            }\n            // apply grouping options\n            if (this.options.groups && this.options.groups.length) {\n                keys = keys.filter(function (key) {\n                    var exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n                    if (!exposeMetadata || !exposeMetadata.options)\n                        return true;\n                    return _this.checkGroups(exposeMetadata.options.groups);\n                });\n            }\n            else {\n                keys = keys.filter(function (key) {\n                    var exposeMetadata = defaultMetadataStorage.findExposeMetadata(target, key);\n                    return (!exposeMetadata ||\n                        !exposeMetadata.options ||\n                        !exposeMetadata.options.groups ||\n                        !exposeMetadata.options.groups.length);\n                });\n            }\n        }\n        // exclude prefixed properties\n        if (this.options.excludePrefixes && this.options.excludePrefixes.length) {\n            keys = keys.filter(function (key) {\n                return _this.options.excludePrefixes.every(function (prefix) {\n                    return key.substr(0, prefix.length) !== prefix;\n                });\n            });\n        }\n        // make sure we have unique keys\n        keys = keys.filter(function (key, index, self) {\n            return self.indexOf(key) === index;\n        });\n        return keys;\n    };\n    TransformOperationExecutor.prototype.checkVersion = function (since, until) {\n        var decision = true;\n        if (decision && since)\n            decision = this.options.version >= since;\n        if (decision && until)\n            decision = this.options.version < until;\n        return decision;\n    };\n    TransformOperationExecutor.prototype.checkGroups = function (groups) {\n        if (!groups)\n            return true;\n        return this.options.groups.some(function (optionGroup) { return groups.includes(optionGroup); });\n    };\n    return TransformOperationExecutor;\n}());\nexport { TransformOperationExecutor };\n//# sourceMappingURL=TransformOperationExecutor.js.map", "export function isPromise(p) {\n    return p !== null && typeof p === 'object' && typeof p.then === 'function';\n}\n//# sourceMappingURL=is-promise.util.js.map", "/**\n * This function returns the global object across Node and browsers.\n *\n * Note: `globalThis` is the standardized approach however it has been added to\n * Node.js in version 12. We need to include this snippet until Node 12 EOL.\n */\nexport function getGlobal() {\n    if (typeof globalThis !== 'undefined') {\n        return globalThis;\n    }\n    if (typeof global !== 'undefined') {\n        return global;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore: Cannot find name 'window'.\n    if (typeof window !== 'undefined') {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore: Cannot find name 'window'.\n        return window;\n    }\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore: Cannot find name 'self'.\n    if (typeof self !== 'undefined') {\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore: Cannot find name 'self'.\n        return self;\n    }\n}\n//# sourceMappingURL=get-global.util.js.map", "/**\n * These are the default options used by any transformation operation.\n */\nexport var defaultOptions = {\n    enableCircularCheck: false,\n    enableImplicitConversion: false,\n    excludeExtraneousValues: false,\n    excludePrefixes: undefined,\n    exposeDefaultValues: false,\n    exposeUnsetFields: true,\n    groups: undefined,\n    ignoreDecorators: false,\n    strategy: undefined,\n    targetMaps: undefined,\n    version: undefined,\n};\n//# sourceMappingURL=default-options.constant.js.map", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { TransformOperationExecutor } from './TransformOperationExecutor';\nimport { TransformationType } from './enums';\nimport { defaultOptions } from './constants/default-options.constant';\nvar ClassTransformer = /** @class */ (function () {\n    function ClassTransformer() {\n    }\n    ClassTransformer.prototype.instanceToPlain = function (object, options) {\n        var executor = new TransformOperationExecutor(TransformationType.CLASS_TO_PLAIN, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(undefined, object, undefined, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.classToPlainFromExist = function (object, plainObject, options) {\n        var executor = new TransformOperationExecutor(TransformationType.CLASS_TO_PLAIN, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(plainObject, object, undefined, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.plainToInstance = function (cls, plain, options) {\n        var executor = new TransformOperationExecutor(TransformationType.PLAIN_TO_CLASS, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(undefined, plain, cls, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.plainToClassFromExist = function (clsObject, plain, options) {\n        var executor = new TransformOperationExecutor(TransformationType.PLAIN_TO_CLASS, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(clsObject, plain, undefined, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.instanceToInstance = function (object, options) {\n        var executor = new TransformOperationExecutor(TransformationType.CLASS_TO_CLASS, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(undefined, object, undefined, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.classToClassFromExist = function (object, fromObject, options) {\n        var executor = new TransformOperationExecutor(TransformationType.CLASS_TO_CLASS, __assign(__assign({}, defaultOptions), options));\n        return executor.transform(fromObject, object, undefined, undefined, undefined, undefined);\n    };\n    ClassTransformer.prototype.serialize = function (object, options) {\n        return JSON.stringify(this.instanceToPlain(object, options));\n    };\n    /**\n     * Deserializes given JSON string to a object of the given class.\n     */\n    ClassTransformer.prototype.deserialize = function (cls, json, options) {\n        var jsonObject = JSON.parse(json);\n        return this.plainToInstance(cls, jsonObject, options);\n    };\n    /**\n     * Deserializes given JSON string to an array of objects of the given class.\n     */\n    ClassTransformer.prototype.deserializeArray = function (cls, json, options) {\n        var jsonObject = JSON.parse(json);\n        return this.plainToInstance(cls, jsonObject, options);\n    };\n    return ClassTransformer;\n}());\nexport { ClassTransformer };\n//# sourceMappingURL=ClassTransformer.js.map", "import { ClassTransformer } from './ClassTransformer';\nexport { ClassTransformer } from './ClassTransformer';\nexport * from './decorators';\nexport * from './interfaces';\nexport * from './enums';\nvar classTransformer = new ClassTransformer();\nexport function classToPlain(object, options) {\n    return classTransformer.instanceToPlain(object, options);\n}\nexport function instanceToPlain(object, options) {\n    return classTransformer.instanceToPlain(object, options);\n}\nexport function classToPlainFromExist(object, plainObject, options) {\n    return classTransformer.classToPlainFromExist(object, plainObject, options);\n}\nexport function plainToClass(cls, plain, options) {\n    return classTransformer.plainToInstance(cls, plain, options);\n}\nexport function plainToInstance(cls, plain, options) {\n    return classTransformer.plainToInstance(cls, plain, options);\n}\nexport function plainToClassFromExist(clsObject, plain, options) {\n    return classTransformer.plainToClassFromExist(clsObject, plain, options);\n}\nexport function instanceToInstance(object, options) {\n    return classTransformer.instanceToInstance(object, options);\n}\nexport function classToClassFromExist(object, fromObject, options) {\n    return classTransformer.classToClassFromExist(object, fromObject, options);\n}\nexport function serialize(object, options) {\n    return classTransformer.serialize(object, options);\n}\n/**\n * Deserializes given JSON string to a object of the given class.\n *\n * @deprecated This function is being removed. Please use the following instead:\n * ```\n * instanceToClass(cls, JSON.parse(json), options)\n * ```\n */\nexport function deserialize(cls, json, options) {\n    return classTransformer.deserialize(cls, json, options);\n}\n/**\n * Deserializes given JSON string to an array of objects of the given class.\n *\n * @deprecated This function is being removed. Please use the following instead:\n * ```\n * JSON.parse(json).map(value => instanceToClass(cls, value, options))\n * ```\n *\n */\nexport function deserializeArray(cls, json, options) {\n    return classTransformer.deserializeArray(cls, json, options);\n}\n//# sourceMappingURL=index.js.map", "import { defaultMetadataStorage } from '../storage';\n/**\n * Marks the given class or property as excluded. By default the property is excluded in both\n * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction\n * via using the `toPlainOnly` or `toClassOnly` option.\n *\n * Can be applied to class definitions and properties.\n */\nexport function Exclude(options) {\n    if (options === void 0) { options = {}; }\n    /**\n     * NOTE: The `propertyName` property must be marked as optional because\n     * this decorator used both as a class and a property decorator and the\n     * Typescript compiler will freak out if we make it mandatory as a class\n     * decorator only receives one parameter.\n     */\n    return function (object, propertyName) {\n        defaultMetadataStorage.addExcludeMetadata({\n            target: object instanceof Function ? object : object.constructor,\n            propertyName: propertyName,\n            options: options,\n        });\n    };\n}\n//# sourceMappingURL=exclude.decorator.js.map", "import { defaultMetadataStorage } from '../storage';\n/**\n * Marks the given class or property as included. By default the property is included in both\n * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction\n * via using the `toPlainOnly` or `toClassOnly` option.\n *\n * Can be applied to class definitions and properties.\n */\nexport function Expose(options) {\n    if (options === void 0) { options = {}; }\n    /**\n     * NOTE: The `propertyName` property must be marked as optional because\n     * this decorator used both as a class and a property decorator and the\n     * Typescript compiler will freak out if we make it mandatory as a class\n     * decorator only receives one parameter.\n     */\n    return function (object, propertyName) {\n        defaultMetadataStorage.addExposeMetadata({\n            target: object instanceof Function ? object : object.constructor,\n            propertyName: propertyName,\n            options: options,\n        });\n    };\n}\n//# sourceMappingURL=expose.decorator.js.map", "import { defaultMetadataStorage } from '../storage';\n/**\n * Defines a custom logic for value transformation.\n *\n * Can be applied to properties only.\n */\nexport function Transform(transformFn, options) {\n    if (options === void 0) { options = {}; }\n    return function (target, propertyName) {\n        defaultMetadataStorage.addTransformMetadata({\n            target: target.constructor,\n            propertyName: propertyName,\n            transformFn: transformFn,\n            options: options,\n        });\n    };\n}\n//# sourceMappingURL=transform.decorator.js.map", "import { ClassTransformer } from '../ClassTransformer';\n/**\n * Return the class instance only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformInstanceToInstance(params) {\n    return function (target, propertyKey, descriptor) {\n        var classTransformer = new ClassTransformer();\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var result = originalMethod.apply(this, args);\n            var isPromise = !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n            return isPromise\n                ? result.then(function (data) { return classTransformer.instanceToInstance(data, params); })\n                : classTransformer.instanceToInstance(result, params);\n        };\n    };\n}\n//# sourceMappingURL=transform-instance-to-instance.decorator.js.map", "import { ClassTransformer } from '../ClassTransformer';\n/**\n * Transform the object from class to plain object and return only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformInstanceToPlain(params) {\n    return function (target, propertyKey, descriptor) {\n        var classTransformer = new ClassTransformer();\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var result = originalMethod.apply(this, args);\n            var isPromise = !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n            return isPromise\n                ? result.then(function (data) { return classTransformer.instanceToPlain(data, params); })\n                : classTransformer.instanceToPlain(result, params);\n        };\n    };\n}\n//# sourceMappingURL=transform-instance-to-plain.decorator.js.map", "import { ClassTransformer } from '../ClassTransformer';\n/**\n * Return the class instance only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformPlainToInstance(classType, params) {\n    return function (target, propertyKey, descriptor) {\n        var classTransformer = new ClassTransformer();\n        var originalMethod = descriptor.value;\n        descriptor.value = function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            var result = originalMethod.apply(this, args);\n            var isPromise = !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n            return isPromise\n                ? result.then(function (data) { return classTransformer.plainToInstance(classType, data, params); })\n                : classTransformer.plainToInstance(classType, result, params);\n        };\n    };\n}\n//# sourceMappingURL=transform-plain-to-instance.decorator.js.map", "import { defaultMetadataStorage } from '../storage';\n/**\n * Specifies a type of the property.\n * The given TypeFunction can return a constructor. A discriminator can be given in the options.\n *\n * Can be applied to properties only.\n */\nexport function Type(typeFunction, options) {\n    if (options === void 0) { options = {}; }\n    return function (target, propertyName) {\n        var reflectedType = Reflect.getMetadata('design:type', target, propertyName);\n        defaultMetadataStorage.addTypeMetadata({\n            target: target.constructor,\n            propertyName: propertyName,\n            reflectedType: reflectedType,\n            typeFunction: typeFunction,\n            options: options,\n        });\n    };\n}\n//# sourceMappingURL=type.decorator.js.map"], "names": ["TransformationType", "defaultMetadataStorage", "MetadataStorage", "this", "_typeMetadatas", "Map", "_transformMetadatas", "_exposeMetadatas", "_excludeMetadatas", "_ancestorsMap", "prototype", "addTypeMetadata", "metadata", "has", "target", "set", "get", "propertyName", "addTransformMetadata", "push", "addExposeMetadata", "addExcludeMetadata", "findTransformMetadatas", "transformationType", "findMetadatas", "filter", "options", "toClassOnly", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CLASS_TO_CLASS", "PLAIN_TO_CLASS", "CLASS_TO_PLAIN", "findExcludeMetadata", "findMetadata", "findExposeMetadata", "findExposeMetadataByCustomName", "name", "getExposedMetadatas", "find", "findTypeMetadata", "getStrategy", "excludeMap", "exclude", "undefined", "exposeMap", "expose", "getMetadata", "getExcludedMetadatas", "getExposedProperties", "map", "getExcludedProperties", "clear", "metadatas", "metadataFromTarget", "metadataFromTargetMap", "Array", "from", "values", "meta", "metadataFromAncestors", "_i", "_a", "getAncestors", "length", "ancestor", "ancestorMetadataMap", "metadataFromAncestor", "apply", "concat", "ancestorResult", "metadataFromAncestorsTarget", "slice", "reverse", "ancestors", "baseClass", "Object", "getPrototypeOf", "constructor", "__spread<PERSON><PERSON>y", "to", "pack", "arguments", "ar", "i", "l", "call", "TransformOperationExecutor", "recursionStack", "Set", "transform", "source", "value", "targetType", "arrayType", "isMap", "level", "p", "_this", "isArray", "newValue_1", "array", "instantiateArrayType", "for<PERSON>ach", "subValue", "index", "subSource", "enableCircularCheck", "isCircular", "add", "realTargetType", "discriminator", "property", "subTypes", "subType", "newObject", "object", "newType", "typeFunction", "keepDiscriminatorProperty", "value_1", "String", "Number", "Boolean", "Date", "valueOf", "globalThis", "global", "window", "self", "<PERSON><PERSON><PERSON>", "then", "keys", "get<PERSON><PERSON><PERSON>", "newValue", "_loop_1", "key", "valueKey", "newValueKey", "this_1", "ignoreDecorators", "exposeMetadata", "Function", "type", "isSubValueMap", "metadata_1", "reflectedType", "targetMaps", "properties", "enableImplicitConversion", "Reflect", "arrayType_1", "getReflectedType", "descriptor", "getOwnPropertyDescriptor", "finalValue", "applyCustomTransformations", "exposeUnsetFields", "transform<PERSON>ey", "exposeDefaultValues", "keys_1", "delete", "Promise", "resolve", "reject", "data", "obj", "version", "checkVersion", "since", "until", "groups", "checkGroups", "transformFn", "strategy", "excludeExtraneousValues", "exposedProperties", "excludedProperties", "excludedProperties_1", "includes", "excludePrefixes", "every", "prefix", "substr", "indexOf", "decision", "some", "optionGroup", "defaultOptions", "__assign", "assign", "t", "s", "n", "hasOwnProperty", "ClassTransformer", "instanceToPlain", "classToPlainFromExist", "plainObject", "plainToInstance", "cls", "plain", "plainToClassFromExist", "clsObject", "instanceToInstance", "classToClassFromExist", "fromObject", "serialize", "JSON", "stringify", "deserialize", "json", "jsonObject", "parse", "deserializeArray", "classTransformer", "params", "propertyKey", "originalMethod", "args", "result", "isPromise", "classType"], "mappings": "wPACA,IAAWA,+BAAAA,EAIRA,uBAAuBA,qBAAqB,KAHxBA,EAAmC,eAAI,GAAK,iBAC/DA,EAAmBA,EAAmC,eAAI,GAAK,iBAC/DA,EAAmBA,EAAmC,eAAI,GAAK,iBCAnE,ICAWC,EAAyB,IDAC,WACjC,SAASC,IAILC,KAAKC,eAAiB,IAAIC,IAC1BF,KAAKG,oBAAsB,IAAID,IAC/BF,KAAKI,iBAAmB,IAAIF,IAC5BF,KAAKK,kBAAoB,IAAIH,IAC7BF,KAAKM,cAAgB,IAAIJ,IAmM7B,OA9LAH,EAAgBQ,UAAUC,gBAAkB,SAAUC,GAC7CT,KAAKC,eAAeS,IAAID,EAASE,SAClCX,KAAKC,eAAeW,IAAIH,EAASE,OAAQ,IAAIT,KAEjDF,KAAKC,eAAeY,IAAIJ,EAASE,QAAQC,IAAIH,EAASK,aAAcL,IAExEV,EAAgBQ,UAAUQ,qBAAuB,SAAUN,GAClDT,KAAKG,oBAAoBO,IAAID,EAASE,SACvCX,KAAKG,oBAAoBS,IAAIH,EAASE,OAAQ,IAAIT,KAEjDF,KAAKG,oBAAoBU,IAAIJ,EAASE,QAAQD,IAAID,EAASK,eAC5Dd,KAAKG,oBAAoBU,IAAIJ,EAASE,QAAQC,IAAIH,EAASK,aAAc,IAE7Ed,KAAKG,oBAAoBU,IAAIJ,EAASE,QAAQE,IAAIJ,EAASK,cAAcE,KAAKP,IAElFV,EAAgBQ,UAAUU,kBAAoB,SAAUR,GAC/CT,KAAKI,iBAAiBM,IAAID,EAASE,SACpCX,KAAKI,iBAAiBQ,IAAIH,EAASE,OAAQ,IAAIT,KAEnDF,KAAKI,iBAAiBS,IAAIJ,EAASE,QAAQC,IAAIH,EAASK,aAAcL,IAE1EV,EAAgBQ,UAAUW,mBAAqB,SAAUT,GAChDT,KAAKK,kBAAkBK,IAAID,EAASE,SACrCX,KAAKK,kBAAkBO,IAAIH,EAASE,OAAQ,IAAIT,KAEpDF,KAAKK,kBAAkBQ,IAAIJ,EAASE,QAAQC,IAAIH,EAASK,aAAcL,IAK3EV,EAAgBQ,UAAUY,uBAAyB,SAAUR,EAAQG,EAAcM,GAC/E,OAAOpB,KAAKqB,cAAcrB,KAAKG,oBAAqBQ,EAAQG,GAAcQ,QAAO,SAAUb,GACvF,OAAKA,EAASc,WAEuB,IAAjCd,EAASc,QAAQC,cAAyD,IAAjCf,EAASc,QAAQE,eAEzB,IAAjChB,EAASc,QAAQC,YACTJ,IAAuBvB,qBAAmB6B,gBAC9CN,IAAuBvB,qBAAmB8B,gBAEb,IAAjClB,EAASc,QAAQE,aACVL,IAAuBvB,qBAAmB+B,qBAK7D7B,EAAgBQ,UAAUsB,oBAAsB,SAAUlB,EAAQG,GAC9D,OAAOd,KAAK8B,aAAa9B,KAAKK,kBAAmBM,EAAQG,IAE7Df,EAAgBQ,UAAUwB,mBAAqB,SAAUpB,EAAQG,GAC7D,OAAOd,KAAK8B,aAAa9B,KAAKI,iBAAkBO,EAAQG,IAE5Df,EAAgBQ,UAAUyB,+BAAiC,SAAUrB,EAAQsB,GACzE,OAAOjC,KAAKkC,oBAAoBvB,GAAQwB,MAAK,SAAU1B,GACnD,OAAOA,EAASc,SAAWd,EAASc,QAAQU,OAASA,MAG7DlC,EAAgBQ,UAAU6B,iBAAmB,SAAUzB,EAAQG,GAC3D,OAAOd,KAAK8B,aAAa9B,KAAKC,eAAgBU,EAAQG,IAE1Df,EAAgBQ,UAAU8B,YAAc,SAAU1B,GAC9C,IAAI2B,EAAatC,KAAKK,kBAAkBQ,IAAIF,GACxC4B,EAAUD,GAAcA,EAAWzB,SAAI2B,GACvCC,EAAYzC,KAAKI,iBAAiBS,IAAIF,GACtC+B,EAASD,GAAaA,EAAU5B,SAAI2B,GACxC,OAAKD,GAAWG,IAAaH,IAAYG,EAC9B,OACJH,EAAU,aAAe,aAEpCxC,EAAgBQ,UAAU2B,oBAAsB,SAAUvB,GACtD,OAAOX,KAAK2C,YAAY3C,KAAKI,iBAAkBO,IAEnDZ,EAAgBQ,UAAUqC,qBAAuB,SAAUjC,GACvD,OAAOX,KAAK2C,YAAY3C,KAAKK,kBAAmBM,IAEpDZ,EAAgBQ,UAAUsC,qBAAuB,SAAUlC,EAAQS,GAC/D,OAAOpB,KAAKkC,oBAAoBvB,GAC3BW,QAAO,SAAUb,GAClB,OAAKA,EAASc,WAEuB,IAAjCd,EAASc,QAAQC,cAAyD,IAAjCf,EAASc,QAAQE,eAEzB,IAAjChB,EAASc,QAAQC,YACTJ,IAAuBvB,qBAAmB6B,gBAC9CN,IAAuBvB,qBAAmB8B,gBAEb,IAAjClB,EAASc,QAAQE,aACVL,IAAuBvB,qBAAmB+B,oBAIpDkB,KAAI,SAAUrC,GAAY,OAAOA,EAASK,iBAEnDf,EAAgBQ,UAAUwC,sBAAwB,SAAUpC,EAAQS,GAChE,OAAOpB,KAAK4C,qBAAqBjC,GAC5BW,QAAO,SAAUb,GAClB,OAAKA,EAASc,WAEuB,IAAjCd,EAASc,QAAQC,cAAyD,IAAjCf,EAASc,QAAQE,eAEzB,IAAjChB,EAASc,QAAQC,YACTJ,IAAuBvB,qBAAmB6B,gBAC9CN,IAAuBvB,qBAAmB8B,gBAEb,IAAjClB,EAASc,QAAQE,aACVL,IAAuBvB,qBAAmB+B,oBAIpDkB,KAAI,SAAUrC,GAAY,OAAOA,EAASK,iBAEnDf,EAAgBQ,UAAUyC,MAAQ,WAC9BhD,KAAKC,eAAe+C,QACpBhD,KAAKI,iBAAiB4C,QACtBhD,KAAKK,kBAAkB2C,QACvBhD,KAAKM,cAAc0C,SAKvBjD,EAAgBQ,UAAUoC,YAAc,SAAUM,EAAWtC,GACzD,IACIuC,EADAC,EAAwBF,EAAUpC,IAAIF,GAEtCwC,IACAD,EAAqBE,MAAMC,KAAKF,EAAsBG,UAAUhC,QAAO,SAAUiC,GAAQ,YAA6Bf,IAAtBe,EAAKzC,iBAGzG,IADA,IAAI0C,EAAwB,GACnBC,EAAK,EAAGC,EAAK1D,KAAK2D,aAAahD,GAAS8C,EAAKC,EAAGE,OAAQH,IAAM,CACnE,IAAII,EAAWH,EAAGD,GACdK,EAAsBb,EAAUpC,IAAIgD,GACxC,GAAIC,EAAqB,CACrB,IAAIC,EAAuBX,MAAMC,KAAKS,EAAoBR,UAAUhC,QAAO,SAAUiC,GAAQ,YAA6Bf,IAAtBe,EAAKzC,gBACzG0C,EAAsBxC,KAAKgD,MAAMR,EAAuBO,IAGhE,OAAOP,EAAsBS,OAAOf,GAAsB,KAE9DnD,EAAgBQ,UAAUuB,aAAe,SAAUmB,EAAWtC,EAAQG,GAClE,IAAIqC,EAAwBF,EAAUpC,IAAIF,GAC1C,GAAIwC,EAAuB,CACvB,IAAID,EAAqBC,EAAsBtC,IAAIC,GACnD,GAAIoC,EACA,OAAOA,EAGf,IAAK,IAAIO,EAAK,EAAGC,EAAK1D,KAAK2D,aAAahD,GAAS8C,EAAKC,EAAGE,OAAQH,IAAM,CACnE,IAAII,EAAWH,EAAGD,GACdK,EAAsBb,EAAUpC,IAAIgD,GACxC,GAAIC,EAAqB,CACrB,IAAII,EAAiBJ,EAAoBjD,IAAIC,GAC7C,GAAIoD,EACA,OAAOA,KAMvBnE,EAAgBQ,UAAUc,cAAgB,SAAU4B,EAAWtC,EAAQG,GACnE,IACIoC,EADAC,EAAwBF,EAAUpC,IAAIF,GAEtCwC,IACAD,EAAqBC,EAAsBtC,IAAIC,IAGnD,IADA,IAAIqD,EAA8B,GACzBV,EAAK,EAAGC,EAAK1D,KAAK2D,aAAahD,GAAS8C,EAAKC,EAAGE,OAAQH,IAAM,CACnE,IAAII,EAAWH,EAAGD,GACdK,EAAsBb,EAAUpC,IAAIgD,GACpCC,GACIA,EAAoBpD,IAAII,IACxBqD,EAA4BnD,KAAKgD,MAAMG,EAA6BL,EAAoBjD,IAAIC,IAIxG,OAAOqD,EACFC,QACAC,UACAJ,QAAQf,GAAsB,IAAIkB,QAAQC,YAEnDtE,EAAgBQ,UAAUoD,aAAe,SAAUhD,GAC/C,IAAKA,EACD,MAAO,GACX,IAAKX,KAAKM,cAAcI,IAAIC,GAAS,CAEjC,IADA,IAAI2D,EAAY,GACPC,EAAYC,OAAOC,eAAe9D,EAAOJ,UAAUmE,kBAA6C,IAAxBH,EAAUhE,UAA2BgE,EAAYC,OAAOC,eAAeF,EAAUhE,UAAUmE,aACxKJ,EAAUtD,KAAKuD,GAEnBvE,KAAKM,cAAcM,IAAID,EAAQ2D,GAEnC,OAAOtE,KAAKM,cAAcO,IAAIF,IAE3BZ,MEhNX,IAAI4E,EAAgD,SAAUC,EAAIvB,EAAMwB,GACpE,GAAIA,GAA6B,IAArBC,UAAUlB,OAAc,IAAK,IAA4BmB,EAAxBC,EAAI,EAAGC,EAAI5B,EAAKO,OAAYoB,EAAIC,EAAGD,KACxED,GAAQC,KAAK3B,IACR0B,IAAIA,EAAK3B,MAAM7C,UAAU6D,MAAMc,KAAK7B,EAAM,EAAG2B,IAClDD,EAAGC,GAAK3B,EAAK2B,IAGrB,OAAOJ,EAAGX,OAAOc,GAAM3B,MAAM7C,UAAU6D,MAAMc,KAAK7B,KAYtD,IAAI8B,EAA4C,WAI5C,SAASA,EAA2B/D,EAAoBG,GACpDvB,KAAKoB,mBAAqBA,EAC1BpB,KAAKuB,QAAUA,EAIfvB,KAAKoF,eAAiB,IAAIC,IAkd9B,OA7cAF,EAA2B5E,UAAU+E,UAAY,SAAUC,EAAQC,EAAOC,EAAYC,EAAWC,EAAOC,GACpG,ICnCkBC,EDmCdC,EAAQ9F,KAEZ,QADc,IAAV4F,IAAoBA,EAAQ,GAC5BxC,MAAM2C,QAAQP,IAAUA,aAAiBH,IAAK,CAC9C,IAAIW,EAAaN,GAAa1F,KAAKoB,qBAAuBvB,qBAAmB8B,eA1BzF,SAA8B+D,GAC1B,IAAIO,EAAQ,IAAIP,EAChB,OAAMO,aAAiBZ,KAAU,SAAUY,EAGpCA,EAFI,GAwBGC,CAAqBR,GACrB,GAgDN,OA/CAF,EAAMW,SAAQ,SAAUC,EAAUC,GAC9B,IAAIC,EAAYf,EAASA,EAAOc,QAAS7D,EACzC,GAAKsD,EAAMvE,QAAQgF,qBAAwBT,EAAMU,WAAWJ,GAoCnDN,EAAM1E,qBAAuBvB,qBAAmB6B,iBACjDsE,aAAsBX,IACtBW,EAAWS,IAAIL,GAGfJ,EAAWhF,KAAKoF,QAzC+C,CACnE,IAAIM,OAAiB,EACrB,GAA0B,mBAAfjB,GACPA,GACAA,EAAWlE,SACXkE,EAAWlE,QAAQoF,eACnBlB,EAAWlE,QAAQoF,cAAcC,UACjCnB,EAAWlE,QAAQoF,cAAcE,SAAU,CAC3C,GAAIf,EAAM1E,qBAAuBvB,qBAAmB8B,eAAgB,CAChE+E,EAAiBjB,EAAWlE,QAAQoF,cAAcE,SAAS1E,MAAK,SAAU2E,GACtE,OAAOA,EAAQ7E,OAASmE,EAASX,EAAWlE,QAAQoF,cAAcC,aAEtE,IAAIrF,EAAU,CAAEwF,UAAWf,EAAYgB,OAAQZ,EAAUQ,cAAUpE,GAC/DyE,EAAUxB,EAAWyB,aAAa3F,GACNmF,OAAblE,IAAnBkE,EAAiDO,EAA6BP,EAAelB,MACxFC,EAAWlE,QAAQ4F,kCACbf,EAASX,EAAWlE,QAAQoF,cAAcC,UAErDd,EAAM1E,qBAAuBvB,qBAAmB6B,iBAChDgF,EAAiBN,EAAS1B,aAE1BoB,EAAM1E,qBAAuBvB,qBAAmB+B,iBAChDwE,EAASX,EAAWlE,QAAQoF,cAAcC,UAAYnB,EAAWlE,QAAQoF,cAAcE,SAAS1E,MAAK,SAAU2E,GAAW,OAAOA,EAAQtB,QAAUY,EAAS1B,eAAgBzC,WAIhLyE,EAAiBjB,EAErB,IAAI2B,EAAUtB,EAAMR,UAAUgB,EAAWF,EAAUM,OAAgBlE,EAAW4D,aAAoBlG,IAAK0F,EAAQ,GAC3GI,aAAsBX,IACtBW,EAAWS,IAAIW,GAGfpB,EAAWhF,KAAKoG,OAYrBpB,EAEN,GAAIP,IAAe4B,QAAW1B,EAK9B,CAAA,GAAIF,IAAe6B,QAAW3B,EAK9B,CAAA,GAAIF,IAAe8B,SAAY5B,EAK/B,CAAA,IAAKF,IAAe+B,MAAQhC,aAAiBgC,QAAU7B,EACxD,OAAIH,aAAiBgC,KACV,IAAIA,KAAKhC,EAAMiC,WAEtBjC,MAAAA,EACOA,EACJ,IAAIgC,KAAKhC,GAEf,IE1GiB,oBAAfkC,WACAA,WAEW,oBAAXC,OACAA,OAIW,oBAAXC,OAGAA,OAIS,oBAATC,KAGAA,UAHX,GF2F2BC,SAAWrC,IAAeqC,QAAUtC,aAAiBsC,UAAYnC,EACpF,OAAIH,MAAAA,EACOA,EACJsC,OAAOzE,KAAKmC,GAElB,GCrHI,QADSK,EDsHCL,ICrHW,iBAANK,GAAoC,mBAAXA,EAAEkC,MDqHrBpC,EAKzB,CAAA,GAAKA,GAAmB,OAAVH,GAAmC,iBAAVA,GAA4C,mBAAfA,EAAMuC,KAK1E,CAAA,GAAqB,iBAAVvC,GAAgC,OAAVA,EAAgB,CAE7CC,GAAcD,EAAMd,cAAgBF,SAChCpB,MAAM2C,QAAQP,IAAUA,EAAMd,cAAgBtB,SAQ/CqC,EAAaD,EAAMd,cAEtBe,GAAcF,IACfE,EAAaF,EAAOb,aACpB1E,KAAKuB,QAAQgF,qBAEbvG,KAAKoF,eAAeqB,IAAIjB,GAE5B,IAAIwC,EAAOhI,KAAKiI,QAAQxC,EAAYD,EAAOG,GACvCuC,EAAW3C,GAAkB,GAC5BA,GACAvF,KAAKoB,qBAAuBvB,qBAAmB8B,gBAC5C3B,KAAKoB,qBAAuBvB,qBAAmB6B,iBAE/CwG,EADAvC,EACW,IAAIzF,IAEVuF,EACM,IAAIA,EAGJ,IA+KnB,IA5KA,IAAI0C,EAAU,SAAUC,GACpB,GAAY,cAARA,GAA+B,gBAARA,EACvB,MAAO,WAEX,IAAIC,EAAWD,EACXE,EAAcF,EAAKtH,EAAesH,EACtC,IAAKG,EAAOhH,QAAQiH,kBAAoB/C,EACpC,GAAI8C,EAAOnH,qBAAuBvB,qBAAmB8B,gBAC7C8G,EAAiB3I,EAAuBkC,+BAA+ByD,EAAY2C,MAEnFtH,EAAe2H,EAAe3H,aAC9BwH,EAAcG,EAAe3H,mBAGhC,GAAIyH,EAAOnH,qBAAuBvB,qBAAmB+B,gBACtD2G,EAAOnH,qBAAuBvB,qBAAmB6B,eAAgB,CACjE,IAAI+G,GAAAA,EAAiB3I,EAAuBiC,mBAAmB0D,EAAY2C,KACrDK,EAAelH,SAAWkH,EAAelH,QAAQU,OACnEqG,EAAcG,EAAelH,QAAQU,MAKjD,IAAImE,OAAW5D,EAQX4D,EAPAmC,EAAOnH,qBAAuBvB,qBAAmB8B,eAOtC6D,EAAM6C,GAGb7C,aAAiBtF,IACNsF,EAAM3E,IAAIwH,GAEhB7C,EAAM6C,aAAqBK,SACrBlD,EAAM6C,KAGN7C,EAAM6C,GAIzB,IAAIM,OAAOnG,EAAWoG,EAAgBxC,aAAoBlG,IAC1D,GAAIuF,GAAcE,EACdgD,EAAOlD,OAEN,GAAIA,EAAY,CACjB,IAAIoD,EAAa/I,EAAuBsC,iBAAiBqD,EAAY3E,GACrE,GAAI+H,EAAY,CACZ,IAAItH,EAAU,CAAEwF,UAAWmB,EAAUlB,OAAQxB,EAAOoB,SAAU9F,GAC1DmG,EAAU4B,EAAW3B,aAAe2B,EAAW3B,aAAa3F,GAAWsH,EAAWC,cAClFD,EAAWtH,SACXsH,EAAWtH,QAAQoF,eACnBkC,EAAWtH,QAAQoF,cAAcC,UACjCiC,EAAWtH,QAAQoF,cAAcE,SAC3BrB,EAAM6C,aAAqBjF,MAwB7BuF,EAAOE,GAvBHN,EAAOnH,qBAAuBvB,qBAAmB8B,iBAM3BgH,OAAbnG,KALTmG,EAAOE,EAAWtH,QAAQoF,cAAcE,SAAS1E,MAAK,SAAU2E,GAC5D,GAAIV,GAAYA,aAAoB5B,QAAUqE,EAAWtH,QAAQoF,cAAcC,YAAYR,EACvF,OAAOU,EAAQ7E,OAASmE,EAASyC,EAAWtH,QAAQoF,cAAcC,cAG7CK,EAAmB0B,EAAKnD,MAChDqD,EAAWtH,QAAQ4F,2BAChBf,GAAYA,aAAoB5B,QAAUqE,EAAWtH,QAAQoF,cAAcC,YAAYR,UAChFA,EAASyC,EAAWtH,QAAQoF,cAAcC,WAIzD2B,EAAOnH,qBAAuBvB,qBAAmB6B,iBACjDiH,EAAOvC,EAAS1B,aAEhB6D,EAAOnH,qBAAuBvB,qBAAmB+B,gBAC7CwE,IACAA,EAASyC,EAAWtH,QAAQoF,cAAcC,UAAYiC,EAAWtH,QAAQoF,cAAcE,SAAS1E,MAAK,SAAU2E,GAAW,OAAOA,EAAQtB,QAAUY,EAAS1B,eAAgBzC,OASxL0G,EAAO1B,EAEX2B,EAAgBA,GAAiBC,EAAWC,gBAAkB5I,SAE7D,GAAIqI,EAAOhH,QAAQwH,WAEpBR,EAAOhH,QAAQwH,WACVzH,QAAO,SAAUwB,GAAO,OAAOA,EAAInC,SAAW8E,KAAgB3C,EAAIkG,WAAWlI,MAC7EqF,SAAQ,SAAUrD,GAAO,OAAQ6F,EAAO7F,EAAIkG,WAAWlI,WAE3D,GAAIyH,EAAOhH,QAAQ0H,0BACpBV,EAAOnH,qBAAuBvB,qBAAmB8B,eAAgB,CAGjE,IAAImH,EAAgBI,QAAQvG,YAAY,cAAe8C,EAAWlF,UAAWO,GACzEgI,IACAH,EAAOG,IAKnB,IAAIK,EAAc/F,MAAM2C,QAAQP,EAAM6C,IAChCE,EAAOa,iBAAiB3D,EAAY3E,QACpC0B,EAEF8D,EAAYf,EAASA,EAAO8C,QAAY7F,EAM5C,GAAI0F,EAASxD,YAAYnE,UAAW,CAChC,IAAI8I,EAAa7E,OAAO8E,yBAAyBpB,EAASxD,YAAYnE,UAAW+H,GACjF,IAAKC,EAAOnH,qBAAuBvB,qBAAmB8B,gBAClD4G,EAAOnH,qBAAuBvB,qBAAmB6B,kBAE/C2H,IAAeA,EAAWzI,KAAQsH,EAASI,aAAwBI,UACrE,MAAO,WAEf,GAAKH,EAAOhH,QAAQgF,qBAAwBgC,EAAO/B,WAAWJ,IAgCzD,GAAImC,EAAOnH,qBAAuBvB,qBAAmB6B,eAAgB,CAClE6H,EAAanD,QAEE5D,KADnB+G,EAAahB,EAAOiB,2BAA2BD,EAAY9D,EAAY2C,EAAK5C,EAAO+C,EAAOnH,sBAC1DmH,EAAOhH,QAAQkI,qBACvCvB,aAAoBhI,IACpBgI,EAAStH,IAAI0H,EAAaiB,GAG1BrB,EAASI,GAAeiB,QAxCqC,CACrE,IAAIG,EAAenB,EAAOnH,qBAAuBvB,qBAAmB8B,eAAiB2G,EAAcF,EAC/FmB,OAAa,EACbhB,EAAOnH,qBAAuBvB,qBAAmB+B,gBAEjD2H,EAAa/D,EAAMkE,GAEnBH,EAAahB,EAAOiB,2BAA2BD,EAAY9D,EAAYiE,EAAclE,EAAO+C,EAAOnH,oBAEnGmI,EAAa/D,EAAMkE,KAAkBH,EAAanD,EAAWmD,EAE7DA,EAAahB,EAAOjD,UAAUgB,EAAWiD,EAAYZ,EAAMQ,EAAaP,EAAehD,EAAQ,SAG9EpD,IAAb4D,GAA0BmC,EAAOhH,QAAQoI,oBAEzCJ,EAAarB,EAASI,IAGtBiB,EAAahB,EAAOjD,UAAUgB,EAAWF,EAAUuC,EAAMQ,EAAaP,EAAehD,EAAQ,GAC7F2D,EAAahB,EAAOiB,2BAA2BD,EAAY9D,EAAYiE,EAAclE,EAAO+C,EAAOnH,2BAGxFoB,IAAf+G,GAA4BhB,EAAOhH,QAAQkI,qBACvCvB,aAAoBhI,IACpBgI,EAAStH,IAAI0H,EAAaiB,GAG1BrB,EAASI,GAAeiB,KAiBpChB,EAASvI,KAEJyD,EAAK,EAAGmG,EAAS5B,EAAMvE,EAAKmG,EAAOhG,OAAQH,IAAM,CAEtD0E,EADUyB,EAAOnG,IAMrB,OAHIzD,KAAKuB,QAAQgF,qBACbvG,KAAKoF,eAAeyE,OAAOrE,GAExB0C,EAGP,OAAO1C,EA1NP,OAAOA,EAPP,OAAO,IAAIsE,SAAQ,SAAUC,EAASC,GAClCxE,EAAMuC,MAAK,SAAUkC,GAAQ,OAAOF,EAAQjE,EAAMR,eAAU9C,EAAWyH,EAAMxE,OAAYjD,OAAWA,EAAWoD,EAAQ,MAAQoE,MAnBnI,OAAIxE,MAAAA,EACOA,EACJ+B,QAAQ/B,GAPf,OAAIA,MAAAA,EACOA,EACJ8B,OAAO9B,GAPd,OAAIA,MAAAA,EACOA,EACJ6B,OAAO7B,IA8PtBL,EAA2B5E,UAAUiJ,2BAA6B,SAAUhE,EAAO7E,EAAQyH,EAAK8B,EAAK9I,GACjG,IAAI0E,EAAQ9F,KACRiD,EAAYnD,EAAuBqB,uBAAuBR,EAAQyH,EAAKpI,KAAKoB,oBAyBhF,YAvB6BoB,IAAzBxC,KAAKuB,QAAQ4I,UACblH,EAAYA,EAAU3B,QAAO,SAAUb,GACnC,OAAKA,EAASc,SAEPuE,EAAMsE,aAAa3J,EAASc,QAAQ8I,MAAO5J,EAASc,QAAQ+I,YAKvErH,EADAjD,KAAKuB,QAAQgJ,QAAUvK,KAAKuB,QAAQgJ,OAAO3G,OAC/BX,EAAU3B,QAAO,SAAUb,GACnC,OAAKA,EAASc,SAEPuE,EAAM0E,YAAY/J,EAASc,QAAQgJ,WAIlCtH,EAAU3B,QAAO,SAAUb,GACnC,OAAQA,EAASc,UAAYd,EAASc,QAAQgJ,SAAW9J,EAASc,QAAQgJ,OAAO3G,WAG/EuC,SAAQ,SAAU1F,GACxB+E,EAAQ/E,EAASgK,YAAY,CAAEjF,MAAOA,EAAO4C,IAAKA,EAAK8B,IAAKA,EAAKvB,KAAMvH,EAAoBG,QAASuE,EAAMvE,aAEvGiE,GAGXL,EAA2B5E,UAAUiG,WAAa,SAAUQ,GACxD,OAAOhH,KAAKoF,eAAe1E,IAAIsG,IAEnC7B,EAA2B5E,UAAU6I,iBAAmB,SAAUzI,EAAQG,GACtE,GAAKH,EAAL,CAEA,IAAI4C,EAAOzD,EAAuBsC,iBAAiBzB,EAAQG,GAC3D,OAAOyC,EAAOA,EAAKuF,mBAAgBtG,IAEvC2C,EAA2B5E,UAAU0H,QAAU,SAAUtH,EAAQqG,EAAQrB,GACrE,IAAIG,EAAQ9F,KAER0K,EAAW5K,EAAuBuC,YAAY1B,GACjC,SAAb+J,IACAA,EAAW1K,KAAKuB,QAAQmJ,UAAY,aAExC,IAAI1C,EAAO,GASX,IARiB,cAAb0C,GAA4B/E,KAExBqC,EADAhB,aAAkB9G,IACXkD,MAAMC,KAAK2D,EAAOgB,QAGlBxD,OAAOwD,KAAKhB,IAGvBrB,EAEA,OAAOqC,EAMX,GAAIhI,KAAKuB,QAAQiH,kBAAoBxI,KAAKuB,QAAQoJ,yBAA2BhK,EAAQ,CACjF,IAAIiK,EAAoB9K,EAAuB+C,qBAAqBlC,EAAQX,KAAKoB,oBAC7EyJ,EAAqB/K,EAAuBiD,sBAAsBpC,EAAQX,KAAKoB,oBACnF4G,EAAOrD,EAAcA,EAAc,GAAIiG,GAAmB,GAAOC,GAAoB,GAEzF,IAAK7K,KAAKuB,QAAQiH,kBAAoB7H,EAAQ,CAEtCiK,EAAoB9K,EAAuB+C,qBAAqBlC,EAAQX,KAAKoB,oBAC7EpB,KAAKoB,qBAAuBvB,qBAAmB8B,iBAC/CiJ,EAAoBA,EAAkB9H,KAAI,SAAUsF,GAChD,IAAIK,EAAiB3I,EAAuBiC,mBAAmBpB,EAAQyH,GACvE,OAAIK,GAAkBA,EAAelH,SAAWkH,EAAelH,QAAQU,KAC5DwG,EAAelH,QAAQU,KAE3BmG,MAIXJ,EADAhI,KAAKuB,QAAQoJ,wBACNC,EAGA5C,EAAK/D,OAAO2G,GAGvB,IAAIE,EAAuBhL,EAAuBiD,sBAAsBpC,EAAQX,KAAKoB,oBACjF0J,EAAqBlH,OAAS,IAC9BoE,EAAOA,EAAK1G,QAAO,SAAU8G,GACzB,OAAQ0C,EAAqBC,SAAS3C,YAIjB5F,IAAzBxC,KAAKuB,QAAQ4I,UACbnC,EAAOA,EAAK1G,QAAO,SAAU8G,GACzB,IAAIK,EAAiB3I,EAAuBiC,mBAAmBpB,EAAQyH,GACvE,OAAKK,IAAmBA,EAAelH,SAEhCuE,EAAMsE,aAAa3B,EAAelH,QAAQ8I,MAAO5B,EAAelH,QAAQ+I,WAKnFtC,EADAhI,KAAKuB,QAAQgJ,QAAUvK,KAAKuB,QAAQgJ,OAAO3G,OACpCoE,EAAK1G,QAAO,SAAU8G,GACzB,IAAIK,EAAiB3I,EAAuBiC,mBAAmBpB,EAAQyH,GACvE,OAAKK,IAAmBA,EAAelH,SAEhCuE,EAAM0E,YAAY/B,EAAelH,QAAQgJ,WAI7CvC,EAAK1G,QAAO,SAAU8G,GACzB,IAAIK,EAAiB3I,EAAuBiC,mBAAmBpB,EAAQyH,GACvE,QAASK,GACJA,EAAelH,SACfkH,EAAelH,QAAQgJ,QACvB9B,EAAelH,QAAQgJ,OAAO3G,WAgB/C,OAXI5D,KAAKuB,QAAQyJ,iBAAmBhL,KAAKuB,QAAQyJ,gBAAgBpH,SAC7DoE,EAAOA,EAAK1G,QAAO,SAAU8G,GACzB,OAAOtC,EAAMvE,QAAQyJ,gBAAgBC,OAAM,SAAUC,GACjD,OAAO9C,EAAI+C,OAAO,EAAGD,EAAOtH,UAAYsH,SAKpDlD,EAAOA,EAAK1G,QAAO,SAAU8G,EAAK/B,EAAOwB,GACrC,OAAOA,EAAKuD,QAAQhD,KAAS/B,MAIrClB,EAA2B5E,UAAU6J,aAAe,SAAUC,EAAOC,GACjE,IAAIe,GAAW,EAKf,OAJIA,GAAYhB,IACZgB,EAAWrL,KAAKuB,QAAQ4I,SAAWE,GACnCgB,GAAYf,IACZe,EAAWrL,KAAKuB,QAAQ4I,QAAUG,GAC/Be,GAEXlG,EAA2B5E,UAAUiK,YAAc,SAAUD,GACzD,OAAKA,GAEEvK,KAAKuB,QAAQgJ,OAAOe,MAAK,SAAUC,GAAe,OAAOhB,EAAOQ,SAASQ,OAE7EpG,KG5eAqG,EAAiB,CACxBjF,qBAAqB,EACrB0C,0BAA0B,EAC1B0B,yBAAyB,EACzBK,qBAAiBxI,EACjBmH,qBAAqB,EACrBF,mBAAmB,EACnBc,YAAQ/H,EACRgG,kBAAkB,EAClBkC,cAAUlI,EACVuG,gBAAYvG,EACZ2H,aAAS3H,GCdTiJ,EAAsC,WAStC,OARAA,EAAWjH,OAAOkH,QAAU,SAASC,GACjC,IAAK,IAAIC,EAAG5G,EAAI,EAAG6G,EAAI/G,UAAUlB,OAAQoB,EAAI6G,EAAG7G,IAE5C,IAAK,IAAIa,KADT+F,EAAI9G,UAAUE,GACOR,OAAOjE,UAAUuL,eAAe5G,KAAK0G,EAAG/F,KACzD8F,EAAE9F,GAAK+F,EAAE/F,IAEjB,OAAO8F,GAEJF,EAASzH,MAAMhE,KAAM8E,YAK5BiH,EAAkC,WAClC,SAASA,KA2CT,OAzCAA,EAAiBxL,UAAUyL,gBAAkB,SAAUhF,EAAQzF,GAE3D,OADe,IAAI4D,EAA2BtF,qBAAmB+B,eAAgB6J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,eAAU9C,EAAWwE,OAAQxE,OAAWA,OAAWA,OAAWA,IAElFuJ,EAAiBxL,UAAU0L,sBAAwB,SAAUjF,EAAQkF,EAAa3K,GAE9E,OADe,IAAI4D,EAA2BtF,qBAAmB+B,eAAgB6J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,UAAU4G,EAAalF,OAAQxE,OAAWA,OAAWA,OAAWA,IAEpFuJ,EAAiBxL,UAAU4L,gBAAkB,SAAUC,EAAKC,EAAO9K,GAE/D,OADe,IAAI4D,EAA2BtF,qBAAmB8B,eAAgB8J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,eAAU9C,EAAW6J,EAAOD,OAAK5J,OAAWA,OAAWA,IAE3EuJ,EAAiBxL,UAAU+L,sBAAwB,SAAUC,EAAWF,EAAO9K,GAE3E,OADe,IAAI4D,EAA2BtF,qBAAmB8B,eAAgB8J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,UAAUiH,EAAWF,OAAO7J,OAAWA,OAAWA,OAAWA,IAEjFuJ,EAAiBxL,UAAUiM,mBAAqB,SAAUxF,EAAQzF,GAE9D,OADe,IAAI4D,EAA2BtF,qBAAmB6B,eAAgB+J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,eAAU9C,EAAWwE,OAAQxE,OAAWA,OAAWA,OAAWA,IAElFuJ,EAAiBxL,UAAUkM,sBAAwB,SAAUzF,EAAQ0F,EAAYnL,GAE7E,OADe,IAAI4D,EAA2BtF,qBAAmB6B,eAAgB+J,EAASA,EAAS,GAAID,GAAiBjK,IACxG+D,UAAUoH,EAAY1F,OAAQxE,OAAWA,OAAWA,OAAWA,IAEnFuJ,EAAiBxL,UAAUoM,UAAY,SAAU3F,EAAQzF,GACrD,OAAOqL,KAAKC,UAAU7M,KAAKgM,gBAAgBhF,EAAQzF,KAKvDwK,EAAiBxL,UAAUuM,YAAc,SAAUV,EAAKW,EAAMxL,GAC1D,IAAIyL,EAAaJ,KAAKK,MAAMF,GAC5B,OAAO/M,KAAKmM,gBAAgBC,EAAKY,EAAYzL,IAKjDwK,EAAiBxL,UAAU2M,iBAAmB,SAAUd,EAAKW,EAAMxL,GAC/D,IAAIyL,EAAaJ,KAAKK,MAAMF,GAC5B,OAAO/M,KAAKmM,gBAAgBC,EAAKY,EAAYzL,IAE1CwK,KCrDX,IAAIoB,EAAmB,IAAIpB,iCCGpB,SAAiBxK,GAQpB,YAPgB,IAAZA,IAAsBA,EAAU,IAO7B,SAAUyF,EAAQlG,GACrBhB,EAAuBoB,mBAAmB,CACtCP,OAAQqG,aAAkB0B,SAAW1B,EAASA,EAAOtC,YACrD5D,aAAcA,EACdS,QAASA,eCZd,SAAgBA,GAQnB,YAPgB,IAAZA,IAAsBA,EAAU,IAO7B,SAAUyF,EAAQlG,GACrBhB,EAAuBmB,kBAAkB,CACrCN,OAAQqG,aAAkB0B,SAAW1B,EAASA,EAAOtC,YACrD5D,aAAcA,EACdS,QAASA,kBCdd,SAAmBkJ,EAAalJ,GAEnC,YADgB,IAAZA,IAAsBA,EAAU,IAC7B,SAAUZ,EAAQG,GACrBhB,EAAuBiB,qBAAqB,CACxCJ,OAAQA,EAAO+D,YACf5D,aAAcA,EACd2J,YAAaA,EACblJ,QAASA,oCCPd,SAAqC6L,GACxC,OAAO,SAAUzM,EAAQ0M,EAAahE,GAClC,IAAI8D,EAAmB,IAAIpB,EACvBuB,EAAiBjE,EAAW7D,MAChC6D,EAAW7D,MAAQ,WAEf,IADA,IAAI+H,EAAO,GACF9J,EAAK,EAAGA,EAAKqB,UAAUlB,OAAQH,IACpC8J,EAAK9J,GAAMqB,UAAUrB,GAEzB,IAAI+J,EAASF,EAAetJ,MAAMhE,KAAMuN,GACpCE,IAAcD,IAA6B,iBAAXA,GAAyC,mBAAXA,IAAiD,mBAAhBA,EAAOzF,KAC1G,OAAO0F,EACDD,EAAOzF,MAAK,SAAUkC,GAAQ,OAAOkD,EAAiBX,mBAAmBvC,EAAMmD,MAC/ED,EAAiBX,mBAAmBgB,EAAQJ,iCCbvD,SAAkCA,GACrC,OAAO,SAAUzM,EAAQ0M,EAAahE,GAClC,IAAI8D,EAAmB,IAAIpB,EACvBuB,EAAiBjE,EAAW7D,MAChC6D,EAAW7D,MAAQ,WAEf,IADA,IAAI+H,EAAO,GACF9J,EAAK,EAAGA,EAAKqB,UAAUlB,OAAQH,IACpC8J,EAAK9J,GAAMqB,UAAUrB,GAEzB,IAAI+J,EAASF,EAAetJ,MAAMhE,KAAMuN,GACpCE,IAAcD,IAA6B,iBAAXA,GAAyC,mBAAXA,IAAiD,mBAAhBA,EAAOzF,KAC1G,OAAO0F,EACDD,EAAOzF,MAAK,SAAUkC,GAAQ,OAAOkD,EAAiBnB,gBAAgB/B,EAAMmD,MAC5ED,EAAiBnB,gBAAgBwB,EAAQJ,iCCbpD,SAAkCM,EAAWN,GAChD,OAAO,SAAUzM,EAAQ0M,EAAahE,GAClC,IAAI8D,EAAmB,IAAIpB,EACvBuB,EAAiBjE,EAAW7D,MAChC6D,EAAW7D,MAAQ,WAEf,IADA,IAAI+H,EAAO,GACF9J,EAAK,EAAGA,EAAKqB,UAAUlB,OAAQH,IACpC8J,EAAK9J,GAAMqB,UAAUrB,GAEzB,IAAI+J,EAASF,EAAetJ,MAAMhE,KAAMuN,GACpCE,IAAcD,IAA6B,iBAAXA,GAAyC,mBAAXA,IAAiD,mBAAhBA,EAAOzF,KAC1G,OAAO0F,EACDD,EAAOzF,MAAK,SAAUkC,GAAQ,OAAOkD,EAAiBhB,gBAAgBuB,EAAWzD,EAAMmD,MACvFD,EAAiBhB,gBAAgBuB,EAAWF,EAAQJ,aCZ/D,SAAclG,EAAc3F,GAE/B,YADgB,IAAZA,IAAsBA,EAAU,IAC7B,SAAUZ,EAAQG,GACrB,IAAIgI,EAAgBI,QAAQvG,YAAY,cAAehC,EAAQG,GAC/DhB,EAAuBU,gBAAgB,CACnCG,OAAQA,EAAO+D,YACf5D,aAAcA,EACdgI,cAAeA,EACf5B,aAAcA,EACd3F,QAASA,8BPWd,SAA+ByF,EAAQ0F,EAAYnL,GACtD,OAAO4L,EAAiBV,sBAAsBzF,EAAQ0F,EAAYnL,mBAtB/D,SAAsByF,EAAQzF,GACjC,OAAO4L,EAAiBnB,gBAAgBhF,EAAQzF,4BAK7C,SAA+ByF,EAAQkF,EAAa3K,GACvD,OAAO4L,EAAiBlB,sBAAsBjF,EAAQkF,EAAa3K,kBA4BhE,SAAqB6K,EAAKW,EAAMxL,GACnC,OAAO4L,EAAiBL,YAAYV,EAAKW,EAAMxL,uBAW5C,SAA0B6K,EAAKW,EAAMxL,GACxC,OAAO4L,EAAiBD,iBAAiBd,EAAKW,EAAMxL,yBA9BjD,SAA4ByF,EAAQzF,GACvC,OAAO4L,EAAiBX,mBAAmBxF,EAAQzF,sBAhBhD,SAAyByF,EAAQzF,GACpC,OAAO4L,EAAiBnB,gBAAgBhF,EAAQzF,mBAK7C,SAAsB6K,EAAKC,EAAO9K,GACrC,OAAO4L,EAAiBhB,gBAAgBC,EAAKC,EAAO9K,4BAKjD,SAA+BgL,EAAWF,EAAO9K,GACpD,OAAO4L,EAAiBb,sBAAsBC,EAAWF,EAAO9K,sBAJ7D,SAAyB6K,EAAKC,EAAO9K,GACxC,OAAO4L,EAAiBhB,gBAAgBC,EAAKC,EAAO9K,gBAWjD,SAAmByF,EAAQzF,GAC9B,OAAO4L,EAAiBR,UAAU3F,EAAQzF"}