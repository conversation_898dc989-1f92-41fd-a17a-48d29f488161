{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,cAAc,cAAc,CAAC;AAC7B,cAAc,cAAc,CAAC;AAC7B,cAAc,SAAS,CAAC;AAExB,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAShD,MAAM,UAAU,YAAY,CAC1B,MAAe,EACf,OAA+B;IAE/B,OAAO,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AAOD,MAAM,UAAU,eAAe,CAC7B,MAAe,EACf,OAA+B;IAE/B,OAAO,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3D,CAAC;AAmBD,MAAM,UAAU,qBAAqB,CACnC,MAAS,EACT,WAAwD,EACxD,OAA+B;IAE/B,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,MAAM,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AAC9E,CAAC;AASD,MAAM,UAAU,YAAY,CAAO,GAAwB,EAAE,KAAc,EAAE,OAA+B;IAC1G,OAAO,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE,KAAY,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAOD,MAAM,UAAU,eAAe,CAC7B,GAAwB,EACxB,KAAc,EACd,OAA+B;IAE/B,OAAO,gBAAgB,CAAC,eAAe,CAAC,GAAG,EAAE,KAAY,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAWD,MAAM,UAAU,qBAAqB,CAAO,SAAY,EAAE,KAAc,EAAE,OAA+B;IACvG,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3E,CAAC;AAOD,MAAM,UAAU,kBAAkB,CAAI,MAAe,EAAE,OAA+B;IACpF,OAAO,gBAAgB,CAAC,kBAAkB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC9D,CAAC;AAWD,MAAM,UAAU,qBAAqB,CAAI,MAAS,EAAE,UAAmB,EAAE,OAA+B;IACtG,OAAO,gBAAgB,CAAC,qBAAqB,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC7E,CAAC;AAYD,MAAM,UAAU,SAAS,CAAI,MAAe,EAAE,OAA+B;IAC3E,OAAO,gBAAgB,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,WAAW,CAAI,GAAwB,EAAE,IAAY,EAAE,OAA+B;IACpG,OAAO,gBAAgB,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC1D,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,gBAAgB,CAAI,GAAwB,EAAE,IAAY,EAAE,OAA+B;IACzG,OAAO,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AAC/D,CAAC", "sourcesContent": ["import { ClassTransformer } from './ClassTransformer';\nimport { ClassTransformOptions } from './interfaces';\nimport { ClassConstructor } from './interfaces';\n\nexport { ClassTransformer } from './ClassTransformer';\nexport * from './decorators';\nexport * from './interfaces';\nexport * from './enums';\n\nconst classTransformer = new ClassTransformer();\n\n/**\n * Converts class (constructor) object to plain (literal) object. Also works with arrays.\n *\n * @deprecated Function name changed, use the `instanceToPlain` method instead.\n */\nexport function classToPlain<T>(object: T, options?: ClassTransformOptions): Record<string, any>;\nexport function classToPlain<T>(object: T[], options?: ClassTransformOptions): Record<string, any>[];\nexport function classToPlain<T>(\n  object: T | T[],\n  options?: ClassTransformOptions\n): Record<string, any> | Record<string, any>[] {\n  return classTransformer.instanceToPlain(object, options);\n}\n\n/**\n * Converts class (constructor) object to plain (literal) object. Also works with arrays.\n */\nexport function instanceToPlain<T>(object: T, options?: ClassTransformOptions): Record<string, any>;\nexport function instanceToPlain<T>(object: T[], options?: ClassTransformOptions): Record<string, any>[];\nexport function instanceToPlain<T>(\n  object: T | T[],\n  options?: ClassTransformOptions\n): Record<string, any> | Record<string, any>[] {\n  return classTransformer.instanceToPlain(object, options);\n}\n\n/**\n * Converts class (constructor) object to plain (literal) object.\n * Uses given plain object as source object (it means fills given plain object with data from class object).\n * Also works with arrays.\n *\n * @deprecated This function is being removed.\n */\nexport function classToPlainFromExist<T>(\n  object: T,\n  plainObject: Record<string, any>,\n  options?: ClassTransformOptions\n): Record<string, any>;\nexport function classToPlainFromExist<T>(\n  object: T,\n  plainObjects: Record<string, any>[],\n  options?: ClassTransformOptions\n): Record<string, any>[];\nexport function classToPlainFromExist<T>(\n  object: T,\n  plainObject: Record<string, any> | Record<string, any>[],\n  options?: ClassTransformOptions\n): Record<string, any> | Record<string, any>[] {\n  return classTransformer.classToPlainFromExist(object, plainObject, options);\n}\n\n/**\n * Converts plain (literal) object to class (constructor) object. Also works with arrays.\n *\n * @deprecated Function name changed, use the `plainToInstance` method instead.\n */\nexport function plainToClass<T, V>(cls: ClassConstructor<T>, plain: V[], options?: ClassTransformOptions): T[];\nexport function plainToClass<T, V>(cls: ClassConstructor<T>, plain: V, options?: ClassTransformOptions): T;\nexport function plainToClass<T, V>(cls: ClassConstructor<T>, plain: V | V[], options?: ClassTransformOptions): T | T[] {\n  return classTransformer.plainToInstance(cls, plain as any, options);\n}\n\n/**\n * Converts plain (literal) object to class (constructor) object. Also works with arrays.\n */\nexport function plainToInstance<T, V>(cls: ClassConstructor<T>, plain: V[], options?: ClassTransformOptions): T[];\nexport function plainToInstance<T, V>(cls: ClassConstructor<T>, plain: V, options?: ClassTransformOptions): T;\nexport function plainToInstance<T, V>(\n  cls: ClassConstructor<T>,\n  plain: V | V[],\n  options?: ClassTransformOptions\n): T | T[] {\n  return classTransformer.plainToInstance(cls, plain as any, options);\n}\n\n/**\n * Converts plain (literal) object to class (constructor) object.\n * Uses given object as source object (it means fills given object with data from plain object).\n *  Also works with arrays.\n *\n * @deprecated This function is being removed. The current implementation is incorrect as it modifies the source object.\n */\nexport function plainToClassFromExist<T, V>(clsObject: T[], plain: V[], options?: ClassTransformOptions): T[];\nexport function plainToClassFromExist<T, V>(clsObject: T, plain: V, options?: ClassTransformOptions): T;\nexport function plainToClassFromExist<T, V>(clsObject: T, plain: V | V[], options?: ClassTransformOptions): T | T[] {\n  return classTransformer.plainToClassFromExist(clsObject, plain, options);\n}\n\n/**\n * Converts class (constructor) object to new class (constructor) object. Also works with arrays.\n */\nexport function instanceToInstance<T>(object: T, options?: ClassTransformOptions): T;\nexport function instanceToInstance<T>(object: T[], options?: ClassTransformOptions): T[];\nexport function instanceToInstance<T>(object: T | T[], options?: ClassTransformOptions): T | T[] {\n  return classTransformer.instanceToInstance(object, options);\n}\n\n/**\n * Converts class (constructor) object to plain (literal) object.\n * Uses given plain object as source object (it means fills given plain object with data from class object).\n * Also works with arrays.\n *\n * @deprecated This function is being removed. The current implementation is incorrect as it modifies the source object.\n */\nexport function classToClassFromExist<T>(object: T, fromObject: T, options?: ClassTransformOptions): T;\nexport function classToClassFromExist<T>(object: T, fromObjects: T[], options?: ClassTransformOptions): T[];\nexport function classToClassFromExist<T>(object: T, fromObject: T | T[], options?: ClassTransformOptions): T | T[] {\n  return classTransformer.classToClassFromExist(object, fromObject, options);\n}\n\n/**\n * Serializes given object to a JSON string.\n *\n * @deprecated This function is being removed. Please use\n * ```\n * JSON.stringify(instanceToPlain(object, options))\n * ```\n */\nexport function serialize<T>(object: T, options?: ClassTransformOptions): string;\nexport function serialize<T>(object: T[], options?: ClassTransformOptions): string;\nexport function serialize<T>(object: T | T[], options?: ClassTransformOptions): string {\n  return classTransformer.serialize(object, options);\n}\n\n/**\n * Deserializes given JSON string to a object of the given class.\n *\n * @deprecated This function is being removed. Please use the following instead:\n * ```\n * instanceToClass(cls, JSON.parse(json), options)\n * ```\n */\nexport function deserialize<T>(cls: ClassConstructor<T>, json: string, options?: ClassTransformOptions): T {\n  return classTransformer.deserialize(cls, json, options);\n}\n\n/**\n * Deserializes given JSON string to an array of objects of the given class.\n *\n * @deprecated This function is being removed. Please use the following instead:\n * ```\n * JSON.parse(json).map(value => instanceToClass(cls, value, options))\n * ```\n *\n */\nexport function deserializeArray<T>(cls: ClassConstructor<T>, json: string, options?: ClassTransformOptions): T[] {\n  return classTransformer.deserializeArray(cls, json, options);\n}\n"]}