{"version": 3, "file": "transform-metadata.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/metadata/transform-metadata.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TransformOptions } from '..';\nimport { TransformFnParams } from './transform-fn-params.interface';\n\n/**\n * This object represents metadata assigned to a property via the @Transform decorator.\n */\nexport interface TransformMetadata {\n  target: Function;\n\n  /**\n   * The property name this metadata belongs to on the target (property only).\n   */\n  propertyName: string;\n\n  /**\n   * The custom transformation function provided by the user in the @Transform decorator.\n   */\n  transformFn: (params: TransformFnParams) => any;\n\n  /**\n   * Options passed to the @Transform operator for this property.\n   */\n  options: TransformOptions;\n}\n"]}