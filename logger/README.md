# logger - 三端统一日志库

## 🎯 库定位和职责

logger是三端统一日志架构中的**核心逻辑层**，使用Rust实现，为Android、iOS、HarmonyOS提供统一的日志处理逻辑。

### 本库(logger)在整体架构中的位置
这是一个为Android、iOS、HarmonyOS三端提供统一日志功能的Rust库，作为整个日志系统的核心逻辑层。

**重要说明**: 本库是三端通用的，不仅用于HarmonyOS，也用于Android和iOS项目。

### 核心职责
- ✅ **日志输出和处理**: 统一的日志级别控制和格式化
- ✅ **文件存储管理**: 基于纯Rust mmap的高性能文件存储 🆕
- ✅ **隐私信息脱敏**: 统一的敏感信息处理规则
- ✅ **大文件自动检测**: 文件超过限制时自动通知前端
- ✅ **按需压缩**: 提供日志压缩接口供前端上传
- ✅ **文件清理**: 上传后的文件清理管理
- ❌ **网络上传**: 由前端负责，保持库的职责单一

## 🏗️ 三端统一架构

### **Android架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Android App   │───▶│   RustChannel   │───▶│     logger      │
│   (Java/Kotlin) │    │   (桥接层)      │    │   (本库)        │
│                 │    │                 │    │                 │
│ • RustLogger    │    │ • FlatBuffers   │    │ • 统一日志逻辑  │
│ • 业务逻辑      │    │ • 数据序列化    │    │ • mmap文件管理  │
│ • UI交互        │    │ • JNI调用       │    │ • 隐私处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **iOS架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    iOS App      │───▶│   RustUplus     │───▶│     logger      │
│ (Swift/ObjC)    │    │   (桥接层)      │    │   (本库)        │
│                 │    │                 │    │                 │
│ • RustLogger    │    │ • FlatBuffers   │    │ • 统一日志逻辑  │
│ • 业务逻辑      │    │ • 数据序列化    │    │ • mmap文件管理  │
│ • UI交互        │    │ • C接口调用     │    │ • 隐私处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **HarmonyOS架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  HarmonyOS App  │───▶│   RustChannel   │───▶│     logger      │
│   (ArkTS)       │    │   (桥接层)      │    │   (本库)        │
│                 │    │                 │    │                 │
│ • RustLogger    │    │ • FlatBuffer    │    │ • 统一日志逻辑  │
│ • 业务逻辑      │    │ • 异步调用      │    │ • mmap文件管理  │
│ • UI交互        │    │ • 大文件回调    │    │ • 隐私处理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 本库(logger)的核心职责
作为统一逻辑层，logger库负责所有的日志业务逻辑：

1. **日志格式化**: 生成包含固定头部信息的统一格式
2. **文件存储管理**: 文件轮转、大小控制、清理策略
3. **隐私信息处理**: 敏感数据脱敏和保护
4. **大文件检测和通知**: 文件超限时通知前端处理
5. **按需压缩**: 提供日志压缩接口供前端上传使用
6. **配置管理**: 动态配置更新和持久化
7. **跨平台适配**: Android、iOS、HarmonyOS的平台差异处理

### logger库结构说明

**本库是完整的三端统一日志解决方案：**

- **rust_logger/**: Rust核心代码，CI自动构建
- **androidApp/rust_logger/**: Android平台产物，纯Rust实现 + Kotlin API封装
- **iosApp/rust_logger/**: iOS平台产物，纯Rust实现 + Swift API封装
- **ohosApp/rust_logger/**: HarmonyOS平台产物，纯Rust实现 + ArkTS API封装

**架构特点：**
- 完全独立的三端统一日志库
- 纯Rust mmap高性能文件管理
- 基于RustChannel架构的桥接层设计
- CI友好的目录结构，支持自动构建和打包

### 桥接层详细说明

**RustChannel (Android桥接层):**
- 提供统一的`callRustMethod(libName, action, params)`接口
- 自动处理FlatBuffers数据序列化和反序列化
- 管理JNI调用和内存安全
- 统一的错误处理和结果封装

**RustUplus (iOS桥接层):**
- 提供`ios_flat_cross_platform(libName, jsonString)`接口
- 支持FlatBuffers数据格式验证和解析
- 管理C接口调用和内存管理
- 集成iOS日志系统(uplog)

**RustChannel (HarmonyOS桥接层):**
- 提供统一的`getRustBuffer(libName, params)`和`getRustBufferAsync(libName, params, callback)`接口
- 支持`manageRustBufferListenerWithData`事件监听机制
- 使用FlatBuffer数据序列化格式，与Android/iOS保持一致
- 自动处理大文件回调通知和异步调用

### 三端统一调用方式

**Android:**
```kotlin
val config = LoggerConfig()
val result = RustLogger.initialize(context, config)
RustLogger.writeLog(LogLevel.INFO, "tag", "message")
```

**iOS:**
```swift
let config = RustLoggerConfig()
let result = RustLogger.initialize(config: config)
RustLogger.writeLog(level: .info, tag: "tag", message: "message")
```

**HarmonyOS:**
```typescript
const config = new LoggerConfig()
RustLogger.initialize(context, config)  // 异步执行
RustLogger.writeLog(LogLevel.INFO, "tag", "message")  // 异步执行

// 新增的调试接口
RustLogger.enableConsoleLog(true)   // 启用控制台日志
RustLogger.enableFullLogs(true)     // 启用完整日志模式
```

## 🎯 为什么需要本库？

### 三端桥接架构的必要性
- **Android**: 通过**RustChannel桥接层**调用本库，使用FlatBuffers数据格式
- **iOS**: 通过**RustUplus桥接层**调用本库，使用FlatBuffers数据格式
- **HarmonyOS**: 通过**RustChannel桥接层**调用本库，使用FlatBuffers数据格式和事件监听机制

### 桥接层的作用
- **数据序列化**: 将平台数据转换为Rust可识别的格式
- **接口适配**: 提供平台特定的调用接口
- **错误处理**: 统一的错误码和消息转换
- **内存管理**: 安全的跨语言内存管理

### 统一逻辑的优势
- 保证三端日志格式完全一致
- 统一的隐私处理策略
- 统一的文件管理和压缩逻辑
- 统一的大文件检测和通知机制
- 减少重复开发和维护成本
- CI自动构建，平台产物包含第三方库集成

## 📁 模块结构

### **主要Rust代码 (rust_logger/src/)**
```
src/
├── lib.rs              # 库入口和模块导出
├── config.rs           # 配置管理
├── core.rs             # 核心日志器和日志级别
├── formatter.rs        # 日志格式化（固定头部信息）
├── storage.rs          # 文件存储和管理
├── mmap_storage.rs     # 纯Rust mmap存储实现 🆕
├── privacy.rs          # 隐私信息处理和脱敏
├── protobuf.rs         # Protobuf序列化（统一格式）
├── compression.rs      # 日志压缩功能（供前端上传使用）
├── processor.rs        # 日志处理链
├── ffi.rs              # C FFI接口（供各平台调用）
└── error.rs            # 错误处理
```

### **平台产物结构**
```
androidApp/rust_logger/     # Android平台产物
├── src/main/java/.../rust/logger/  # Kotlin API封装
└── build.gradle.kts        # 构建配置

iosApp/rust_logger/         # iOS平台产物
├── *.swift                 # Swift API封装
└── RustLogger.podspec      # CocoaPods配置

ohosApp/rust_logger/        # HarmonyOS平台产物
├── src/main/ets/api/       # ArkTS API封装
├── Index.ets               # 模块导出
└── oh-package.json5        # 包配置
```

## 🔧 主要功能

### 1. 纯Rust mmap文件管理 (storage.rs + mmap_storage.rs) 🆕

**新架构说明：**
- 使用纯Rust实现的mmap存储系统，无外部依赖
- 提供高性能的内存映射文件写入，避免系统调用开销
- 生成未压缩的.log文件，便于后端日志解析
- 参考xlog的存储逻辑和缓冲区管理策略
- 完全跨平台支持，无需平台特定的库文件

**mmap存储功能特性：**
- **高性能写入**: 150KB内存映射缓冲区，减少系统调用
- **智能flush**: 50KB阈值自动flush，参考xlog设计
- **直接protobuf写入**: 无需hex编码，直接写入二进制数据
- **线程安全**: 使用Mutex保护共享状态
- **自动文件管理**: 按日期命名，自动轮转和清理
- **无压缩存储**: 生成.log文件，便于后端解析和调试

**技术优势：**
- **无外部依赖**: 不依赖C++库，减少集成复杂度
- **更好的调试**: 未压缩文件便于开发调试和问题排查
- **统一实现**: 三端使用完全相同的存储逻辑
- **性能优化**: mmap减少内存拷贝，提升写入性能

### 2. 日志格式化 (formatter.rs)

**实际日志格式：**
```
[时间戳][sessionID][deviceID][模块信息][LogLevel][TestMode][userId][日志信息]
```

**各字段说明：**
- 时间戳: `[2024-01-01 12:00:00.123 UTC]`
- 会话ID: `[uuid-session-id]` (自动生成)
- 设备ID: `[device123]` (由外部传入，支持三端)
- 模块信息: `[MyTag]` (对应日志标签)
- 日志级别: `[INFO]`
- 测试模式: `[正常模式]` 或 `[测试模式]`
- 用户ID: `[user123]` (由外部传入)
- 日志信息: `User login successful: john_doe` (处理后的消息和参数)

**示例输出：**
```
[2024-01-01 12:00:00.123 UTC][abc-123-uuid][device123][MyTag][INFO][正常模式][user123][User login successful: john_doe]
```

### 3. Protobuf序列化 (protobuf.rs) 🆕

**与Android兼容的protobuf格式：**
- 使用与Android相同的LogMetaData protobuf结构
- 支持所有必要字段：time, session_id, tag, level, test_mode, user_id, log_message
- 带长度前缀的序列化格式，确保与Android完全兼容
- 高效的二进制存储，比JSON格式更紧凑

**关键特性：**
- **服务端兼容**：确保服务端能正确反序列化日志数据
- **网页版支持**：保证网页版日志展示功能正常工作
- **自动序列化**：日志写入时自动使用protobuf格式存储
- **向后兼容**：同时支持文本格式的向后兼容

**protobuf结构：**
```protobuf
message LogMetaData {
  string time = 1;        // 时间戳
  string session_id = 2;  // 会话ID
  string tag = 3;         // 日志标签
  string level = 4;       // 日志级别
  string test_mode = 5;   // 测试模式
  string user_id = 6;     // 用户ID
  string log_message = 7; // 日志消息
}
```

### 4. 文件存储管理 (storage.rs + mmap_storage.rs) 🆕

**功能特性：**
- 单文件大小限制（默认20MB，与Android一致）
- 目录总大小限制（默认600MB，与Android一致）
- 自动文件轮转
- 旧文件清理策略
- **mmap缓冲区管理**：150KB内存映射缓冲区，50KB自动flush
- **Protobuf格式存储**：直接写入protobuf二进制数据
- **线程安全存储**：使用Mutex保护并发访问

**文件命名规则：**
```
test_log_20240101.log
test_log_20240102.log
```

**mmap存储架构：**
```rust
pub struct MmapLogStorage {
    config: StorageConfig,           // 存储配置
    mmap_buffer: MmapMut,           // 150KB内存映射缓冲区
    buffer_used: usize,             // 已使用缓冲区大小
    is_closed: bool,                // 存储状态
}

pub struct ThreadSafeMmapStorage {
    inner: Arc<Mutex<MmapLogStorage>>, // 线程安全包装
}
```

**存储流程：**
1. **写入数据**: 直接写入mmap缓冲区，避免系统调用
2. **阈值检查**: 超过50KB时自动flush到磁盘
3. **文件轮转**: 按日期自动创建新文件
4. **缓冲区管理**: 150KB循环使用，高效内存管理

### 4.1. 纯Rust mmap存储系统详解 🆕

**设计理念：**
- **纯Rust实现**: 避免C++依赖和压缩问题
- **参考xlog设计**: 保持相同的缓冲区大小和flush阈值
- **便于调试**: 生成未压缩的.log文件，便于后端解析
- **高性能**: mmap内存映射减少系统调用开销

**核心组件：**

#### **StorageConfig - 存储配置**
```rust
pub struct StorageConfig {
    pub log_directory: PathBuf,      // 日志目录
    pub cache_directory: PathBuf,    // 缓存目录（mmap文件）
    pub log_file_prefix: String,     // 文件前缀
    pub max_buffer_size: usize,      // 缓冲区大小（150KB）
    pub flush_threshold: usize,      // flush阈值（50KB）
}
```

#### **MmapLogStorage - 核心存储引擎**
```rust
impl MmapLogStorage {
    pub fn write_protobuf(&mut self, data: &[u8]) -> Result<()>  // 写入protobuf数据
    pub fn flush_to_file(&mut self) -> Result<()>               // 手动flush到文件
    pub fn should_flush(&self) -> bool                          // 检查是否需要flush
    pub fn buffer_usage(&self) -> (usize, usize)               // 获取缓冲区使用情况
}
```

#### **ThreadSafeMmapStorage - 线程安全包装**
```rust
impl ThreadSafeMmapStorage {
    pub fn new(config: StorageConfig) -> Result<Self>           // 创建存储实例
    pub fn write_protobuf(&self, data: &[u8]) -> Result<()>    // 线程安全写入
    pub fn flush_to_file(&self) -> Result<()>                  // 线程安全flush
    pub fn should_flush(&self) -> bool                         // 线程安全检查
}
```

**工作原理：**
1. **初始化**: 在缓存目录创建150KB的mmap文件
2. **写入**: 直接写入内存映射区域，无需系统调用
3. **自动flush**: 达到50KB阈值时自动flush到日志文件
4. **文件管理**: 按日期创建.log文件，便于管理和解析

**性能优势：**
- **零拷贝**: mmap避免用户态到内核态的数据拷贝
- **批量写入**: 缓冲区积累数据后批量写入磁盘
- **减少系统调用**: 大幅减少write()系统调用次数
- **内存效率**: 150KB固定缓冲区，内存使用可控

**技术对比：**
| 特性 | 传统方案 | 纯Rust mmap |
|------|----------|-------------|
| 依赖 | 外部C++库 | 纯Rust |
| 压缩 | 压缩存储 | 无压缩 |
| 文件格式 | 二进制 | .log |
| 调试友好 | 需解压 | 直接查看 |
| 集成复杂度 | 高 | 低 |
| 性能 | 高 | 高 |

### 5. 隐私信息处理 (privacy.rs)

**与Android一致的脱敏规则：**
- 手机号: `138****1234`
- 身份证: `123456********1234`
- 邮箱: `j***@example.com`
- 银行卡: `1234****5678`
- IP地址: `192.168.*.*`
- 密码相关: `password:***`

**性能优化策略：**
- 使用高效的正则表达式引擎，避免Android端的性能问题
- 预编译正则模式，减少运行时开销
- 智能缓存机制，避免重复脱敏相同内容
- 可配置的脱敏级别，平衡安全性和性能

**敏感信息标记处理：**
- 处理来自桥接层的敏感性标记
- 对标记为敏感的参数进行强制脱敏
- 自动检测未标记但包含敏感模式的内容

### 6. 日志压缩和文件管理 (compression.rs)

**压缩功能实现：**

#### **核心功能：**
- **ZIP压缩功能** - 支持7天日志、异常日志、单文件压缩
- **文件清理逻辑** - 提供上传成功/失败后的清理接口
- **大文件检测** - 文件超过限制时自动通知前端

#### **职责分离：**
- **Rust层职责** - 日志压缩、文件管理、大文件检测
- **前端职责** - 网络上传、进度显示、重试机制、错误处理

#### **工作流程：**
1. **大文件检测** - 文件超过`max_file_size`时通知前端
2. **按需压缩** - 前端调用压缩接口获取ZIP文件路径
3. **前端上传** - 前端负责网络传输和进度显示
4. **文件清理** - 上传完成后调用清理接口删除文件

### 7. 版本号目录支持

**Android对齐功能：**
- **版本号目录** - 支持setVersionName()设置版本号作为一级目录
- **动态目录切换** - 运行时可以更改版本号并重新初始化存储目录
- **目录结构** - `/logs/{version_name}/uplog_*.log`

### 8. 日志处理链 (processor.rs)

**责任链模式实现：**
1. **条件过滤处理器** - 根据隐私协议状态过滤日志
2. **脱敏处理器** - 手机号、身份证、邮箱自动脱敏（与Android一致，优化性能）
3. **格式化处理器** - 统一日志格式，处理用户ID、设备ID、会话ID等元数据
4. **长度限制处理器** - 防止单条日志过长
5. **可扩展架构** - 易于添加新的处理器

**文件处理规则（对齐Android）：**

**主动压缩：**
- 用户点击"上传日志"按钮触发
- Rust层压缩当前时间点前7天内的日志文件为ZIP包
- 前端负责上传至服务器，带有进度显示

**启动检查：**
- 每次启动时前端检查是否有未上传成功的ZIP包，有的话后台静默上传
- 对应Android的`checkNeedToUploadLogFiles()`

**异常检查：**
- 前端检查是否有异常日志，有的话调用Rust层压缩后上传
- 上传成功：调用`cleanupAfterUploadSuccess`删除原始异常日志和压缩包
- 上传失败：调用`cleanupAfterUploadFailure`只删除压缩包，保留原始文件
- 对应Android的`checkExceptionForUpload()`

**大文件自动通知：**
- 开启全量日志时，文件一旦超过20MB自动通知前端
- 前端可选择压缩上传或忽略继续写入
- 对应Android的`processWhenFullMaxSize()`

**文件清理机制：**
- 前端上传成功后调用`cleanupAfterUploadSuccess`清理文件
- 前端上传失败后调用`cleanupAfterUploadFailure`保留原始文件
- 重试机制由前端实现，Rust层只负责文件管理

### 9. 配置管理 (config.rs)

**支持的配置：**
```rust
pub struct LoggerConfig {
    // 基础配置
    pub log_level: LogLevel,
    pub enable_console_output: bool,  // 控制台输出开关（对应Android的enableConsoleLog）
    pub enable_full_log: bool,        // 全量日志开关（对应Android的enableFullLogs）

    // 用户信息
    pub user_id: Option<String>,
    pub device_id: Option<String>,

    // 隐私配置
    pub privacy_agreed: bool,

    // 文件配置
    pub max_file_size: u64,           // 单个文件大小限制，超过时自动通知前端（默认20MB）
    pub max_directory_size: u64,
    pub log_file_prefix: String,
    pub log_directory: String,

    // 格式化配置
    pub custom_prefix: String,

    // 性能配置
    pub max_log_length: usize,
    pub max_logs_per_second: u32,
    pub buffer_size: usize,

    // 其他配置
    pub test_mode: bool,
    pub log_env: LogEnv,
    pub disable_sensitive_words: bool,
    pub session_id: Option<String>,
    pub app_version: Option<String>,
    pub is_debug_mode: bool,
    pub version_name: Option<String>,
}
```

**重要变更说明：**
- ❌ 移除了 `enable_file_output`：Android没有此配置，文件输出是默认行为
- ❌ 移除了所有上传相关配置：`upload_url`、`max_retry_count`、`upload_timeout_seconds`、`max_zip_file_size`
- ✅ 保留了 `enable_console_output`：对应Android的 `enableConsoleLog()`
- ✅ 保留了 `enable_full_log`：对应Android的 `enableFullLogs()`
- 📝 **架构变更**：网络上传功能移至前端实现，Rust层只负责文件管理和压缩

### 10. 调试和开发接口

**新增的调试接口（与Android保持一致）：**

#### **enableConsoleLog(enable: boolean)**
- **功能**: 控制是否在控制台输出日志
- **实现**: 修改`LoggerConfig.enable_console_output`字段
- **调用方式**: 异步执行，无返回值

#### **enableFullLogs(enable: boolean)**
- **功能**: 启用完整日志模式
- **实现**:
  - 启用时：设置日志级别为DEBUG，启用文件输出和控制台输出
  - 禁用时：恢复日志级别为INFO
- **调用方式**: 异步执行，无返回值

**接口一致性：**
这些接口与Android版本的`enableConsoleLog()`和`enableFullLogs()`方法完全对应，确保三端接口的一致性。

## 🔌 FFI接口 (ffi.rs)

### RustChannel统一接口

**HarmonyOS通过RustChannel调用：**
```rust
// 主要入口函数
pub fn lib_logger_cross_platform(params: HashMap<String, String>) -> Vec<u8>

// 事件监听函数（大文件回调）
pub fn lib_logger_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + 'static,
) -> Vec<u8>
```

**支持的action：**
- `initialize` - 初始化日志器
- `write_log` - 写入日志
- `update_user_id` - 更新用户ID
- `write_crash_log` - 写入崩溃日志
- `compress_logs_for_upload` - 压缩日志文件
- `compress_crash_logs` - 压缩崩溃日志
- `cleanup_after_upload_success` - 上传成功后清理
- `cleanup_after_upload_failure` - 上传失败后清理
- `add_large_file_observer` - 添加大文件观察者
- `enable_console_log` - 启用/禁用控制台日志输出（新增）
- `enable_full_logs` - 启用/禁用完整日志模式（新增）

**大文件回调机制：**
1. HarmonyOS前端调用`setLargeFileCallback()`注册监听器
2. 使用`manageRustBufferListenerWithData`建立事件通道
3. Rust层检测到大文件时通过`notify_large_file_event()`发送事件
4. 前端接收事件并调用用户设置的回调函数

### 供三端平台调用的C接口：

```c
// 初始化
bool up_rust_logger_initialize(const char* config_json);

// 写入日志
void up_rust_logger_write_log(const char* log_entry_json);

// 动态配置
void up_rust_logger_update_user_id(const char* user_id);
void up_rust_logger_set_privacy_agreed(bool agreed);
void up_rust_logger_set_full_log(bool enable);
void up_rust_logger_set_log_level(int level);
int up_rust_logger_get_log_level();

// 调试接口（新增）
void up_rust_logger_enable_console_log(bool enable);  // 启用/禁用控制台日志
void up_rust_logger_enable_full_logs(bool enable);    // 启用/禁用完整日志模式

// 大文件通知回调
typedef void (*LargeFileNotifyCallback)(const char* file_path);
void up_rust_logger_set_large_file_callback(LargeFileNotifyCallback callback);

// 日志压缩和清理（替代上传功能）
char* up_rust_logger_compress_logs_for_upload(uint32_t days);     // 压缩指定天数的日志
char* up_rust_logger_compress_crash_logs();                      // 压缩崩溃日志
bool up_rust_logger_cleanup_after_upload_success(const char* zip_path); // 上传成功后清理
bool up_rust_logger_cleanup_after_upload_failure(const char* zip_path); // 上传失败后清理

// 崩溃日志
bool up_rust_logger_write_crash_log(const char* crash_info);

// 版本号目录支持
bool up_rust_logger_set_version_name(const char* version_name);

// 状态查询
bool up_rust_logger_is_initialized();
char* up_rust_logger_get_config();
void up_rust_logger_free_string(char* ptr);
```

### 新的架构设计

**职责分离：**
- **Rust层**: 日志处理、文件存储、大文件检测、按需压缩、文件清理
- **前端层**: 网络上传、进度显示、重试机制、错误处理

**工作流程：**
1. **大文件检测**: 文件超过 `max_file_size` 时自动触发回调通知前端
2. **按需压缩**: 前端调用压缩接口获取ZIP文件路径
3. **网络上传**: 前端负责网络传输和进度显示
4. **文件清理**: 上传完成后调用清理接口删除文件

### FlatBuffer数据格式：

**HarmonyOS使用FlatBuffer格式进行数据传输，与Android/iOS保持一致：**

**配置数据：**
- 使用LoggerConfig FlatBuffer结构
- 包含所有必要的配置字段
- 支持嵌套结构和可选字段

**日志条目数据：**
- 使用LogEntry FlatBuffer结构
- 高效的二进制序列化
- 支持参数数组和敏感性标记

**响应数据：**
- 使用LoggerFlat FlatBuffer结构
- 统一的成功/失败响应格式
- 支持不同类型的返回数据（字符串、布尔值、整数等）

**三端平台接口示例：**

**Android:**
```kotlin
// 设置大文件回调
RustLogger.setLargeFileCallback { filePath ->
    val zipResult = RustLogger.compressLogsForUpload(7)
    if (zipResult.success) {
        uploadFile(zipResult.data) { success ->
            if (success) {
                RustLogger.cleanupAfterUploadSuccess(zipResult.data)
            } else {
                RustLogger.cleanupAfterUploadFailure(zipResult.data)
            }
        }
    }
}
```

**iOS:**
```swift
// 设置代理
RustLogger.setDelegate(self)

func onLargeFileDetected(filePath: String) {
    let zipResult = RustLogger.compressLogsForUpload(days: 7)
    if zipResult.success {
        uploadFile(zipResult.data) { success in
            if success {
                RustLogger.cleanupAfterUploadSuccess(zipPath: zipResult.data)
            } else {
                RustLogger.cleanupAfterUploadFailure(zipPath: zipResult.data)
            }
        }
    }
}
```

**HarmonyOS:**
```typescript
// 设置回调
RustLogger.setLargeFileCallback({
  onLargeFileDetected: (filePath: string) => {
    const zipResult = RustLogger.compressLogsForUpload(7);
    if (zipResult.success) {
      uploadFile(zipResult.data, (success: boolean) => {
        if (success) {
          RustLogger.cleanupAfterUploadSuccess(zipResult.data);
        } else {
          RustLogger.cleanupAfterUploadFailure(zipResult.data);
        }
      });
    }
  }
});
```

## 🚀 构建和集成

### 构建配置

**Cargo.toml特性：**
```toml
[features]
default = []
# 网络上传功能已移除，由前端实现
# 使用纯Rust mmap存储，无外部依赖

[lib]
crate-type = ["cdylib", "staticlib"]  # 支持动态和静态链接

[dependencies]
memmap2 = "0.9"          # mmap内存映射
parking_lot = "0.12"     # 高性能锁
chrono = "0.4"           # 时间处理
serde = "1.0"            # 序列化
```

### 平台特定构建

**Android:**
```bash
cargo build --target aarch64-linux-android --release
```

**iOS:**
```bash
cargo build --target aarch64-apple-ios --release
```

**HarmonyOS:**
```bash
cargo build --target aarch64-unknown-linux-ohos --release
```

### 集成到各平台桥接层

**HarmonyOS集成：**
- 通过RustChannel自动调用，无需手动配置
- CI自动构建和打包，纯Rust实现无需额外库文件

**Android/iOS集成：**
- 通过对应的桥接层自动处理
- 纯Rust实现，无需预编译的外部库文件
- 简化了集成流程，减少了平台特定的依赖

## 🔄 数据流转详解

### 1. 初始化流程
```
UpLog.initialize(config)
    ↓ (转换为LoggerConfig)
RustLogger.initialize(context, config)
    ↓ (序列化为FlatBuffer)
RustChannel: lib_logger_cross_platform(params)
    ↓ (解析FlatBuffer并验证)
Rust: Logger.initialize(config)
    ↓ (初始化各个组件)
[Formatter, Storage, Privacy, Processor]
```

### 2. 日志写入流程
```
UpLog.logger.info("user: %{public}s", "john")
    ↓ (格式转换)
RustLogger.writeLog(LogLevel.INFO, "tag", "user: {}", "john")
    ↓ (序列化为FlatBuffer)
RustChannel: lib_logger_cross_platform(params)
    ↓ (解析并创建LogEntry)
Rust: Logger.write_log(entry)
    ↓ (处理流程)
[隐私检查] → [级别过滤] → [隐私处理] → [格式化] → [存储]
```

### 3. 配置更新流程
```
UpLog.updateUserId("new_user")
    ↓
RustLogger.updateUserId("new_user")
    ↓ (序列化为FlatBuffer)
RustChannel: lib_logger_cross_platform(params)
    ↓
Rust: Logger.update_user_id("new_user")
    ↓ (更新内存配置)
Config.user_id = "new_user"
```

### 4. 大文件检测和处理流程

**大文件自动检测：**
```
Logger.write_log(entry) → 文件大小检查
    ↓ (如果超过max_file_size且enable_full_log=true)
Logger.check_auto_upload()
    ↓ (触发回调通知前端)
LargeFileCallback.onLargeFileDetected(filePath)
    ↓ (前端处理)
[压缩日志] → [网络上传] → [清理文件]
```

**手动压缩上传：**
```
前端调用: compressLogsForUpload(7)
    ↓
Rust: Logger.compress_logs_for_upload(7)
    ↓ (压缩逻辑)
[收集7天日志] → [压缩ZIP] → [返回ZIP路径]
    ↓ (前端处理)
[网络上传ZIP] → [显示进度] → [处理结果]
    ↓ (上传完成)
cleanupAfterUploadSuccess/Failure(zipPath)
```

**崩溃日志处理：**
```
前端调用: compressCrashLogs()
    ↓
Rust: Logger.compress_crash_logs()
    ↓ (压缩逻辑)
[收集崩溃日志] → [压缩ZIP] → [返回ZIP路径]
    ↓ (前端处理)
[网络上传ZIP] → [处理结果] → [清理文件]
```

### 5. 崩溃日志流程

**崩溃日志写入：**
```
App崩溃 → UpLog.writeCrashLog(crashInfo)
    ↓
UpLogBridge.writeCrashLog(crashInfo)
    ↓
C++: up_rust_logger_write_crash_log(crash_info)
    ↓
Rust: Logger.write_crash_log(crash_info)
    ↓ (处理流程)
[隐私检查] → [格式化] → [写入exception目录] → [保存为yyyy-MM-dd-HH:mm:ss.SSSSSSZ.log]
```

**异常日志检查处理：**
```
应用启动 → 前端检查崩溃日志
    ↓
前端调用: compressCrashLogs()
    ↓ (检查exception目录)
[发现异常日志] → [压缩所有.log文件] → [返回ZIP路径]
    ↓ (前端处理)
[网络上传] → [清理本地文件]
```

**自动大文件通知：**
```
Logger.write_log(entry) → 文件大小检查
    ↓ (如果超过max_file_size且enable_full_log=true)
LargeFileCallback.onLargeFileDetected(filePath)
    ↓ (前端决定处理方式)
[可选择压缩上传] → [或者忽略继续写入]
```

## 📦 Protobuf集成

**Logger库已成功集成protobuf序列化功能，确保与Android端的服务器通信格式一致。**

### Protobuf文件结构

- **位置**: `proto/log_metadata.proto`
- **生成的Rust代码**: `src/generated/logger.rs`
- **主要类型**: `LogMetaData`

### 字段映射

protobuf字段与Android端保持一致：

```protobuf
message LogMetaData {
    string time = 1;         // 时间戳 (RFC3339格式)
    string session_id = 2;   // 会话ID
    string tag = 3;          // 日志标签/模块
    string level = 4;        // 日志级别 (DEBUG/INFO/WARN/ERROR)
    string test_mode = 5;    // 测试模式 (TEST/PROD)
    string user_id = 6;      // 用户ID
    string log_message = 7;  // 日志消息内容
}
```

### 构建配置

- **build.rs**: 自动编译protobuf文件到`src/generated/`目录
- **依赖**: 使用`prost`和`prost-build`进行protobuf处理
- **特性**: 在`xlog` feature中启用protobuf功能

### LogFormatter集成

- `to_protobuf()`: 将单个日志条目转换为protobuf格式
- `entries_to_protobuf()`: 批量转换日志条目
- 自动处理时间戳格式转换和字段映射

### 使用示例

```rust
use crate::formatter::LogFormatter;
use crate::core::LogEntry;
use crate::config::LoggerConfig;

let formatter = LogFormatter::new();
let config = LoggerConfig::new();
let entry = LogEntry::new(/* ... */);

// 转换为protobuf格式
let protobuf_data = formatter.to_protobuf(&entry, &config)?;

// 用于服务器上传
upload_to_server(protobuf_data);
```

### 服务器上传集成

```rust
// 在需要上传日志时
let protobuf_entries = formatter.entries_to_protobuf(&log_entries, &config)?;
for protobuf_data in protobuf_entries {
    // 发送到服务器
    send_to_server(protobuf_data).await?;
}
```

### 兼容性保证

- **字段名称**: 与Android端`LogMetaData.proto`完全一致
- **数据类型**: 所有字段使用string类型，确保跨平台兼容
- **序列化格式**: 使用标准protobuf二进制格式

### 性能考虑

- protobuf序列化在需要时进行，不影响常规日志性能
- 生成的代码经过优化，序列化开销最小
- 支持批量处理以提高上传效率

## 🧪 测试和调试

### 单元测试
```bash
cargo test
```

### 集成测试
```bash
cargo test --test integration
```

### 性能测试
```bash
cargo bench
```

### 调试日志
```bash
RUST_LOG=debug cargo test
```

### 内部日志编译时优化 🆕

**logger系统提供了编译时日志级别优化功能，可以在编译时就过滤掉不需要的内部调试日志，从而提高运行时性能。**

#### 可用的编译时日志级别特性

- `internal_log_level_error`: 只编译ERROR级别的内部日志
- `internal_log_level_warn`: 编译ERROR和WARN级别的内部日志
- `internal_log_level_info`: 编译ERROR、WARN和INFO级别的内部日志
- `internal_log_level_debug`: 编译所有级别的内部日志（默认行为）

#### 使用方法

**生产环境优化（只保留ERROR日志）：**
```bash
# 编译时只包含ERROR级别的内部日志
cargo build --release --features internal_log_level_error
```

**测试环境（保留ERROR和WARN日志）：**
```bash
# 编译时包含ERROR和WARN级别的内部日志
cargo build --features internal_log_level_warn
```

**开发环境（保留ERROR、WARN和INFO日志）：**
```bash
# 编译时包含ERROR、WARN和INFO级别的内部日志
cargo build --features internal_log_level_info
```

**调试环境（保留所有日志）：**
```bash
# 默认行为，包含所有级别的内部日志
cargo build
# 或者显式指定
cargo build --features internal_log_level_debug
```

#### 性能优势

**编译时优化：**
1. **代码体积减小**: 不需要的日志代码在编译时就被移除
2. **运行时性能提升**: 没有运行时的级别检查开销
3. **字符串格式化优化**: 不需要的格式化代码被完全移除

**性能对比示例：**
```rust
// 在生产环境使用 internal_log_level_error 特性时
// 以下代码在编译时就被完全移除，零运行时开销：

internal_debug!("MODULE", "Debug info: {}", expensive_calculation());
internal_info!("MODULE", "Processing item {}", item.id);
internal_trace!("MODULE", "Detailed trace: {:?}", complex_object);

// 只有这个会被编译：
internal_error!("MODULE", "Critical error: {}", error);
```

#### 运行时控制

即使使用了编译时优化，仍然可以通过环境变量进行运行时控制：

```bash
# 设置运行时日志级别（在编译时级别范围内）
export RUST_LOGGER_LOG=warn

# 完全禁用内部日志
export RUST_LOGGER_DISABLE=1
```

#### 最佳实践

**不同环境的构建脚本：**
```bash
#!/bin/bash
# build.sh

case "$ENV" in
  "production")
    cargo build --release --features internal_log_level_error
    ;;
  "staging")
    cargo build --release --features internal_log_level_warn
    ;;
  "development")
    cargo build --features internal_log_level_info
    ;;
  *)
    cargo build
    ;;
esac
```

**CI/CD集成：**
```yaml
# .github/workflows/build.yml
- name: Build for production
  run: cargo build --release --features internal_log_level_error

- name: Build for development
  run: cargo build --features internal_log_level_debug
```

#### 注意事项

1. **特性互斥**: 不要同时启用多个日志级别特性，系统会使用最严格的那个
2. **测试影响**: 使用编译时优化时，某些依赖内部日志的测试可能需要调整
3. **调试困难**: 在生产环境使用最严格的级别时，调试信息会完全丢失

#### 验证优化效果

```bash
# 检查编译警告，优化生效时会出现"unused variable"等警告
cargo build --features internal_log_level_error

# 比较不同特性下的二进制文件大小
cargo build --release
ls -la target/release/

cargo build --release --features internal_log_level_error
ls -la target/release/
```

## 📈 性能考虑

### 1. 内存管理
- 使用`BufWriter`减少系统调用
- 字符串池复用减少分配
- 及时释放大对象

### 2. 并发安全
- 使用`parking_lot`提供高性能锁
- 原子操作减少锁竞争
- 无锁数据结构优化热路径

### 3. I/O优化
- 批量写入减少磁盘操作
- 异步上传不阻塞主线程
- 文件预分配减少碎片

## 🔮 扩展点

### 1. 新平台支持
- 纯Rust实现，天然支持所有Rust支持的平台
- 无需平台特定的库文件，简化了移植工作
- 上层应用负责传入平台特定的设备ID、日志目录等配置
- 网络检查、路径配置等都由上层应用处理，logger库保持平台无关

### 2. 新功能添加
- 日志压缩：在`storage.rs`中添加压缩逻辑
- 加密存储：在`storage.rs`中添加加密功能
- 实时监控：添加新的监控模块

### 3. 性能优化
- 内存池：优化字符串分配
- 批处理：优化I/O操作
- 缓存：优化重复计算

这个README为未来的AI开发提供了完整的架构理解和开发指导，确保能够基于现有设计继续开发和优化。
