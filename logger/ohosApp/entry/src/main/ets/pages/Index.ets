import { hilog } from '@kit.PerformanceAnalysisKit'
import bundleManager from '@ohos.bundle.bundleManager'
import { RustLogger, LoggerConfig, LogLevel } from '@uplus/rust_logger'

@Entry
@Component
struct Index {
  @State message: string = 'Hello World'

  build() {
    RelativeContainer() {
      Text(this.message)
        .id('HelloWorld')
        .fontSize(50)
        .fontWeight(FontWeight.Bold)
        .alignRules({
          center: { anchor: '__container__', align: VerticalAlign.Center },
          middle: { anchor: '__container__', align: HorizontalAlign.Center }
        })
    }
    .height('100%')
    .width('100%')
  }

  aboutToAppear(): void {
    bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_HAP_MODULE |
    bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_METADATA).then(data => {
      hilog.info(0x0000, 'testTag', 'getBundleInfoForSelf successfully. Data: %{public}s', JSON.stringify(data))

      // 测试logger库
      const config = new LoggerConfig()
      config.appVersion = "TestApp"
      config.versionName = "1.0.0"
      config.logLevel = LogLevel.DEBUG  // 设置为DEBUG级别，确保所有日志都能写入
      config.enableConsoleOutput = true  // 启用控制台输出
      config.enableFullLog = true  // 启用全量日志
      config.privacyAgreed = true;

      hilog.info(0x0000, 'testTag', 'Logger config: logDir=%{public}s, level=%{public}d',
        config.logDirectory || "default", config.logLevel)

      // 初始化logger
      RustLogger.initialize(getContext(this), config)
      hilog.info(0x0000, 'testTag', 'Logger init called')

      // 等待一下确保初始化完成（因为是异步的）
      setTimeout(() => {
        hilog.info(0x0000, 'testTag', 'Starting logger tests...')

        // 测试日志写入 - 鸿蒙格式兼容性
        RustLogger.writeLog(LogLevel.INFO, "testTag", "用户登录: %{public}s, 手机号: %{private}s", ["张三", "13812345678"])
        RustLogger.writeLog(LogLevel.DEBUG, "testTag", "调试信息: %{public}d", ["123"])
        RustLogger.writeLog(LogLevel.WARN, "testTag", "警告: %{private}i", ["456"])

        // 测试日志写入 - Rust格式
        RustLogger.writeLog(LogLevel.INFO, "testTag", "用户操作: {}, 结果: {}", ["点击按钮", "成功"])
        RustLogger.writeLog(LogLevel.ERROR, "testTag", "错误信息: {}", ["网络连接失败"])

        hilog.info(0x0000, 'testTag', 'Logger test completed')

        // 检查日志目录
        setTimeout(() => {
          const logDir = `${getContext(this).filesDir}/logs`
          hilog.info(0x0000, 'testTag', 'Expected log directory: %{public}s', logDir)
        }, 2000)
      }, 2000)
    })
  }
}