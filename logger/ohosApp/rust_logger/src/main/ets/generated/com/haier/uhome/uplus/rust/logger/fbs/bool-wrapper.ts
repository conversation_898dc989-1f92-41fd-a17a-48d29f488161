// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

export class BoolWrapper {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):BoolWrapper {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsBoolWrapper(bb:flatbuffers.ByteBuffer, obj?:BoolWrapper):BoolWrapper {
  return (obj || new BoolWrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsBoolWrapper(bb:flatbuffers.ByteBuffer, obj?:BoolWrapper):BoolWrapper {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new BoolWrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

value():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

static startBoolWrapper(builder:flatbuffers.Builder) {
  builder.startObject(1);
}

static addValue(builder:flatbuffers.Builder, value:boolean) {
  builder.addFieldInt8(0, +value, +false);
}

static endBoolWrapper(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createBoolWrapper(builder:flatbuffers.Builder, value:boolean):flatbuffers.Offset {
  BoolWrapper.startBoolWrapper(builder);
  BoolWrapper.addValue(builder, value);
  return BoolWrapper.endBoolWrapper(builder);
}
}
