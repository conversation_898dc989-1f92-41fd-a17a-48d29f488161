// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

export class LogStats {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LogStats {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLogStats(bb:flatbuffers.ByteBuffer, obj?:LogStats):LogStats {
  return (obj || new LogStats()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLogStats(bb:flatbuffers.ByteBuffer, obj?:LogStats):LogStats {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LogStats()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

totalLogs():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

errorLogs():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

warningLogs():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

fileCount():number {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

totalSize():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

static startLogStats(builder:flatbuffers.Builder) {
  builder.startObject(5);
}

static addTotalLogs(builder:flatbuffers.Builder, totalLogs:bigint) {
  builder.addFieldInt64(0, totalLogs, BigInt('0'));
}

static addErrorLogs(builder:flatbuffers.Builder, errorLogs:bigint) {
  builder.addFieldInt64(1, errorLogs, BigInt('0'));
}

static addWarningLogs(builder:flatbuffers.Builder, warningLogs:bigint) {
  builder.addFieldInt64(2, warningLogs, BigInt('0'));
}

static addFileCount(builder:flatbuffers.Builder, fileCount:number) {
  builder.addFieldInt32(3, fileCount, 0);
}

static addTotalSize(builder:flatbuffers.Builder, totalSize:bigint) {
  builder.addFieldInt64(4, totalSize, BigInt('0'));
}

static endLogStats(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createLogStats(builder:flatbuffers.Builder, totalLogs:bigint, errorLogs:bigint, warningLogs:bigint, fileCount:number, totalSize:bigint):flatbuffers.Offset {
  LogStats.startLogStats(builder);
  LogStats.addTotalLogs(builder, totalLogs);
  LogStats.addErrorLogs(builder, errorLogs);
  LogStats.addWarningLogs(builder, warningLogs);
  LogStats.addFileCount(builder, fileCount);
  LogStats.addTotalSize(builder, totalSize);
  return LogStats.endLogStats(builder);
}
}
