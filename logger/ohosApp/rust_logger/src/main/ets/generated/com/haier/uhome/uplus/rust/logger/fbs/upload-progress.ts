// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

export class UploadProgress {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):UploadProgress {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsUploadProgress(bb:flatbuffers.ByteBuffer, obj?:UploadProgress):UploadProgress {
  return (obj || new UploadProgress()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsUploadProgress(bb:flatbuffers.ByteBuffer, obj?:UploadProgress):UploadProgress {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new UploadProgress()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

totalFiles():number {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

uploadedFiles():number {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

currentFile():string|null
currentFile(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
currentFile(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

progressPercent():number {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

static startUploadProgress(builder:flatbuffers.Builder) {
  builder.startObject(4);
}

static addTotalFiles(builder:flatbuffers.Builder, totalFiles:number) {
  builder.addFieldInt32(0, totalFiles, 0);
}

static addUploadedFiles(builder:flatbuffers.Builder, uploadedFiles:number) {
  builder.addFieldInt32(1, uploadedFiles, 0);
}

static addCurrentFile(builder:flatbuffers.Builder, currentFileOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, currentFileOffset, 0);
}

static addProgressPercent(builder:flatbuffers.Builder, progressPercent:number) {
  builder.addFieldInt32(3, progressPercent, 0);
}

static endUploadProgress(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createUploadProgress(builder:flatbuffers.Builder, totalFiles:number, uploadedFiles:number, currentFileOffset:flatbuffers.Offset, progressPercent:number):flatbuffers.Offset {
  UploadProgress.startUploadProgress(builder);
  UploadProgress.addTotalFiles(builder, totalFiles);
  UploadProgress.addUploadedFiles(builder, uploadedFiles);
  UploadProgress.addCurrentFile(builder, currentFileOffset);
  UploadProgress.addProgressPercent(builder, progressPercent);
  return UploadProgress.endUploadProgress(builder);
}
}
