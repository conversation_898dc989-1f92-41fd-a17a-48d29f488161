// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

import { LogLevel } from './log-level';


export class LogEntry {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LogEntry {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLogEntry(bb:flatbuffers.ByteBuffer, obj?:LogEntry):LogEntry {
  return (obj || new LogEntry()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLogEntry(bb:flatbuffers.ByteBuffer, obj?:LogEntry):LogEntry {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LogEntry()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

level():LogLevel {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt8(this.bb_pos + offset) : LogLevel.DEBUG;
}

tag():string|null
tag(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
tag(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

format():string|null
format(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
format(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

args(index: number):string
args(index: number,optionalEncoding:flatbuffers.Encoding):string|Uint8Array
args(index: number,optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__string(this.bb!.__vector(this.bb_pos + offset) + index * 4, optionalEncoding) : null;
}

argsLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

sensitive(index: number):boolean|null {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? !!this.bb!.readInt8(this.bb!.__vector(this.bb_pos + offset) + index) : false;
}

sensitiveLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? this.bb!.__vector_len(this.bb_pos + offset) : 0;
}

sensitiveArray():Int8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? new Int8Array(this.bb!.bytes().buffer, this.bb!.bytes().byteOffset + this.bb!.__vector(this.bb_pos + offset), this.bb!.__vector_len(this.bb_pos + offset)) : null;
}

timestamp():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 14);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

static startLogEntry(builder:flatbuffers.Builder) {
  builder.startObject(6);
}

static addLevel(builder:flatbuffers.Builder, level:LogLevel) {
  builder.addFieldInt8(0, level, LogLevel.DEBUG);
}

static addTag(builder:flatbuffers.Builder, tagOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, tagOffset, 0);
}

static addFormat(builder:flatbuffers.Builder, formatOffset:flatbuffers.Offset) {
  builder.addFieldOffset(2, formatOffset, 0);
}

static addArgs(builder:flatbuffers.Builder, argsOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, argsOffset, 0);
}

static createArgsVector(builder:flatbuffers.Builder, data:flatbuffers.Offset[]):flatbuffers.Offset {
  builder.startVector(4, data.length, 4);
  for (let i = data.length - 1; i >= 0; i--) {
    builder.addOffset(data[i]!);
  }
  return builder.endVector();
}

static startArgsVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(4, numElems, 4);
}

static addSensitive(builder:flatbuffers.Builder, sensitiveOffset:flatbuffers.Offset) {
  builder.addFieldOffset(4, sensitiveOffset, 0);
}

static createSensitiveVector(builder:flatbuffers.Builder, data:boolean[]):flatbuffers.Offset {
  builder.startVector(1, data.length, 1);
  for (let i = data.length - 1; i >= 0; i--) {
    builder.addInt8(+data[i]!);
  }
  return builder.endVector();
}

static startSensitiveVector(builder:flatbuffers.Builder, numElems:number) {
  builder.startVector(1, numElems, 1);
}

static addTimestamp(builder:flatbuffers.Builder, timestamp:bigint) {
  builder.addFieldInt64(5, timestamp, BigInt('0'));
}

static endLogEntry(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  builder.requiredField(offset, 6) // tag
  builder.requiredField(offset, 8) // format
  return offset;
}

static createLogEntry(builder:flatbuffers.Builder, level:LogLevel, tagOffset:flatbuffers.Offset, formatOffset:flatbuffers.Offset, argsOffset:flatbuffers.Offset, sensitiveOffset:flatbuffers.Offset, timestamp:bigint):flatbuffers.Offset {
  LogEntry.startLogEntry(builder);
  LogEntry.addLevel(builder, level);
  LogEntry.addTag(builder, tagOffset);
  LogEntry.addFormat(builder, formatOffset);
  LogEntry.addArgs(builder, argsOffset);
  LogEntry.addSensitive(builder, sensitiveOffset);
  LogEntry.addTimestamp(builder, timestamp);
  return LogEntry.endLogEntry(builder);
}
}
