// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from '@ohos/flatbuffers';

export class NoneWrapper {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):NoneWrapper {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsNoneWrapper(bb:flatbuffers.ByteBuffer, obj?:NoneWrapper):NoneWrapper {
  return (obj || new NoneWrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsNoneWrapper(bb:flatbuffers.ByteBuffer, obj?:NoneWrapper):NoneWrapper {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new NoneWrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static startNoneWrapper(builder:flatbuffers.Builder) {
  builder.startObject(0);
}

static endNoneWrapper(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createNoneWrapper(builder:flatbuffers.Builder):flatbuffers.Offset {
  NoneWrapper.startNoneWrapper(builder);
  return NoneWrapper.endNoneWrapper(builder);
}
}
