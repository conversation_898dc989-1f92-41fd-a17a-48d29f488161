// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

export class Int32Wrapper {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):Int32Wrapper {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsInt32Wrapper(bb:flatbuffers.ByteBuffer, obj?:Int32Wrapper):Int32Wrapper {
  return (obj || new Int32Wrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsInt32Wrapper(bb:flatbuffers.ByteBuffer, obj?:Int32Wrapper):Int32Wrapper {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new Int32Wrapper()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

value():number {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

static startInt32Wrapper(builder:flatbuffers.Builder) {
  builder.startObject(1);
}

static addValue(builder:flatbuffers.Builder, value:number) {
  builder.addFieldInt32(0, value, 0);
}

static endInt32Wrapper(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createInt32Wrapper(builder:flatbuffers.Builder, value:number):flatbuffers.Offset {
  Int32Wrapper.startInt32Wrapper(builder);
  Int32Wrapper.addValue(builder, value);
  return Int32Wrapper.endInt32Wrapper(builder);
}
}
