// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import { BoolWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/bool-wrapper.js';
import { Int32Wrapper } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/int32-wrapper.js';
import { LargeFileEvent } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/large-file-event.js';
import { LogEntry } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/log-entry.js';
import { LogStats } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/log-stats.js';
import { LoggerConfig } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/logger-config.js';
import { NoneWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/none-wrapper.js';
import { StrWrapper } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/str-wrapper.js';
import { UploadProgress } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/upload-progress.js';


export enum LoggerContainer {
  NONE = 0,
  BoolWrapper = 1,
  StrWrapper = 2,
  Int32Wrapper = 3,
  NoneWrapper = 4,
  LogEntry = 5,
  LoggerConfig = 6,
  UploadProgress = 7,
  LogStats = 8,
  LargeFileEvent = 9
}

export function unionToLoggerContainer(
  type: LoggerContainer,
  accessor: (obj:BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress) => BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress|null
): BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress|null {
  switch(LoggerContainer[type]) {
    case 'NONE': return null; 
    case 'BoolWrapper': return accessor(new BoolWrapper())! as BoolWrapper;
    case 'StrWrapper': return accessor(new StrWrapper())! as StrWrapper;
    case 'Int32Wrapper': return accessor(new Int32Wrapper())! as Int32Wrapper;
    case 'NoneWrapper': return accessor(new NoneWrapper())! as NoneWrapper;
    case 'LogEntry': return accessor(new LogEntry())! as LogEntry;
    case 'LoggerConfig': return accessor(new LoggerConfig())! as LoggerConfig;
    case 'UploadProgress': return accessor(new UploadProgress())! as UploadProgress;
    case 'LogStats': return accessor(new LogStats())! as LogStats;
    case 'LargeFileEvent': return accessor(new LargeFileEvent())! as LargeFileEvent;
    default: return null;
  }
}

export function unionListToLoggerContainer(
  type: LoggerContainer, 
  accessor: (index: number, obj:BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress) => BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress|null, 
  index: number
): BoolWrapper|Int32Wrapper|LargeFileEvent|LogEntry|LogStats|LoggerConfig|NoneWrapper|StrWrapper|UploadProgress|null {
  switch(LoggerContainer[type]) {
    case 'NONE': return null; 
    case 'BoolWrapper': return accessor(index, new BoolWrapper())! as BoolWrapper;
    case 'StrWrapper': return accessor(index, new StrWrapper())! as StrWrapper;
    case 'Int32Wrapper': return accessor(index, new Int32Wrapper())! as Int32Wrapper;
    case 'NoneWrapper': return accessor(index, new NoneWrapper())! as NoneWrapper;
    case 'LogEntry': return accessor(index, new LogEntry())! as LogEntry;
    case 'LoggerConfig': return accessor(index, new LoggerConfig())! as LoggerConfig;
    case 'UploadProgress': return accessor(index, new UploadProgress())! as UploadProgress;
    case 'LogStats': return accessor(index, new LogStats())! as LogStats;
    case 'LargeFileEvent': return accessor(index, new LargeFileEvent())! as LargeFileEvent;
    default: return null;
  }
}
