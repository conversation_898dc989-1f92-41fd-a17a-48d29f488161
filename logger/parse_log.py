#!/usr/bin/env python3
"""
日志文件解析工具
解析rust_logger生成的protobuf格式日志文件
"""

import sys
import struct
from pathlib import Path

def read_varint(data, offset):
    """读取varint编码的长度"""
    result = 0
    shift = 0
    pos = offset
    
    while pos < len(data):
        byte = data[pos]
        result |= (byte & 0x7F) << shift
        pos += 1
        if (byte & 0x80) == 0:
            break
        shift += 7
    
    return result, pos

def parse_log_file(file_path):
    """解析日志文件"""
    print(f"🔍 解析日志文件: {file_path}")
    
    try:
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"📊 文件大小: {len(data)} 字节")
        
        offset = 0
        entry_count = 0
        
        while offset < len(data):
            try:
                # 读取varint长度前缀
                length, new_offset = read_varint(data, offset)
                
                if new_offset >= len(data) or new_offset + length > len(data):
                    print(f"⚠️  在偏移 {offset} 处遇到无效长度: {length}")
                    break
                
                # 读取protobuf数据
                protobuf_data = data[new_offset:new_offset + length]
                
                entry_count += 1
                print(f"\n📝 日志条目 #{entry_count}:")
                print(f"   偏移: {offset}")
                print(f"   长度: {length} 字节")
                print(f"   数据: {protobuf_data[:50].hex()}{'...' if len(protobuf_data) > 50 else ''}")
                
                # 尝试解析protobuf中的文本内容
                try:
                    text_content = protobuf_data.decode('utf-8', errors='ignore')
                    if any(keyword in text_content for keyword in ['session_id', 'sessionId', 'tag', 'level']):
                        print(f"   可能的文本内容: {repr(text_content[:200])}")
                except:
                    pass
                
                # 查找可能的sessionId
                if b'session_id' in protobuf_data or b'sessionId' in protobuf_data:
                    print(f"   ✅ 发现 session_id 字段!")
                
                offset = new_offset + length
                
                # 限制输出条目数量
                if entry_count >= 10:
                    print(f"\n... (显示前10条，总共可能有更多条目)")
                    break
                    
            except Exception as e:
                print(f"❌ 解析条目时出错: {e}")
                break
        
        print(f"\n📊 解析完成:")
        print(f"   总条目数: {entry_count}")
        print(f"   处理字节数: {offset}/{len(data)}")
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {file_path}")
    except Exception as e:
        print(f"❌ 解析失败: {e}")

def main():
    if len(sys.argv) != 2:
        print("用法: python3 parse_log.py <日志文件路径>")
        print("示例: python3 parse_log.py 2025-06-23-10_58_32.583894+0000.log")
        sys.exit(1)
    
    file_path = sys.argv[1]
    parse_log_file(file_path)

if __name__ == "__main__":
    main()
