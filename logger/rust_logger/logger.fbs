namespace com.haier.uhome.uplus.rust.logger.fbs;

// 基础包装类型
table BoolWrapper {
    value:bool;
}

table StrWrapper {
    value:string;
}

table Int32Wrapper {
    value:int;
}

table NoneWrapper {
    // 空表，用于表示无数据
}

// 日志级别枚举
enum LogLevel:byte { DEBUG = 0, INFO = 1, WARN = 2, ERROR = 3 }

// 日志条目
table LogEntry {
    level:LogLevel;
    tag:string (required);
    format:string (required);
    args:[string];
    sensitive:[bool];
    timestamp:long;
}

// 日志配置
table LoggerConfig {
    // 基础配置
    log_level:LogLevel;
    enable_console_output:bool;
    enable_file_output:bool;
    enable_full_log:bool;
    test_mode:bool;
    log_env:string;
    disable_sensitive_words:bool;
    
    // 用户信息
    user_id:string;
    device_id:string;
    session_id:string;
    app_version:string;
    
    // 隐私配置
    privacy_agreed:bool;
    is_debug_mode:bool;
    
    // 文件配置
    max_file_size:long;
    max_directory_size:long;
    log_file_prefix:string;
    log_directory:string (required);
    
    // 上传配置
    upload_url:string;
    max_retry_count:int;
    upload_timeout_seconds:long;
    max_zip_file_size:long;
    
    // 性能配置
    max_log_length:int;
    max_logs_per_second:int;
    
    // Android对齐的关键配置
    version_name:string;
}

// 上传进度信息
table UploadProgress {
    total_files:int;
    uploaded_files:int;
    current_file:string;
    progress_percent:int;
}

// 日志统计信息
table LogStats {
    total_logs:long;
    error_logs:long;
    warning_logs:long;
    file_count:int;
    total_size:long;
}

// 大文件事件
table LargeFileEvent {
    event_type:string;
    file_path:string;
    file_size:long;
}

// 联合类型，用于不同的返回值
union LoggerContainer {
    BoolWrapper,
    StrWrapper,
    Int32Wrapper,
    NoneWrapper,
    LogEntry,
    LoggerConfig,
    UploadProgress,
    LogStats,
    LargeFileEvent
}

// 事件消息（用于大文件回调）
table LoggerMessage {
    code:int;
    container:LoggerContainer;
}

// 主要的返回结果结构
table LoggerFlat {
    container:LoggerContainer;
    code:int;
    message:string;
    success:bool;
}

root_type LoggerFlat;
