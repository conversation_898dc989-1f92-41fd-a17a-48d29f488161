# 内部日志编译时优化

rust_logger提供了编译时日志级别优化功能，可以在编译时就过滤掉不需要的日志级别，从而提高运行时性能。

## 特性说明

### 可用的编译时日志级别特性

- `internal_log_level_error`: 只编译ERROR级别的内部日志
- `internal_log_level_warn`: 编译ERROR和WARN级别的内部日志
- `internal_log_level_info`: 编译ERROR、WARN和INFO级别的内部日志
- `internal_log_level_debug`: 编译ERROR、WARN、INFO和DEBUG级别的内部日志
- `internal_log_level_trace`: 编译所有级别的内部日志（默认行为）

### 优先级

特性按照日志级别的严重程度有优先级：
- `internal_log_level_error` > `internal_log_level_warn` > `internal_log_level_info` > `internal_log_level_debug`
- 如果同时指定多个特性，会使用最严格的那个

## 使用方法

### 1. 生产环境优化（只保留ERROR日志）

```bash
# 编译时只包含ERROR级别的内部日志
cargo build --release --features internal_log_level_error
```

### 2. 测试环境（保留ERROR和WARN日志）

```bash
# 编译时包含ERROR和WARN级别的内部日志
cargo build --features internal_log_level_warn
```

### 3. 开发环境（保留ERROR、WARN和INFO日志）

```bash
# 编译时包含ERROR、WARN和INFO级别的内部日志
cargo build --features internal_log_level_info
```

### 4. 调试环境（保留所有日志）

```bash
# 默认行为，包含所有级别的内部日志
cargo build
# 或者显式指定
cargo build --features internal_log_level_trace
```

## 性能优势

### 编译时优化

当使用编译时日志级别特性时：

1. **代码体积减小**: 不需要的日志代码在编译时就被移除
2. **运行时性能提升**: 没有运行时的级别检查开销
3. **字符串格式化优化**: 不需要的格式化代码被完全移除

### 性能对比示例

```rust
// 在生产环境使用 internal_log_level_error 特性时
// 以下代码在编译时就被完全移除，零运行时开销：

internal_debug!("MODULE", "Debug info: {}", expensive_calculation());
internal_info!("MODULE", "Processing item {}", item.id);
internal_trace!("MODULE", "Detailed trace: {:?}", complex_object);

// 只有这个会被编译：
internal_error!("MODULE", "Critical error: {}", error);
```

## 运行时控制

即使使用了编译时优化，仍然可以通过环境变量进行运行时控制：

```bash
# 设置运行时日志级别（在编译时级别范围内）
export RUST_LOGGER_LOG=warn

# 完全禁用内部日志
export RUST_LOGGER_DISABLE=1
```

## 最佳实践

### 1. 生产环境配置

```toml
# Cargo.toml
[features]
production = ["internal_log_level_error"]
```

```bash
cargo build --release --features production
```

### 2. 不同环境的构建脚本

```bash
#!/bin/bash
# build.sh

case "$ENV" in
  "production")
    cargo build --release --features internal_log_level_error
    ;;
  "staging")
    cargo build --release --features internal_log_level_warn
    ;;
  "development")
    cargo build --features internal_log_level_info
    ;;
  *)
    cargo build
    ;;
esac
```

### 3. CI/CD集成

```yaml
# .github/workflows/build.yml
- name: Build for production
  run: cargo build --release --features internal_log_level_error
  
- name: Build for development
  run: cargo build --features internal_log_level_debug
```

## 注意事项

1. **特性互斥**: 不要同时启用多个日志级别特性，系统会使用最严格的那个
2. **测试影响**: 使用编译时优化时，某些依赖内部日志的测试可能需要调整
3. **调试困难**: 在生产环境使用最严格的级别时，调试信息会完全丢失

## 验证优化效果

可以通过以下方式验证编译时优化是否生效：

```bash
# 检查编译警告，优化生效时会出现"unused variable"等警告
cargo build --features internal_log_level_error

# 比较不同特性下的二进制文件大小
cargo build --release
ls -la target/release/

cargo build --release --features internal_log_level_error
ls -la target/release/
```

## 总结

编译时日志级别优化是一个强大的性能优化工具，特别适合：

- 生产环境部署
- 性能敏感的应用
- 嵌入式或资源受限的环境
- 需要最小化二进制文件大小的场景

通过合理使用这些特性，可以在保持代码可维护性的同时，获得最佳的运行时性能。
