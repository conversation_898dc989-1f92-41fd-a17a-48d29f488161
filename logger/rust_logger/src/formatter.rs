use chrono::Utc;
use uuid::Uuid;
use std::collections::HashMap;
use prost::Message;

use crate::{
    config::LoggerConfig,
    core::LogEntry,
    error::{LoggerError, Result},
    protobuf::LogMetaData,
};

/// 日志格式化器
pub struct LogFormatter {
    session_id: String,
}

impl LogFormatter {
    /// 创建新的格式化器
    pub fn new() -> Self {
        Self {
            session_id: Uuid::new_v4().to_string(),
        }
    }
    
    /// 初始化格式化器
    pub fn initialize(&mut self, config: &LoggerConfig) -> Result<()> {
        // 如果配置中有session_id，使用配置的
        if !config.session_id.is_empty() {
            self.session_id = config.session_id.clone();
        }

        log::debug!("LogFormatter initialized with session_id: {}", self.session_id);
        Ok(())
    }
    
    /// 格式化日志条目
    /// 格式："[时间戳]+[sessionID]+[deviceID]+[模块信息]+[LogLevel]+[TestMode]+[userId]+[日志信息]"
    pub fn format_entry(&self, entry: &LogEntry, config: &LoggerConfig) -> Result<String> {
        let mut formatted = String::new();

        // [时间戳]
        let timestamp = entry.timestamp.format("%Y-%m-%d %H:%M:%S%.3f UTC");
        formatted.push_str(&format!("[{}]", timestamp));

        // [sessionID]
        formatted.push_str(&format!("[{}]", self.session_id));

        // [deviceID] - 由外部传入，不自动获取
        let device_id = config.device_id.as_ref().map(|s| s.as_str()).unwrap_or("UNKNOWN");
        formatted.push_str(&format!("[{}]", device_id));

        // [模块信息] - 使用tag作为模块信息
        formatted.push_str(&format!("[{}]", entry.tag));

        // [LogLevel]
        formatted.push_str(&format!("[{}]", entry.level.as_str()));

        // [TestMode] - 根据test_mode设置，与Android逻辑一致
        let test_mode_str = if config.test_mode { "TEST" } else { "PROD" };
        formatted.push_str(&format!("[{}]", test_mode_str));

        // [userId]
        let user_id = if config.user_id.is_empty() { "UNKNOWN" } else { &config.user_id };
        formatted.push_str(&format!("[{}]", user_id));

        // [日志信息]
        let mut content = self.build_content(entry)?;

        // 应用脱敏处理
        content = self.apply_sensitive_masking(&content, entry, config);

        // 添加自定义前缀（如果配置了）
        if let Some(ref custom_prefix) = config.custom_prefix {
            if !custom_prefix.is_empty() {
                content = format!("{}: {}", custom_prefix, content);
            }
        }

        formatted.push_str(&format!("[{}]", content));

        Ok(formatted)
    }
    

    
    /// 构建日志内容
    fn build_content(&self, entry: &LogEntry) -> Result<String> {
        let mut content = entry.message.clone();

        // 替换占位符
        let mut arg_index = 0;
        while content.contains("{}") && arg_index < entry.args.len() {
            content = content.replacen("{}", &entry.args[arg_index], 1);
            arg_index += 1;
        }

        Ok(content)
    }

    /// 对敏感信息进行脱敏处理
    fn apply_sensitive_masking(&self, content: &str, entry: &LogEntry, config: &LoggerConfig) -> String {
        // 如果禁用脱敏，直接返回原内容
        if config.disable_sensitive_words {
            return content.to_string();
        }

        let mut masked_content = content.to_string();

        // 根据sensitive标记对相应的参数进行脱敏
        if entry.sensitive_flags.len() == entry.args.len() {
            for (i, &is_sensitive) in entry.sensitive_flags.iter().enumerate() {
                if is_sensitive && i < entry.args.len() {
                    let arg = &entry.args[i];
                    // 简单的脱敏策略：保留前2位和后2位，中间用*替换
                    let masked_arg = if arg.len() > 4 {
                        format!("{}***{}", &arg[..2], &arg[arg.len()-2..])
                    } else if arg.len() > 2 {
                        format!("{}***", &arg[..1])
                    } else {
                        "***".to_string()
                    };
                    masked_content = masked_content.replace(arg, &masked_arg);
                }
            }
        }

        masked_content
    }
    

    
    /// 格式化用于上传的日志
    pub fn format_for_upload(&self, entries: &[LogEntry], config: &LoggerConfig) -> Result<String> {
        let mut upload_content = String::new();
        
        // 添加上传头部信息
        upload_content.push_str(&self.build_upload_header(config)?);
        upload_content.push('\n');
        
        // 添加所有日志条目
        for entry in entries {
            let formatted_entry = self.format_entry(entry, config)?;
            upload_content.push_str(&formatted_entry);
            upload_content.push('\n');
        }
        
        Ok(upload_content)
    }
    
    /// 构建上传头部信息
    fn build_upload_header(&self, config: &LoggerConfig) -> Result<String> {
        let mut header = HashMap::new();

        // 使用配置中的device_id
        let default_device_id = "unknown_device".to_string();
        let device_id = config.device_id.as_ref().unwrap_or(&default_device_id);
        header.insert("device_id", device_id.clone());
        header.insert("session_id", self.session_id.clone());
        let default_app_version = "unknown".to_string();
        let app_version = config.app_version.as_ref().unwrap_or(&default_app_version);
        header.insert("app_version", app_version.clone());
        header.insert("platform", Self::get_platform_name());
        header.insert("upload_time", Utc::now().to_rfc3339());

        if !config.user_id.is_empty() {
            header.insert("user_id", config.user_id.clone());
        }

        serde_json::to_string(&header).map_err(LoggerError::from)
    }
    
    /// 获取平台名称
    fn get_platform_name() -> String {
        #[cfg(target_os = "android")]
        return "Android".to_string();
        
        #[cfg(target_os = "ios")]
        return "iOS".to_string();
        
        #[cfg(target_os = "linux")] // HarmonyOS
        return "HarmonyOS".to_string();
        
        #[cfg(not(any(target_os = "android", target_os = "ios", target_os = "linux")))]
        return "Unknown".to_string();
    }
    

    
    /// 获取会话ID
    pub fn get_session_id(&self) -> &str {
        &self.session_id
    }

    /// 将日志条目转换为protobuf格式
    /// 用于服务器通信，确保与Android端格式一致
    pub fn to_protobuf(&self, entry: &LogEntry, config: &LoggerConfig) -> Result<Vec<u8>> {
        let metadata = LogMetaData {
            time: entry.timestamp.to_rfc3339(),
            session_id: self.session_id.clone(),
            tag: entry.tag.clone(),
            level: entry.level.as_str().to_string(),
            test_mode: if config.test_mode { "TEST".to_string() } else { "PROD".to_string() },
            user_id: config.user_id.clone(),
            log_message: self.build_content(entry)?,
        };

        let mut buf = Vec::new();
        metadata.encode(&mut buf)
            .map_err(|e| LoggerError::unknown(format!("Failed to encode protobuf: {}", e)))?;

        Ok(buf)
    }

    /// 批量转换日志条目为protobuf格式
    pub fn entries_to_protobuf(&self, entries: &[LogEntry], config: &LoggerConfig) -> Result<Vec<Vec<u8>>> {
        entries.iter()
            .map(|entry| self.to_protobuf(entry, config))
            .collect()
    }
}
