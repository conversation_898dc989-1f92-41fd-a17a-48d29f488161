//! # Logger - 统一日志库
//!
//! 这是一个为Android、iOS、HarmonyOS三端提供统一日志功能的Rust库。
//!
//! ## 架构设计
//!
//! ```text
//! Android: App → RustChannel → logger (Rust核心)
//! iOS: App → RustUplus → logger (Rust核心)
//! HarmonyOS: App → librust_uplus.so → logger (Rust核心)
//! ```
//!
//! ## 主要功能
//!
//! - 多级别日志输出和过滤
//! - 日志格式化（固定头部信息）
//! - 文件存储和管理
//! - 隐私信息处理和脱敏
//! - 日志上传功能
//! - 三端统一的配置管理

pub mod config;
pub mod core;
pub mod formatter;
pub mod storage;
pub mod sensitive_data;
pub mod ffi;
pub mod error;
pub mod compression;
pub mod processor;
pub mod protobuf;
pub mod mmap_storage;
pub mod internal_log;

// 重新导出主要类型
pub use config::LoggerConfig;
pub use core::{Logger, LogLevel};
pub use error::{LoggerError, Result};

// FFI接口导出
pub use ffi::*;

// 测试模块
#[cfg(test)]
mod test;
