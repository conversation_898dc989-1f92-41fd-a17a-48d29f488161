use std::sync::{<PERSON>tex, OnceLock, Arc, RwLock};
use std::collections::HashMap;
use flatbuffers::FlatBufferBuilder;
use task_manager::platform::function::PlatformConsumer;
use task_manager::common_runtime::get_runtime;

use crate::{
    config::LoggerConfig,
    core::{Logger, LogEntry},
};

// 导入内部日志系统
use crate::{internal_info, internal_error};

// 导入生成的FlatBuffer代码
mod generated {
    include!("generated/logger_generated.rs");
}

use generated::com::haier::uhome::uplus::rust::logger::fbs::*;

// 全局日志器实例
static GLOBAL_LOGGER: OnceLock<Mutex<Logger>> = OnceLock::new();

// 大文件观察者存储
static LARGE_FILE_OBSERVERS: OnceLock<Arc<RwLock<HashMap<String, Box<dyn PlatformConsumer + Send + Sync>>>>> = OnceLock::new();



// ============================================================================
// RustChannel 兼容函数 - 这是唯一的对外接口
// ============================================================================

/// RustChannel统一入口函数
/// 同步执行
#[no_mangle]
pub fn lib_logger_cross_platform(params: HashMap<String, String>) -> Vec<u8> {
    let default_action = "".to_string();
    let action = params.get("action").unwrap_or(&default_action).as_str();
    log::info!("logger: executing action: {}", action);

    match action {
        // 同步操作 - 配置和状态查询
        "add_large_file_observer" => handle_add_large_file_observer(&params),
        "enable_console_log" => handle_enable_console_log(&params),
        "enable_full_logs" => handle_enable_full_logs(&params),
        "is_console_log_enabled" => handle_is_console_log_enabled(&params),
        "is_full_logs_enabled" => handle_is_full_logs_enabled(&params),
        "get_log_level" => handle_get_log_level(&params),
        _ => {
            log::warn!("logger: unsupported action: {}", action);
            create_success_response_bool(true)
        }
    }
}

/// RustChannel异步函数，处理需要异步执行的操作
/// 参考updevice的lib_updevice_cross_platform_async实现
pub async fn lib_logger_cross_platform_async(params: HashMap<String, String>) -> Vec<u8> {
    let default_action = "".to_string();
    let action = params.get("action").unwrap_or(&default_action).as_str();
    log::info!("logger async: executing action: {}", action);

    match action {
        "initialize" => handle_initialize_async(&params).await,
        "write_log" => handle_write_log_async(&params).await,
        "update_user_id" => handle_update_user_id_async(&params).await,
        "write_crash_log" => handle_write_crash_log_async(&params).await,
        "cleanup_after_upload_success" => handle_cleanup_after_upload_success_async(&params).await,
        "cleanup_after_upload_failure" => handle_cleanup_after_upload_failure_async(&params).await,
        _ => {
            log::warn!("logger async: unsupported action: {}", action);
            create_success_response_bool(true)
        }
    }
}

/// RustChannel消费者函数，用于处理需要回调的操作
/// 这个函数会被RustChannel的manageRustBufferListenerWithData调用
/// 注意：泛型函数不能使用#[no_mangle]，RustChannel会自动找到这个函数
pub fn lib_logger_cross_platform_consumer_data(
    params: HashMap<String, String>,
    consumer: impl PlatformConsumer + Send + Sync + 'static,
) -> Vec<u8> {
    let default_action = "".to_string();
    let action = params.get("action").unwrap_or(&default_action).as_str();
    log::info!("logger consumer: executing action: {}", action);

    match action {
        "add_large_file_observer" => add_large_file_observer_impl(consumer),
        "compress_logs_for_upload" => handle_compress_logs_with_callback(&params, consumer),
        "compress_crash_logs" => handle_compress_crash_logs_with_callback(&params, consumer),
        _ => {
            log::warn!("logger consumer: unsupported action: {}", action);
            create_success_response_bool(true)
        }
    }
}

// ============================================================================
// FlatBuffer 响应函数
// ============================================================================

/// 创建成功响应（FlatBuffer格式）
fn create_success_response_bool(value: bool) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 创建BoolWrapper
    let bool_wrapper = BoolWrapper::create(&mut builder, &BoolWrapperArgs {
        value,
    });

    // 创建LoggerFlat
    let logger_flat = LoggerFlat::create(&mut builder, &LoggerFlatArgs {
        container_type: LoggerContainer::BoolWrapper,
        container: Some(bool_wrapper.as_union_value()),
        code: 0, // 成功代码
        message: None,
        success: true,
    });

    builder.finish(logger_flat, None);
    builder.finished_data().to_vec()
}

/// 创建成功响应（字符串）
fn create_success_response_string(value: String) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 先创建字符串
    let value_str = builder.create_string(&value);

    // 创建StrWrapper
    let str_wrapper = StrWrapper::create(&mut builder, &StrWrapperArgs {
        value: Some(value_str),
    });

    // 创建LoggerFlat
    let logger_flat = LoggerFlat::create(&mut builder, &LoggerFlatArgs {
        container_type: LoggerContainer::StrWrapper,
        container: Some(str_wrapper.as_union_value()),
        code: 0, // 成功代码
        message: None,
        success: true,
    });

    builder.finish(logger_flat, None);
    builder.finished_data().to_vec()
}

/// 创建成功响应（整数）
fn create_success_response_int32(value: i32) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 创建Int32Wrapper
    let int32_wrapper = Int32Wrapper::create(&mut builder, &Int32WrapperArgs {
        value,
    });

    // 创建LoggerFlat
    let logger_flat = LoggerFlat::create(&mut builder, &LoggerFlatArgs {
        container_type: LoggerContainer::Int32Wrapper,
        container: Some(int32_wrapper.as_union_value()),
        code: 0, // 成功代码
        message: None,
        success: true,
    });

    builder.finish(logger_flat, None);
    builder.finished_data().to_vec()
}

/// 创建成功响应（无数据）
fn create_success_response_none() -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 创建NoneWrapper
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});

    // 创建LoggerFlat
    let logger_flat = LoggerFlat::create(&mut builder, &LoggerFlatArgs {
        container_type: LoggerContainer::NoneWrapper,
        container: Some(none_wrapper.as_union_value()),
        code: 0, // 成功代码
        message: None,
        success: true,
    });

    builder.finish(logger_flat, None);
    builder.finished_data().to_vec()
}

/// 创建错误响应（FlatBuffer格式）
fn create_error_response(message: &str) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 先创建错误消息字符串
    let error_message = builder.create_string(message);

    // 创建NoneWrapper（错误时没有数据）
    let none_wrapper = NoneWrapper::create(&mut builder, &NoneWrapperArgs {});

    // 创建LoggerFlat
    let logger_flat = LoggerFlat::create(&mut builder, &LoggerFlatArgs {
        container_type: LoggerContainer::NoneWrapper,
        container: Some(none_wrapper.as_union_value()),
        code: -1, // 错误代码
        message: Some(error_message),
        success: false,
    });

    builder.finish(logger_flat, None);
    builder.finished_data().to_vec()
}

// ============================================================================
// Action 处理函数
// ============================================================================

/// 处理初始化（异步版本）
async fn handle_initialize_async(params: &HashMap<String, String>) -> Vec<u8> {
    // 使用内部日志系统
    internal_info!("FFI", "🔧 handle_initialize_async called");

    let config_str = match params.get("config") {
        Some(config) => {
            internal_info!("FFI", "📋 Config received");
            config.clone()
        },
        None => {
            internal_error!("FFI", "❌ Missing config parameter in initialize");
            return create_success_response_bool(true);
        }
    };

    // 使用task_manager的运行时来处理可能的阻塞操作
    let result = get_runtime().spawn_blocking(move || {
        internal_info!("FFI", "🚀 Starting logger initialization in async task");

        match LoggerConfig::from_json(&config_str) {
            Ok(config) => {
                internal_info!("FFI", "✅ Config parsed successfully");

                let mut logger = Logger::new();

                // 设置大文件回调
                logger.set_large_file_callback(|file_path: String| {
                    notify_large_file_event(&file_path);
                });

                internal_info!("FFI", "🔄 Calling logger.initialize()...");
                match logger.initialize(config) {
                    Ok(_) => {
                        internal_info!("FFI", "✅ Logger.initialize() succeeded");
                        GLOBAL_LOGGER.set(Mutex::new(logger)).unwrap_or_else(|_| {
                            internal_error!("FFI", "❌ Logger already initialized");
                        });
                        internal_info!("FFI", "🎉 Logger initialized successfully");
                        true
                    }
                    Err(e) => {
                        internal_error!("FFI", "❌ Failed to initialize logger: {}", e);
                        false
                    }
                }
            }
            Err(e) => {
                internal_error!("FFI", "❌ Failed to parse config: {}", e);
                false
            }
        }
    }).await;

    match result {
        Ok(success) => {
            log::info!("✅ handle_initialize_async completed with success: {}", success);
            create_success_response_bool(success)
        }
        Err(e) => {
            log::error!("❌ handle_initialize_async failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

/// 处理写入日志（异步版本）
async fn handle_write_log_async(params: &HashMap<String, String>) -> Vec<u8> {
    let log_entry_str = match params.get("log_entry") {
        Some(entry) => entry.clone(),
        None => {
            log::error!("Missing log_entry parameter in async write_log");
            return create_success_response_bool(false);
        }
    };

    // 使用异步任务执行
    let result = get_runtime().spawn_blocking(move || {
        if let Ok(log_entry) = serde_json::from_str::<LogEntry>(&log_entry_str) {
            if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
                if let Ok(logger) = logger_mutex.lock() {
                    return logger.write_log(log_entry).is_ok();
                }
            }
        }
        false
    }).await;

    match result {
        Ok(success) => create_success_response_bool(success),
        Err(e) => {
            log::error!("Async write_log failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

/// 处理更新用户ID（异步版本）
async fn handle_update_user_id_async(params: &HashMap<String, String>) -> Vec<u8> {
    let user_id = match params.get("user_id") {
        Some(id) => id.clone(),
        None => {
            log::error!("Missing user_id parameter in async update_user_id");
            return create_success_response_bool(false);
        }
    };

    let result = get_runtime().spawn_blocking(move || {
        if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                return logger.update_user_id(user_id).is_ok();
            }
        }
        false
    }).await;

    match result {
        Ok(success) => create_success_response_bool(success),
        Err(e) => {
            log::error!("Async update_user_id failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

/// 处理写入崩溃日志（异步版本）
async fn handle_write_crash_log_async(params: &HashMap<String, String>) -> Vec<u8> {
    let crash_info = match params.get("crash_info") {
        Some(info) => info.clone(),
        None => {
            log::error!("Missing crash_info parameter in async write_crash_log");
            return create_success_response_bool(false);
        }
    };

    let result = get_runtime().spawn_blocking(move || {
        if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                return logger.write_crash_log(crash_info).is_ok();
            }
        }
        false
    }).await;

    match result {
        Ok(success) => create_success_response_bool(success),
        Err(e) => {
            log::error!("Async write_crash_log failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

// is_initialized 接口已删除 - 不需要此接口

/// 处理压缩日志文件（带回调版本）
fn handle_compress_logs_with_callback(
    params: &HashMap<String, String>,
    consumer: impl PlatformConsumer + Send + Sync + 'static
) -> Vec<u8> {
    let days = params.get("days")
        .and_then(|d| d.parse::<u32>().ok())
        .unwrap_or(7);

    std::thread::spawn(move || {
        let result = if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                match logger.compress_logs_for_upload(days) {
                    Ok(zip_path) => {
                        log::info!("Logs compressed successfully: {}", zip_path);
                        create_success_response_string(zip_path)
                    }
                    Err(e) => {
                        log::error!("Failed to compress logs: {}", e);
                        create_error_response(&format!("Failed to compress logs: {}", e))
                    }
                }
            } else {
                log::error!("Failed to lock logger mutex for compress_logs");
                create_error_response("Failed to lock logger mutex")
            }
        } else {
            log::error!("Logger not initialized for compress_logs");
            create_error_response("Logger not initialized")
        };

        // 通过callback通知前端结果
        consumer.accept(result);
    });

    create_success_response_bool(true)
}

/// 处理压缩崩溃日志（带回调版本）
fn handle_compress_crash_logs_with_callback(
    _params: &HashMap<String, String>,
    consumer: impl PlatformConsumer + Send + Sync + 'static
) -> Vec<u8> {
    std::thread::spawn(move || {
        let result = if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                match logger.compress_crash_logs() {
                    Ok(zip_path) => {
                        log::info!("Crash logs compressed successfully: {}", zip_path);
                        create_success_response_string(zip_path)
                    }
                    Err(e) => {
                        log::error!("Failed to compress crash logs: {}", e);
                        create_error_response(&format!("Failed to compress crash logs: {}", e))
                    }
                }
            } else {
                log::error!("Failed to lock logger mutex for compress_crash_logs");
                create_error_response("Failed to lock logger mutex")
            }
        } else {
            log::error!("Logger not initialized for compress_crash_logs");
            create_error_response("Logger not initialized")
        };

        // 通过callback通知前端结果
        consumer.accept(result);
    });

    create_success_response_bool(true)
}

/// 处理上传成功后清理（异步版本）
async fn handle_cleanup_after_upload_success_async(params: &HashMap<String, String>) -> Vec<u8> {
    let zip_path = match params.get("zip_path") {
        Some(path) => path.clone(),
        None => {
            log::error!("Missing zip_path parameter in async cleanup_after_upload_success");
            return create_success_response_bool(false);
        }
    };

    let result = get_runtime().spawn_blocking(move || {
        if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                return logger.cleanup_after_upload_success(zip_path).is_ok();
            }
        }
        false
    }).await;

    match result {
        Ok(success) => create_success_response_bool(success),
        Err(e) => {
            log::error!("Async cleanup_after_upload_success failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

/// 处理上传失败后清理（异步版本）
async fn handle_cleanup_after_upload_failure_async(params: &HashMap<String, String>) -> Vec<u8> {
    let zip_path = match params.get("zip_path") {
        Some(path) => path.clone(),
        None => {
            log::error!("Missing zip_path parameter in async cleanup_after_upload_failure");
            return create_success_response_bool(false);
        }
    };

    let result = get_runtime().spawn_blocking(move || {
        if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
            if let Ok(logger) = logger_mutex.lock() {
                return logger.cleanup_after_upload_failure(zip_path).is_ok();
            }
        }
        false
    }).await;

    match result {
        Ok(success) => create_success_response_bool(success),
        Err(e) => {
            log::error!("Async cleanup_after_upload_failure failed: {}", e);
            create_success_response_bool(false)
        }
    }
}

/// 处理添加大文件观察者
fn handle_add_large_file_observer(params: &HashMap<String, String>) -> Vec<u8> {
    let listener_id = params.get("listener_id").unwrap_or(&"0".to_string()).clone();

    // 返回监听器ID
    create_success_response_string(listener_id)
}

/// 添加大文件观察者实现
fn add_large_file_observer_impl(consumer: impl PlatformConsumer + Send + Sync + 'static) -> Vec<u8> {
    // 初始化观察者存储
    let observers = LARGE_FILE_OBSERVERS.get_or_init(|| {
        Arc::new(RwLock::new(HashMap::new()))
    });

    // 生成唯一ID
    let observer_id = format!("large_file_observer_{}", std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap_or_default()
        .as_millis());

    // 添加观察者
    {
        if let Ok(mut observers) = observers.write() {
            observers.insert(observer_id.clone(), Box::new(consumer));
        }
    }

    log::info!("Large file observer added with ID: {}", observer_id);
    create_success_response_string(observer_id)
}

/// 通知大文件事件
fn notify_large_file_event(file_path: &str) {
    log::info!("Large file detected, notifying frontend: {}", file_path);

    // 创建FlatBuffer格式的大文件事件
    let event_data = create_large_file_event_message(file_path);

    // 通过全局观察者通知前端
    if let Some(observers) = LARGE_FILE_OBSERVERS.get() {
        if let Ok(observers) = observers.read() {
            for observer in observers.values() {
                observer.accept(event_data.clone());
            }
        }
    }
}

/// 处理启用控制台日志（异步）
fn handle_enable_console_log(params: &HashMap<String, String>) -> Vec<u8> {
    let enable = match params.get("enable") {
        Some(enable_str) => enable_str.parse::<bool>().unwrap_or(false),
        None => return create_error_response("Missing enable parameter"),
    };

    if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
        if let Ok(logger) = logger_mutex.lock() {
            match logger.enable_console_log(enable) {
                Ok(_) => {
                    log::info!("Console log {} via async call", if enable { "enabled" } else { "disabled" });
                    create_success_response_none()
                },
                Err(e) => {
                    log::error!("Failed to enable console log: {}", e);
                    create_error_response(&format!("Failed to enable console log: {}", e))
                },
            }
        } else {
            create_error_response("Failed to lock logger mutex")
        }
    } else {
        create_error_response("Logger not initialized")
    }
}

/// 处理启用完整日志模式（异步）
fn handle_enable_full_logs(params: &HashMap<String, String>) -> Vec<u8> {
    let enable = match params.get("enable") {
        Some(enable_str) => enable_str.parse::<bool>().unwrap_or(false),
        None => return create_error_response("Missing enable parameter"),
    };

    if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
        if let Ok(logger) = logger_mutex.lock() {
            match logger.enable_full_logs(enable) {
                Ok(_) => {
                    log::info!("Full logs mode {} via async call", if enable { "enabled" } else { "disabled" });
                    create_success_response_none()
                },
                Err(e) => {
                    log::error!("Failed to enable full logs: {}", e);
                    create_error_response(&format!("Failed to enable full logs: {}", e))
                },
            }
        } else {
            create_error_response("Failed to lock logger mutex")
        }
    } else {
        create_error_response("Logger not initialized")
    }
}

/// 创建大文件事件消息（FlatBuffer格式）
fn create_large_file_event_message(file_path: &str) -> Vec<u8> {
    let mut builder = FlatBufferBuilder::new();

    // 先创建字符串
    let event_type_str = builder.create_string("large_file_detected");
    let file_path_str = builder.create_string(file_path);

    // 创建LargeFileEvent
    let large_file_event = LargeFileEvent::create(&mut builder, &LargeFileEventArgs {
        event_type: Some(event_type_str),
        file_path: Some(file_path_str),
        file_size: 0, // 可以后续添加文件大小信息
    });

    // 创建LoggerMessage
    let logger_message = LoggerMessage::create(&mut builder, &LoggerMessageArgs {
        code: 1001, // 大文件事件代码
        container_type: LoggerContainer::LargeFileEvent,
        container: Some(large_file_event.as_union_value()),
    });

    builder.finish(logger_message, None);
    builder.finished_data().to_vec()
}

/// 处理获取控制台日志状态
fn handle_is_console_log_enabled(_params: &HashMap<String, String>) -> Vec<u8> {
    if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
        if let Ok(logger) = logger_mutex.lock() {
            let enabled = logger.is_console_log_enabled();
            log::info!("Console log enabled status: {}", enabled);
            create_success_response_bool(enabled)
        } else {
            log::error!("Failed to lock logger mutex for is_console_log_enabled");
            create_error_response("Failed to lock logger mutex")
        }
    } else {
        log::error!("Logger not initialized for is_console_log_enabled");
        create_error_response("Logger not initialized")
    }
}

/// 处理获取完整日志模式状态
fn handle_is_full_logs_enabled(_params: &HashMap<String, String>) -> Vec<u8> {
    if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
        if let Ok(logger) = logger_mutex.lock() {
            let enabled = logger.is_full_logs_enabled();
            log::info!("Full logs enabled status: {}", enabled);
            create_success_response_bool(enabled)
        } else {
            log::error!("Failed to lock logger mutex for is_full_logs_enabled");
            create_error_response("Failed to lock logger mutex")
        }
    } else {
        log::error!("Logger not initialized for is_full_logs_enabled");
        create_error_response("Logger not initialized")
    }
}

/// 处理获取日志级别
fn handle_get_log_level(_params: &HashMap<String, String>) -> Vec<u8> {
    if let Some(logger_mutex) = GLOBAL_LOGGER.get() {
        if let Ok(logger) = logger_mutex.lock() {
            let level = logger.get_log_level();
            let level_int = level as i32;
            log::info!("Current log level: {:?} ({})", level, level_int);
            create_success_response_int32(level_int)
        } else {
            log::error!("Failed to lock logger mutex for get_log_level");
            create_error_response("Failed to lock logger mutex")
        }
    } else {
        log::error!("Logger not initialized for get_log_level");
        create_error_response("Logger not initialized")
    }
}
