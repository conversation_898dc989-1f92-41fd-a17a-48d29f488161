//! # 内部调试日志模块
//!
//! 为logger系统提供统一的内部调试日志接口，底层根据不同平台采用不同实现。
//! 当前实现HarmonyOS平台，未来可扩展到Android和iOS。

use std::env;
use std::sync::{Mutex, Once};

// HarmonyOS平台特定的导入和定义
#[cfg(feature = "ohos")]
use std::ffi::{CString, c_char};

#[cfg(feature = "ohos")]
#[repr(C)]
enum LogType {
    LogApp = 0,
}

#[cfg(feature = "ohos")]
#[repr(C)]
enum LogLevel {
    LogDebug = 3,
    LogInfo = 4,
    LogWarn = 5,
    LogError = 6,
    LogFatal = 7,
}

#[cfg(feature = "ohos")]
#[link(name = "hilog_ndk.z")]
extern "C" {
    fn OH_LOG_Print(log_type: LogType, log_level: LogLevel, domain: u32, tag: *const c_char, fmt: *const c_char) -> i32;
}

#[cfg(feature = "ohos")]
const INTERNAL_LOG_DOMAIN: u32 = 0x0122; // 使用不同的domain避免与业务日志冲突

/// 内部日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum InternalLogLevel {
    Error = 1,
    Warn = 2,
    Info = 3,
    Debug = 4,
    Trace = 5,
}

impl InternalLogLevel {
    /// 从字符串解析日志级别
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "error" => Some(InternalLogLevel::Error),
            "warn" => Some(InternalLogLevel::Warn),
            "info" => Some(InternalLogLevel::Info),
            "debug" => Some(InternalLogLevel::Debug),
            "trace" => Some(InternalLogLevel::Trace),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            InternalLogLevel::Error => "ERROR",
            InternalLogLevel::Warn => "WARN",
            InternalLogLevel::Info => "INFO",
            InternalLogLevel::Debug => "DEBUG",
            InternalLogLevel::Trace => "TRACE",
        }
    }
}

/// 内部日志器配置
struct InternalLoggerConfig {
    level: InternalLogLevel,
    enabled: bool,
}

impl Default for InternalLoggerConfig {
    fn default() -> Self {
        Self {
            level: InternalLogLevel::Warn, // 默认只输出警告和错误
            enabled: true,
        }
    }
}

/// 全局日志器实例
static LOGGER_INIT: Once = Once::new();
static LOGGER_CONFIG: Mutex<Option<InternalLoggerConfig>> = Mutex::new(None);

/// 初始化内部日志器
///
/// 支持通过环境变量控制：
/// - RUST_LOGGER_LOG: 设置日志级别 (error, warn, info, debug, trace)
/// - RUST_LOGGER_DISABLE: 设置为任意值可禁用内部日志
pub fn init() {
    LOGGER_INIT.call_once(|| {
        let mut config = InternalLoggerConfig::default();

        // 检查是否禁用日志
        if env::var("RUST_LOGGER_DISABLE").is_ok() {
            config.enabled = false;
        }

        // 检查日志级别设置
        if let Ok(level_str) = env::var("RUST_LOGGER_LOG") {
            if let Some(level) = InternalLogLevel::from_str(&level_str) {
                config.level = level;
            }
        }

        // 在开发环境下默认启用更详细的日志
        #[cfg(debug_assertions)]
        {
            if env::var("RUST_LOGGER_LOG").is_err() {
                config.level = InternalLogLevel::Info;
            }
        }

        *LOGGER_CONFIG.lock().unwrap() = Some(config);
    });
}

/// 检查是否应该记录指定级别的日志
pub fn is_enabled(level: InternalLogLevel) -> bool {
    init(); // 确保已初始化
    
    if let Ok(config_guard) = LOGGER_CONFIG.lock() {
        if let Some(config) = config_guard.as_ref() {
            return config.enabled && level <= config.level;
        }
    }
    false
}

/// 写入内部日志
pub fn log(level: InternalLogLevel, target: &str, message: &str) {
    if !is_enabled(level) {
        return;
    }

    // 格式化消息，包含目标信息
    let formatted_message = format!("[{}] {}", target, message);

    // 根据平台采用不同的实现
    platform_log(level, &formatted_message);
}

/// HarmonyOS平台的日志实现
#[cfg(feature = "ohos")]
fn platform_log(level: InternalLogLevel, message: &str) {
    let c_tag = CString::new("RUST_LOGGER_INTERNAL").unwrap_or_default();
    let c_message = CString::new(message).unwrap_or_default();

    let log_level = match level {
        InternalLogLevel::Error => LogLevel::LogError,
        InternalLogLevel::Warn => LogLevel::LogWarn,
        InternalLogLevel::Info => LogLevel::LogInfo,
        InternalLogLevel::Debug => LogLevel::LogDebug,
        InternalLogLevel::Trace => LogLevel::LogDebug, // HarmonyOS没有Trace级别，使用Debug
    };

    unsafe {
        OH_LOG_Print(LogType::LogApp, log_level, INTERNAL_LOG_DOMAIN, c_tag.as_ptr(), c_message.as_ptr());
    }
}

/// 非HarmonyOS平台的日志实现（Android/iOS/其他）
#[cfg(not(feature = "ohos"))]
fn platform_log(level: InternalLogLevel, message: &str) {
    // 对于非HarmonyOS平台，使用标准输出
    match level {
        InternalLogLevel::Error => eprintln!("[ERROR] {}", message),
        InternalLogLevel::Warn => eprintln!("[WARN] {}", message),
        InternalLogLevel::Info => println!("[INFO] {}", message),
        InternalLogLevel::Debug => println!("[DEBUG] {}", message),
        InternalLogLevel::Trace => println!("[TRACE] {}", message),
    }
}

/// 错误级别日志宏
#[macro_export]
macro_rules! internal_error {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Error,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 警告级别日志宏
#[macro_export]
macro_rules! internal_warn {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Warn,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 信息级别日志宏
#[macro_export]
macro_rules! internal_info {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Info,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 调试级别日志宏
#[macro_export]
macro_rules! internal_debug {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Debug,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 跟踪级别日志宏
#[macro_export]
macro_rules! internal_trace {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Trace,
            $target,
            &format!($($arg)*)
        );
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_log_level_parsing() {
        assert_eq!(InternalLogLevel::from_str("error"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("ERROR"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("warn"), Some(InternalLogLevel::Warn));
        assert_eq!(InternalLogLevel::from_str("info"), Some(InternalLogLevel::Info));
        assert_eq!(InternalLogLevel::from_str("debug"), Some(InternalLogLevel::Debug));
        assert_eq!(InternalLogLevel::from_str("trace"), Some(InternalLogLevel::Trace));
        assert_eq!(InternalLogLevel::from_str("invalid"), None);
    }

    #[test]
    fn test_log_level_ordering() {
        assert!(InternalLogLevel::Error < InternalLogLevel::Warn);
        assert!(InternalLogLevel::Warn < InternalLogLevel::Info);
        assert!(InternalLogLevel::Info < InternalLogLevel::Debug);
        assert!(InternalLogLevel::Debug < InternalLogLevel::Trace);
    }

    #[test]
    fn test_macros() {
        // 这些测试主要验证宏能正确编译
        internal_error!("test", "Error message: {}", "test");
        internal_warn!("test", "Warning message");
        internal_info!("test", "Info message");
        internal_debug!("test", "Debug message");
        internal_trace!("test", "Trace message");
    }

    #[test]
    fn test_initialization() {
        // 测试初始化功能
        init();
        assert!(is_enabled(InternalLogLevel::Error));
        assert!(is_enabled(InternalLogLevel::Warn));
    }
}
