//! # 内部调试日志模块
//!
//! 为logger系统提供跨平台的内部调试日志功能，避免使用平台特定的日志系统。
//! 参考env_logger的实现方式，支持通过环境变量控制日志级别和输出。

use std::env;
use std::io::{self, Write};
use std::sync::{Mutex, Once};
use chrono::{DateTime, Local};

/// 内部日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum InternalLogLevel {
    Error = 1,
    Warn = 2,
    Info = 3,
    Debug = 4,
    Trace = 5,
}

impl InternalLogLevel {
    /// 从字符串解析日志级别
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "error" => Some(InternalLogLevel::Error),
            "warn" => Some(InternalLogLevel::Warn),
            "info" => Some(InternalLogLevel::Info),
            "debug" => Some(InternalLogLevel::Debug),
            "trace" => Some(InternalLogLevel::Trace),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            InternalLogLevel::Error => "ERROR",
            InternalLogLevel::Warn => "WARN",
            InternalLogLevel::Info => "INFO",
            InternalLogLevel::Debug => "DEBUG",
            InternalLogLevel::Trace => "TRACE",
        }
    }
}

/// 内部日志器配置
struct InternalLoggerConfig {
    level: InternalLogLevel,
    enabled: bool,
}

impl Default for InternalLoggerConfig {
    fn default() -> Self {
        Self {
            level: InternalLogLevel::Warn, // 默认只输出警告和错误
            enabled: true,
        }
    }
}

/// 全局日志器实例
static LOGGER_INIT: Once = Once::new();
static LOGGER_CONFIG: Mutex<Option<InternalLoggerConfig>> = Mutex::new(None);

/// 初始化内部日志器
/// 
/// 支持通过环境变量控制：
/// - RUST_LOGGER_LOG: 设置日志级别 (error, warn, info, debug, trace)
/// - RUST_LOGGER_DISABLE: 设置为任意值可禁用内部日志
pub fn init() {
    LOGGER_INIT.call_once(|| {
        let mut config = InternalLoggerConfig::default();
        
        // 检查是否禁用日志
        if env::var("RUST_LOGGER_DISABLE").is_ok() {
            config.enabled = false;
        }
        
        // 检查日志级别设置
        if let Ok(level_str) = env::var("RUST_LOGGER_LOG") {
            if let Some(level) = InternalLogLevel::from_str(&level_str) {
                config.level = level;
            }
        }
        
        // 在开发环境下默认启用更详细的日志
        #[cfg(debug_assertions)]
        {
            if env::var("RUST_LOGGER_LOG").is_err() {
                config.level = InternalLogLevel::Info;
            }
        }
        
        *LOGGER_CONFIG.lock().unwrap() = Some(config);
    });
}

/// 检查是否应该记录指定级别的日志
pub fn is_enabled(level: InternalLogLevel) -> bool {
    init(); // 确保已初始化
    
    if let Ok(config_guard) = LOGGER_CONFIG.lock() {
        if let Some(config) = config_guard.as_ref() {
            return config.enabled && level <= config.level;
        }
    }
    false
}

/// 写入内部日志
pub fn log(level: InternalLogLevel, target: &str, message: &str) {
    if !is_enabled(level) {
        return;
    }
    
    let now: DateTime<Local> = Local::now();
    let timestamp = now.format("%Y-%m-%d %H:%M:%S%.3f");
    
    // 格式: [时间戳] [级别] [目标] 消息
    let log_line = format!("[{}] [{}] [{}] {}\n", 
                          timestamp, 
                          level.as_str(), 
                          target, 
                          message);
    
    // 根据级别选择输出流
    match level {
        InternalLogLevel::Error => {
            let _ = io::stderr().write_all(log_line.as_bytes());
            let _ = io::stderr().flush();
        }
        _ => {
            let _ = io::stdout().write_all(log_line.as_bytes());
            let _ = io::stdout().flush();
        }
    }
}

/// 错误级别日志宏
#[macro_export]
macro_rules! internal_error {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Error,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 警告级别日志宏
#[macro_export]
macro_rules! internal_warn {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Warn,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 信息级别日志宏
#[macro_export]
macro_rules! internal_info {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Info,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 调试级别日志宏
#[macro_export]
macro_rules! internal_debug {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Debug,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 跟踪级别日志宏
#[macro_export]
macro_rules! internal_trace {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Trace,
            $target,
            &format!($($arg)*)
        );
    };
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_log_level_parsing() {
        assert_eq!(InternalLogLevel::from_str("error"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("ERROR"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("warn"), Some(InternalLogLevel::Warn));
        assert_eq!(InternalLogLevel::from_str("info"), Some(InternalLogLevel::Info));
        assert_eq!(InternalLogLevel::from_str("debug"), Some(InternalLogLevel::Debug));
        assert_eq!(InternalLogLevel::from_str("trace"), Some(InternalLogLevel::Trace));
        assert_eq!(InternalLogLevel::from_str("invalid"), None);
    }
    
    #[test]
    fn test_log_level_ordering() {
        assert!(InternalLogLevel::Error < InternalLogLevel::Warn);
        assert!(InternalLogLevel::Warn < InternalLogLevel::Info);
        assert!(InternalLogLevel::Info < InternalLogLevel::Debug);
        assert!(InternalLogLevel::Debug < InternalLogLevel::Trace);
    }
    
    #[test]
    fn test_macros() {
        // 这些测试主要验证宏能正确编译
        internal_error!("test", "Error message: {}", "test");
        internal_warn!("test", "Warning message");
        internal_info!("test", "Info message");
        internal_debug!("test", "Debug message");
        internal_trace!("test", "Trace message");
    }
}
