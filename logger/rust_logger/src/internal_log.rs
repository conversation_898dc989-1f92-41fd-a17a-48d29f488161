//! # 内部调试日志模块
//!
//! 为logger系统提供统一的内部调试日志接口，底层根据不同平台采用不同实现。
//! 当前实现HarmonyOS平台，未来可扩展到Android和iOS。
//!
//! ## 编译时优化
//!
//! 支持通过编译时特性来过滤日志级别，提高性能：
//! - `internal_log_level_error`: 只编译ERROR级别日志
//! - `internal_log_level_warn`: 编译ERROR和WARN级别日志
//! - `internal_log_level_info`: 编译ERROR、WARN和INFO级别日志
//! - `internal_log_level_debug`: 编译所有级别日志（默认）
//!
//! 使用方法：
//! ```bash
//! # 只编译ERROR级别的内部日志
//! cargo build --features internal_log_level_error
//!
//! # 编译ERROR和WARN级别的内部日志
//! cargo build --features internal_log_level_warn
//!
//! # 编译所有级别的内部日志（默认）
//! cargo build
//! ```

use std::env;
use std::sync::{Mutex, Once};

// 编译时日志级别配置
#[cfg(feature = "internal_log_level_error")]
const COMPILE_TIME_LOG_LEVEL: InternalLogLevel = InternalLogLevel::Error;

#[cfg(all(feature = "internal_log_level_warn", not(feature = "internal_log_level_error")))]
const COMPILE_TIME_LOG_LEVEL: InternalLogLevel = InternalLogLevel::Warn;

#[cfg(all(feature = "internal_log_level_info", not(any(feature = "internal_log_level_error", feature = "internal_log_level_warn"))))]
const COMPILE_TIME_LOG_LEVEL: InternalLogLevel = InternalLogLevel::Info;

// Debug级别是最高级别，不需要单独的特性配置

#[cfg(not(any(feature = "internal_log_level_error", feature = "internal_log_level_warn", feature = "internal_log_level_info")))]
const COMPILE_TIME_LOG_LEVEL: InternalLogLevel = InternalLogLevel::Debug;

// HarmonyOS平台特定的导入和定义
#[cfg(feature = "ohos")]
use std::ffi::{CString, c_char};

#[cfg(feature = "ohos")]
#[repr(C)]
enum LogType {
    LogApp = 0,
}

#[cfg(feature = "ohos")]
#[repr(C)]
enum LogLevel {
    LogDebug = 3,
    LogInfo = 4,
    LogWarn = 5,
    LogError = 6,
    LogFatal = 7,
}

#[cfg(feature = "ohos")]
#[link(name = "hilog_ndk.z")]
extern "C" {
    fn OH_LOG_Print(log_type: LogType, log_level: LogLevel, domain: u32, tag: *const c_char, fmt: *const c_char) -> i32;
}

#[cfg(feature = "ohos")]
const INTERNAL_LOG_DOMAIN: u32 = 0x0122; // 使用不同的domain避免与业务日志冲突

/// 内部日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum InternalLogLevel {
    Error = 1,
    Warn = 2,
    Info = 3,
    Debug = 4,
}

impl InternalLogLevel {
    /// 从字符串解析日志级别
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "error" => Some(InternalLogLevel::Error),
            "warn" => Some(InternalLogLevel::Warn),
            "info" => Some(InternalLogLevel::Info),
            "debug" => Some(InternalLogLevel::Debug),
            _ => None,
        }
    }

    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            InternalLogLevel::Error => "ERROR",
            InternalLogLevel::Warn => "WARN",
            InternalLogLevel::Info => "INFO",
            InternalLogLevel::Debug => "DEBUG",
        }
    }
}

/// 内部日志器配置
struct InternalLoggerConfig {
    level: InternalLogLevel,
    enabled: bool,
}

impl Default for InternalLoggerConfig {
    fn default() -> Self {
        Self {
            level: InternalLogLevel::Warn, // 默认只输出警告和错误
            enabled: true,
        }
    }
}

/// 全局日志器实例
static LOGGER_INIT: Once = Once::new();
static LOGGER_CONFIG: Mutex<Option<InternalLoggerConfig>> = Mutex::new(None);

/// 初始化内部日志器
///
/// 支持通过环境变量控制：
/// - RUST_LOGGER_LOG: 设置日志级别 (error, warn, info, debug)
/// - RUST_LOGGER_DISABLE: 设置为任意值可禁用内部日志
pub fn init() {
    LOGGER_INIT.call_once(|| {
        let mut config = InternalLoggerConfig::default();

        // 检查是否禁用日志
        if env::var("RUST_LOGGER_DISABLE").is_ok() {
            config.enabled = false;
        }

        // 检查日志级别设置
        if let Ok(level_str) = env::var("RUST_LOGGER_LOG") {
            if let Some(level) = InternalLogLevel::from_str(&level_str) {
                config.level = level;
            }
        }

        // 在开发环境下默认启用更详细的日志
        #[cfg(debug_assertions)]
        {
            if env::var("RUST_LOGGER_LOG").is_err() {
                config.level = InternalLogLevel::Info;
            }
        }

        *LOGGER_CONFIG.lock().unwrap() = Some(config);
    });
}

/// 检查是否应该记录指定级别的日志
/// 结合编译时和运行时过滤
pub fn is_enabled(level: InternalLogLevel) -> bool {
    // 编译时过滤：如果级别高于编译时设置的级别，直接返回false
    if level > COMPILE_TIME_LOG_LEVEL {
        return false;
    }

    init(); // 确保已初始化

    if let Ok(config_guard) = LOGGER_CONFIG.lock() {
        if let Some(config) = config_guard.as_ref() {
            return config.enabled && level <= config.level;
        }
    }
    false
}

/// 写入内部日志
pub fn log(level: InternalLogLevel, target: &str, message: &str) {
    if !is_enabled(level) {
        return;
    }

    // 格式化消息，包含目标信息
    let formatted_message = format!("[{}] {}", target, message);

    // 根据平台采用不同的实现
    platform_log(level, &formatted_message);
}

/// HarmonyOS平台的日志实现
#[cfg(feature = "ohos")]
fn platform_log(level: InternalLogLevel, message: &str) {
    let c_tag = CString::new("RUST_LOGGER_INTERNAL").unwrap_or_default();
    let c_message = CString::new(message).unwrap_or_default();

    let log_level = match level {
        InternalLogLevel::Error => LogLevel::LogError,
        InternalLogLevel::Warn => LogLevel::LogWarn,
        InternalLogLevel::Info => LogLevel::LogInfo,
        InternalLogLevel::Debug => LogLevel::LogDebug,
    };

    unsafe {
        OH_LOG_Print(LogType::LogApp, log_level, INTERNAL_LOG_DOMAIN, c_tag.as_ptr(), c_message.as_ptr());
    }
}

/// 非HarmonyOS平台的日志实现（Android/iOS/其他）
#[cfg(not(feature = "ohos"))]
fn platform_log(level: InternalLogLevel, message: &str) {
    // 对于非HarmonyOS平台，使用标准输出
    match level {
        InternalLogLevel::Error => eprintln!("[ERROR] {}", message),
        InternalLogLevel::Warn => eprintln!("[WARN] {}", message),
        InternalLogLevel::Info => println!("[INFO] {}", message),
        InternalLogLevel::Debug => println!("[DEBUG] {}", message),
    }
}

/// 错误级别日志宏
#[macro_export]
macro_rules! internal_error {
    ($target:expr, $($arg:tt)*) => {
        $crate::internal_log::log(
            $crate::internal_log::InternalLogLevel::Error,
            $target,
            &format!($($arg)*)
        );
    };
}

/// 警告级别日志宏
#[macro_export]
macro_rules! internal_warn {
    ($target:expr, $($arg:tt)*) => {
        #[cfg(not(feature = "internal_log_level_error"))]
        {
            $crate::internal_log::log(
                $crate::internal_log::InternalLogLevel::Warn,
                $target,
                &format!($($arg)*)
            );
        }
    };
}

/// 信息级别日志宏
#[macro_export]
macro_rules! internal_info {
    ($target:expr, $($arg:tt)*) => {
        #[cfg(not(any(feature = "internal_log_level_error", feature = "internal_log_level_warn")))]
        {
            $crate::internal_log::log(
                $crate::internal_log::InternalLogLevel::Info,
                $target,
                &format!($($arg)*)
            );
        }
    };
}

/// 调试级别日志宏
#[macro_export]
macro_rules! internal_debug {
    ($target:expr, $($arg:tt)*) => {
        #[cfg(not(any(
            feature = "internal_log_level_error",
            feature = "internal_log_level_warn",
            feature = "internal_log_level_info"
        )))]
        {
            $crate::internal_log::log(
                $crate::internal_log::InternalLogLevel::Debug,
                $target,
                &format!($($arg)*)
            );
        }
    };
}



#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_log_level_parsing() {
        assert_eq!(InternalLogLevel::from_str("error"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("ERROR"), Some(InternalLogLevel::Error));
        assert_eq!(InternalLogLevel::from_str("warn"), Some(InternalLogLevel::Warn));
        assert_eq!(InternalLogLevel::from_str("info"), Some(InternalLogLevel::Info));
        assert_eq!(InternalLogLevel::from_str("debug"), Some(InternalLogLevel::Debug));
        assert_eq!(InternalLogLevel::from_str("invalid"), None);
    }

    #[test]
    fn test_log_level_ordering() {
        assert!(InternalLogLevel::Error < InternalLogLevel::Warn);
        assert!(InternalLogLevel::Warn < InternalLogLevel::Info);
        assert!(InternalLogLevel::Info < InternalLogLevel::Debug);
    }

    #[test]
    fn test_macros() {
        // 这些测试主要验证宏能正确编译
        internal_error!("test", "Error message: {}", "test");
        internal_warn!("test", "Warning message");
        internal_info!("test", "Info message");
        internal_debug!("test", "Debug message");
    }

    #[test]
    fn test_initialization() {
        // 验证配置是否正确初始化
        init();

        if let Ok(config_guard) = LOGGER_CONFIG.lock() {
            assert!(config_guard.is_some(), "Config should be initialized");
            // 由于Once的限制，配置可能已经被其他测试设置，我们只验证基本功能
        }

        // 验证编译时级别
        assert!(InternalLogLevel::Error <= COMPILE_TIME_LOG_LEVEL);
        assert!(InternalLogLevel::Warn <= COMPILE_TIME_LOG_LEVEL);
    }

    #[test]
    fn test_compile_time_optimization() {
        // 测试编译时优化
        // 这个测试验证编译时级别过滤是否正常工作

        // 在默认情况下（无特性），所有级别都应该被编译
        #[cfg(not(any(
            feature = "internal_log_level_error",
            feature = "internal_log_level_warn",
            feature = "internal_log_level_info"
        )))]
        {
            assert_eq!(COMPILE_TIME_LOG_LEVEL, InternalLogLevel::Debug);
        }

        // 当启用error级别时，编译时级别应该是Error
        #[cfg(feature = "internal_log_level_error")]
        {
            assert_eq!(COMPILE_TIME_LOG_LEVEL, InternalLogLevel::Error);
        }

        // 当启用warn级别时，编译时级别应该是Warn
        #[cfg(all(feature = "internal_log_level_warn", not(feature = "internal_log_level_error")))]
        {
            assert_eq!(COMPILE_TIME_LOG_LEVEL, InternalLogLevel::Warn);
        }
    }

    #[test]
    fn test_performance_with_disabled_logs() {
        use std::time::Instant;

        // 测试当日志被禁用时的性能
        std::env::set_var("RUST_LOGGER_DISABLE", "1");
        init();

        let start = Instant::now();

        // 执行大量日志调用
        for i in 0..10000 {
            internal_debug!("PERF_TEST", "Debug message {}", i);
            internal_info!("PERF_TEST", "Info message {}", i);
            internal_warn!("PERF_TEST", "Warn message {}", i);
        }

        let duration = start.elapsed();
        println!("Performance test with disabled logs took: {:?}", duration);

        // 清理环境变量
        std::env::remove_var("RUST_LOGGER_DISABLE");

        // 当日志被禁用时，性能应该很好（这里只是一个示例断言）
        assert!(duration.as_millis() < 1000, "Disabled logs should be very fast");
    }
}
