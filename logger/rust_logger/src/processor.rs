use std::sync::Arc;
use crate::{Result, LoggerConfig};
use crate::core::{LogEntry, LogLevel};

/// 日志处理器特征
pub trait LogProcessor: Send + Sync {
    /// 处理日志条目
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry>;
    
    /// 获取处理器名称（用于调试）
    fn name(&self) -> &'static str;
    
    /// 是否启用此处理器
    fn is_enabled(&self, _config: &LoggerConfig) -> bool {
        true
    }
}

/// 日志处理链
pub struct LogProcessorChain {
    processors: Vec<Arc<dyn LogProcessor>>,
}

impl LogProcessorChain {
    /// 创建新的处理链
    pub fn new() -> Self {
        Self {
            processors: Vec::new(),
        }
    }
    
    /// 添加处理器
    pub fn add_processor(mut self, processor: Arc<dyn LogProcessor>) -> Self {
        self.processors.push(processor);
        self
    }
    
    /// 处理日志条目
    pub fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        for processor in &self.processors {
            if processor.is_enabled(config) {
                log::trace!("Processing with: {}", processor.name());
                entry = processor.process(entry, config)?;
            } else {
                log::trace!("Skipping disabled processor: {}", processor.name());
            }
        }
        Ok(entry)
    }
    
    /// 获取启用的处理器数量
    pub fn enabled_count(&self, config: &LoggerConfig) -> usize {
        self.processors.iter()
            .filter(|p| p.is_enabled(config))
            .count()
    }
    
    /// 获取所有处理器名称
    pub fn processor_names(&self) -> Vec<&'static str> {
        self.processors.iter()
            .map(|p| p.name())
            .collect()
    }
}

impl Default for LogProcessorChain {
    fn default() -> Self {
        Self::new()
    }
}

/// 简单脱敏处理器（用于处理链）
pub struct SimpleSensitiveDataProcessor;

impl LogProcessor for SensitiveDataProcessor {
    fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        if config.disable_sensitive_words {
            // 如果禁用脱敏，直接返回
            return Ok(entry);
        }
        
        // 脱敏处理逻辑
        entry.message = self.desensitize_message(&entry.message);

        // 脱敏标签
        entry.tag = self.desensitize_message(&entry.tag);

        // 脱敏参数
        for arg in &mut entry.args {
            *arg = self.desensitize_message(arg);
        }
        
        Ok(entry)
    }
    
    fn name(&self) -> &'static str {
        "SensitiveDataProcessor"
    }
    
    fn is_enabled(&self, config: &LoggerConfig) -> bool {
        !config.disable_sensitive_words
    }
}

impl SensitiveDataProcessor {
    /// 脱敏消息内容
    fn desensitize_message(&self, message: &str) -> String {
        let mut result = message.to_string();
        
        // 手机号脱敏：保留前3位和后4位（使用单词边界避免匹配身份证号的一部分）
        let phone_regex = regex::Regex::new(r"\b1[3-9]\d{9}\b").unwrap();
        result = phone_regex.replace_all(&result, |caps: &regex::Captures| {
            let phone = &caps[0];
            format!("{}****{}", &phone[..3], &phone[7..])
        }).to_string();
        
        // 身份证号脱敏：保留前6位和后4位
        let id_regex = regex::Regex::new(r"\d{18}").unwrap();
        result = id_regex.replace_all(&result, |caps: &regex::Captures| {
            let id = &caps[0];
            // 18位身份证：前6位 + 8个* + 后4位
            format!("{}********{}", &id[..6], &id[14..])
        }).to_string();
        
        // 邮箱脱敏：保留用户名前2位和域名
        let email_regex = regex::Regex::new(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}").unwrap();
        result = email_regex.replace_all(&result, |caps: &regex::Captures| {
            let email = &caps[0];
            if let Some(at_pos) = email.find('@') {
                let username = &email[..at_pos];
                let domain = &email[at_pos..];
                if username.len() > 2 {
                    format!("{}***{}", &username[..2], domain)
                } else {
                    format!("***{}", domain)
                }
            } else {
                email.to_string()
            }
        }).to_string();
        
        result
    }
}

/// 格式化处理器
pub struct FormattingProcessor;

impl LogProcessor for FormattingProcessor {
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 这里可以添加格式化逻辑，比如：
        // - 添加时间戳格式化
        // - 添加会话ID
        // - 添加设备信息
        // - 统一消息格式
        
        let mut processed_entry = entry;
        
        // 添加环境信息到消息中
        if config.test_mode {
            processed_entry.message = format!("[TEST] {}", processed_entry.message);
        }
        
        // 添加环境标识到tag中
        processed_entry.tag = format!("{} [env:{:?}]", processed_entry.tag, config.log_env);
        
        Ok(processed_entry)
    }
    
    fn name(&self) -> &'static str {
        "FormattingProcessor"
    }
}

/// 长度限制处理器
pub struct LengthLimitProcessor;

impl LogProcessor for LengthLimitProcessor {
    fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        let max_length = config.max_log_length;
        
        if entry.message.len() > max_length {
            entry.message.truncate(max_length - 3);
            entry.message.push_str("...");
        }
        
        // 限制标签长度
        if entry.tag.len() > 100 {
            entry.tag.truncate(97);
            entry.tag.push_str("...");
        }

        // 限制参数长度
        for arg in &mut entry.args {
            if arg.len() > 200 {
                arg.truncate(197);
                arg.push_str("...");
            }
        }
        
        Ok(entry)
    }
    
    fn name(&self) -> &'static str {
        "LengthLimitProcessor"
    }
}



/// 条件过滤处理器
pub struct ConditionalFilterProcessor;

impl LogProcessor for ConditionalFilterProcessor {
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 根据配置过滤某些日志
        if !config.privacy_agreed && entry.level as u8 <= LogLevel::Info as u8 {
            // 如果用户未同意隐私协议，过滤掉Info及以下级别的日志
            return Err(crate::error::LoggerError::config("Privacy not agreed, filtering log"));
        }
        
        Ok(entry)
    }
    
    fn name(&self) -> &'static str {
        "ConditionalFilterProcessor"
    }
    
    fn is_enabled(&self, _config: &LoggerConfig) -> bool {
        true // 总是启用，但内部会根据条件决定是否过滤
    }
}

/// 创建默认的处理链
pub fn create_default_processor_chain() -> LogProcessorChain {
    LogProcessorChain::new()
        .add_processor(Arc::new(ConditionalFilterProcessor))      // 1. 条件过滤
        .add_processor(Arc::new(SensitiveDataProcessor))          // 2. 脱敏处理
        .add_processor(Arc::new(FormattingProcessor))             // 3. 格式化
        .add_processor(Arc::new(LengthLimitProcessor))            // 4. 长度限制
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::{LogLevel, LogEntry};
    
    #[test]
    fn test_processor_chain() {
        let chain = create_default_processor_chain();
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true); // 设置隐私协议同意
        
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Test message with phone 13812345678".to_string(),
            vec!["arg1".to_string()],
            vec![false],
        );

        let result = chain.process(entry, &config).unwrap();

        // 验证脱敏效果
        assert!(result.message.contains("138****5678"));

        // 验证格式化效果
        assert!(result.tag.contains("env:"));

        // 验证处理链正常工作
        assert!(!result.args.is_empty());
    }
    
    #[test]
    fn test_sensitive_data_processor() {
        let processor = SensitiveDataProcessor;
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true);
        
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Phone: 13812345678, Email: <EMAIL>, ID: 110101199001011234".to_string(),
            vec![],
            vec![],
        );
        
        let result = processor.process(entry, &config).unwrap();
        


        assert!(result.message.contains("138****5678"));
        assert!(result.message.contains("te***@example.com"));
        assert!(result.message.contains("110101********1234"));
    }
}
