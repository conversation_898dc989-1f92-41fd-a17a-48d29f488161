use std::sync::Arc;
use std::time::Instant;
use std::collections::VecDeque;
use parking_lot::Mutex;
use crate::{Result, LoggerConfig};
use crate::core::{LogEntry, LogLevel};
use crate::sensitive_data::SensitiveDataProcessor;

/// 日志处理器特征
pub trait LogProcessor: Send + Sync {
    /// 处理日志条目
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry>;
    
    /// 获取处理器名称（用于调试）
    fn name(&self) -> &'static str;
    
    /// 是否启用此处理器
    fn is_enabled(&self, _config: &LoggerConfig) -> bool {
        true
    }
}

/// 日志处理链
pub struct LogProcessorChain {
    processors: Vec<Arc<dyn LogProcessor>>,
}

impl LogProcessorChain {
    /// 创建新的处理链
    pub fn new() -> Self {
        Self {
            processors: Vec::new(),
        }
    }
    
    /// 添加处理器
    pub fn add_processor(mut self, processor: Arc<dyn LogProcessor>) -> Self {
        self.processors.push(processor);
        self
    }
    
    /// 处理日志条目
    pub fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        for processor in &self.processors {
            if processor.is_enabled(config) {
                entry = processor.process(entry, config)?;
            }
        }
        Ok(entry)
    }
    
    /// 获取启用的处理器数量
    pub fn enabled_count(&self, config: &LoggerConfig) -> usize {
        self.processors.iter()
            .filter(|p| p.is_enabled(config))
            .count()
    }
    
    /// 获取所有处理器名称
    pub fn processor_names(&self) -> Vec<&'static str> {
        self.processors.iter()
            .map(|p| p.name())
            .collect()
    }
}

impl Default for LogProcessorChain {
    fn default() -> Self {
        Self::new()
    }
}

/// 敏感信息处理器适配器（将sensitive_data.rs中的处理器适配到责任链）
pub struct SensitiveDataProcessorAdapter {
    processor: std::sync::Mutex<SensitiveDataProcessor>,
    initialized: std::sync::atomic::AtomicBool,
}

impl SensitiveDataProcessorAdapter {
    pub fn new() -> Self {
        Self {
            processor: std::sync::Mutex::new(SensitiveDataProcessor::new()),
            initialized: std::sync::atomic::AtomicBool::new(false),
        }
    }

    fn ensure_initialized(&self, config: &LoggerConfig) -> Result<()> {
        if !self.initialized.load(std::sync::atomic::Ordering::Relaxed) {
            let mut processor = self.processor.lock().unwrap();
            processor.initialize(config)?;
            self.initialized.store(true, std::sync::atomic::Ordering::Relaxed);
        }
        Ok(())
    }
}

impl LogProcessor for SensitiveDataProcessorAdapter {
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        if config.disable_sensitive_words {
            // 如果禁用脱敏，直接返回
            return Ok(entry);
        }

        // 确保处理器已初始化
        self.ensure_initialized(config)?;

        // 使用完整的敏感信息处理器
        let processor = self.processor.lock().unwrap();
        processor.process_entry(entry, config)
    }
    
    fn name(&self) -> &'static str {
        "SensitiveDataProcessor"
    }
    
    fn is_enabled(&self, config: &LoggerConfig) -> bool {
        !config.disable_sensitive_words && config.privacy_agreed
    }
}

/// 条件过滤处理器
pub struct ConditionalFilterProcessor;

impl LogProcessor for ConditionalFilterProcessor {
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 根据配置过滤某些日志
        if !config.privacy_agreed && entry.level as u8 <= LogLevel::Info as u8 {
            // 如果用户未同意隐私协议，过滤掉Info及以下级别的日志
            return Err(crate::error::LoggerError::config("Privacy not agreed, filtering log"));
        }
        
        Ok(entry)
    }
    
    fn name(&self) -> &'static str {
        "ConditionalFilterProcessor"
    }
    
    fn is_enabled(&self, _config: &LoggerConfig) -> bool {
        true // 总是启用，但内部会根据条件决定是否过滤
    }
}

/// 格式化处理器
pub struct FormattingProcessor;

impl LogProcessor for FormattingProcessor {
    fn process(&self, entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        let mut processed_entry = entry;
        
        // 添加环境信息到消息中
        if config.test_mode {
            processed_entry.message = format!("[TEST] {}", processed_entry.message);
        }
        
        // 添加环境标识到tag中
        processed_entry.tag = format!("{} [env:{:?}]", processed_entry.tag, config.log_env);
        
        Ok(processed_entry)
    }
    
    fn name(&self) -> &'static str {
        "FormattingProcessor"
    }
}

/// 长度限制和限流处理器
pub struct LengthLimitProcessor {
    timestamps: Mutex<VecDeque<Instant>>,
}

impl LengthLimitProcessor {
    pub fn new() -> Self {
        Self {
            timestamps: Mutex::new(VecDeque::new()),
        }
    }

    fn allow_log(&self, max_logs_per_second: u32) -> bool {
        // 如果max_logs_per_second为0，表示不限流
        if max_logs_per_second == 0 {
            return true;
        }

        let mut timestamps = self.timestamps.lock();
        let now = Instant::now();

        // 移除超过1秒的时间戳
        while let Some(&front) = timestamps.front() {
            if now.duration_since(front).as_secs() >= 1 {
                timestamps.pop_front();
            } else {
                break;
            }
        }

        // 检查是否超过限制
        if timestamps.len() >= max_logs_per_second as usize {
            false
        } else {
            timestamps.push_back(now);
            true
        }
    }
}

impl LogProcessor for LengthLimitProcessor {
    fn process(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 1. 首先检查限流
        if !self.allow_log(config.max_logs_per_second) {
            return Err(crate::error::LoggerError::config("Rate limit exceeded"));
        }

        // 2. 然后处理长度限制
        let max_length = config.max_log_length;

        if entry.message.len() > max_length {
            entry.message.truncate(max_length - 3);
            entry.message.push_str("...");
        }

        // 限制标签长度
        if entry.tag.len() > 100 {
            entry.tag.truncate(97);
            entry.tag.push_str("...");
        }

        // 限制参数长度
        for arg in &mut entry.args {
            if arg.len() > 200 {
                arg.truncate(197);
                arg.push_str("...");
            }
        }

        Ok(entry)
    }

    fn name(&self) -> &'static str {
        "LengthLimitAndRateLimitProcessor"
    }
}



/// 创建默认的处理链
pub fn create_default_processor_chain() -> Result<LogProcessorChain> {
    Ok(LogProcessorChain::new()
        .add_processor(Arc::new(ConditionalFilterProcessor))      // 1. 条件过滤
        .add_processor(Arc::new(SensitiveDataProcessorAdapter::new())) // 2. 敏感信息脱敏
        .add_processor(Arc::new(FormattingProcessor))             // 3. 格式化
        .add_processor(Arc::new(LengthLimitProcessor::new())))    // 4. 长度限制和限流
}

/// 初始化处理链中需要初始化的处理器
pub fn initialize_processor_chain(_chain: &LogProcessorChain, _config: &LoggerConfig) -> Result<()> {
    // 这里可以遍历处理器并初始化需要初始化的处理器
    // 目前只有SensitiveDataProcessorAdapter需要初始化，但由于trait限制，
    // 我们在create_default_processor_chain中已经创建了初始化好的实例
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::{LogLevel, LogEntry};

    #[test]
    fn test_processor_chain() {
        let chain = create_default_processor_chain().unwrap();
        let mut config = crate::config::LoggerConfig::new();
        config.set_privacy_agreed(true); // 设置隐私协议同意

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Test message with phone 13812345678".to_string(),
            vec!["arg1".to_string()],
            vec![false],
        );

        let result = chain.process(entry, &config).unwrap();

        // 验证脱敏效果
        assert!(result.message.contains("138****5678"));

        // 验证格式化效果
        assert!(result.tag.contains("env:"));

        // 验证处理链正常工作
        assert!(!result.args.is_empty());
    }

    #[test]
    fn test_sensitive_data_processor_adapter() {
        let adapter = SensitiveDataProcessorAdapter::new();
        let config = crate::config::LoggerConfig::new();

        let mut test_config = config;
        test_config.set_privacy_agreed(true);

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Phone: 13812345678, Email: <EMAIL>, ID: 110101199001011234".to_string(),
            vec![],
            vec![],
        );

        let result = adapter.process(entry, &test_config).unwrap();

        // 验证脱敏效果
        assert!(result.message.contains("138****5678"));
        assert!(result.message.contains("t***@example.com"));
        assert!(result.message.contains("110101********1234"));
    }

    #[test]
    fn test_conditional_filter_processor() {
        let processor = ConditionalFilterProcessor;
        let mut config = crate::config::LoggerConfig::new();

        // 测试隐私协议未同意时的过滤
        config.set_privacy_agreed(false);

        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "This should be filtered".to_string(),
            vec![],
            vec![],
        );

        let result = processor.process(entry, &config);
        assert!(result.is_err()); // 应该被过滤

        // 测试Error级别不被过滤
        let error_entry = LogEntry::new(
            LogLevel::Error,
            "test".to_string(),
            "This should not be filtered".to_string(),
            vec![],
            vec![],
        );

        let result = processor.process(error_entry, &config);
        assert!(result.is_ok()); // Error级别不应该被过滤
    }

    #[test]
    fn test_length_limit_processor() {
        let processor = LengthLimitProcessor::new();
        let mut config = crate::config::LoggerConfig::new();
        config.set_max_log_length(100); // 设置一个较小的限制来测试截断

        let long_message = "a".repeat(200); // 超过限制的消息
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            long_message,
            vec!["b".repeat(300)],
            vec![false],
        );

        let result = processor.process(entry, &config).unwrap();

        // 验证消息长度被限制
        assert!(result.message.len() <= config.max_log_length);
        assert!(result.message.ends_with("..."));

        // 验证参数长度被限制
        assert!(result.args[0].len() <= 200);
        assert!(result.args[0].ends_with("..."));
    }

    #[test]
    fn test_rate_limit_processor() {
        let processor = LengthLimitProcessor::new();
        let mut config = crate::config::LoggerConfig::new();

        // 测试默认不限流（max_logs_per_second = 0）
        config.max_logs_per_second = 0;
        for _ in 0..10 {
            let entry = LogEntry::new(
                LogLevel::Info,
                "test".to_string(),
                "Test message".to_string(),
                vec![],
                vec![],
            );
            let result = processor.process(entry, &config);
            assert!(result.is_ok()); // 默认不限流，所有日志都应该通过
        }

        // 测试限流功能（设置为2条/秒）
        config.max_logs_per_second = 2;

        // 前2条应该通过
        for _ in 0..2 {
            let entry = LogEntry::new(
                LogLevel::Info,
                "test".to_string(),
                "Test message".to_string(),
                vec![],
                vec![],
            );
            let result = processor.process(entry, &config);
            assert!(result.is_ok());
        }

        // 第3条应该被限流
        let entry = LogEntry::new(
            LogLevel::Info,
            "test".to_string(),
            "Test message".to_string(),
            vec![],
            vec![],
        );
        let result = processor.process(entry, &config);
        assert!(result.is_err()); // 应该被限流
    }
}
