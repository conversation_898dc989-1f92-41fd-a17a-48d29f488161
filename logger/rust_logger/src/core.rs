use serde::{Deserialize, Serialize};
use std::sync::Arc;
use parking_lot::RwLock;
use chrono::{DateTime, Utc};
use crate::compression::LogCompressor;

use crate::{
    config::LoggerConfig,
    formatter::LogFormatter,
    storage::LogStorage,
    processor::{LogProcessor<PERSON>hain, create_default_processor_chain, initialize_processor_chain},
    error::{LoggerError, Result},
    internal_info, internal_debug, internal_warn,
};

/// 大文件通知回调函数类型
/// 参数：文件路径
pub type LargeFileCallback = Box<dyn Fn(String) + Send + Sync>;

/// 日志级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
#[repr(u8)]
pub enum LogLevel {
    Debug = 0,
    Info = 1,
    Warn = 2,
    Error = 3,
}

// 自定义序列化，支持数字和字符串两种格式
impl Serialize for LogLevel {
    fn serialize<S>(&self, serializer: S) -> std::result::Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        // 序列化为数字
        serializer.serialize_u8(*self as u8)
    }
}

// 自定义反序列化，支持数字和字符串两种格式
impl<'de> Deserialize<'de> for LogLevel {
    fn deserialize<D>(deserializer: D) -> std::result::Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        struct LogLevelVisitor;

        impl<'de> serde::de::Visitor<'de> for LogLevelVisitor {
            type Value = LogLevel;

            fn expecting(&self, formatter: &mut std::fmt::Formatter) -> std::fmt::Result {
                formatter.write_str("a number (0-3) or string (Debug, Info, Warn, Error)")
            }

            fn visit_u64<E>(self, value: u64) -> std::result::Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                match value {
                    0 => Ok(LogLevel::Debug),
                    1 => Ok(LogLevel::Info),
                    2 => Ok(LogLevel::Warn),
                    3 => Ok(LogLevel::Error),
                    _ => Err(E::custom(format!("invalid log level: {}", value))),
                }
            }

            fn visit_i64<E>(self, value: i64) -> std::result::Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                self.visit_u64(value as u64)
            }

            fn visit_str<E>(self, value: &str) -> std::result::Result<Self::Value, E>
            where
                E: serde::de::Error,
            {
                match value.to_lowercase().as_str() {
                    "debug" => Ok(LogLevel::Debug),
                    "info" => Ok(LogLevel::Info),
                    "warn" | "warning" => Ok(LogLevel::Warn),
                    "error" => Ok(LogLevel::Error),
                    _ => Err(E::custom(format!("invalid log level: {}", value))),
                }
            }
        }

        deserializer.deserialize_any(LogLevelVisitor)
    }
}

impl LogLevel {
    /// 从数字转换为日志级别
    pub fn from_u8(value: u8) -> Option<Self> {
        match value {
            0 => Some(LogLevel::Debug),
            1 => Some(LogLevel::Info),
            2 => Some(LogLevel::Warn),
            3 => Some(LogLevel::Error),
            _ => None,
        }
    }
    
    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warn => "WARN",
            LogLevel::Error => "ERROR",
        }
    }

    /// 转换为String（用于protobuf）
    pub fn to_string(&self) -> String {
        self.as_str().to_string()
    }
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub level: LogLevel,
    pub tag: String,
    #[serde(rename = "format")] // JSON中使用"format"字段名，与Android保持一致
    pub message: String,
    pub args: Vec<String>,
    #[serde(skip_deserializing)] // 不从JSON反序列化，由Rust内部生成
    pub timestamp: DateTime<Utc>,
    #[serde(skip_deserializing)] // 不从JSON反序列化，由Rust内部生成
    pub sensitive_flags: Vec<bool>,
    #[serde(skip_deserializing)] // 不从JSON反序列化，由Rust内部生成
    pub thread_id: Option<String>,
    #[serde(skip_deserializing)] // 不从JSON反序列化，由Rust内部生成
    pub process_id: Option<u32>,
}

impl LogEntry {
    /// 创建新的日志条目
    pub fn new(
        level: LogLevel,
        tag: String,
        message: String,
        args: Vec<String>,
        sensitive_flags: Vec<bool>,
    ) -> Self {
        Self {
            level,
            tag,
            message,
            args,
            timestamp: Utc::now(),
            sensitive_flags,
            thread_id: Self::get_thread_id(),
            process_id: Self::get_process_id(),
        }
    }

    /// 从JSON创建日志条目（遵循Android逻辑）
    /// 只解析外部字段，元数据由Rust内部生成
    pub fn from_json_with_metadata(json_str: &str) -> std::result::Result<Self, serde_json::Error> {
        let mut entry: LogEntry = serde_json::from_str(json_str)?;
        // Rust层补充元数据，确保时间戳准确性和一致性
        entry.timestamp = Utc::now();
        entry.sensitive_flags = vec![false; entry.args.len()]; // 初始化为非敏感，后续由处理器链处理
        entry.thread_id = Self::get_thread_id();
        entry.process_id = Self::get_process_id();
        Ok(entry)
    }

    /// 获取线程ID
    fn get_thread_id() -> Option<String> {
        std::thread::current().name().map(|s| s.to_string())
    }

    /// 获取进程ID
    fn get_process_id() -> Option<u32> {
        Some(std::process::id())
    }

    /// 检查是否包含敏感信息
    pub fn has_sensitive_data(&self) -> bool {
        self.sensitive_flags.iter().any(|&flag| flag)
    }
}

/// 统一日志器
pub struct Logger {
    config: Arc<RwLock<LoggerConfig>>,
    formatter: Arc<RwLock<LogFormatter>>,
    storage: Arc<RwLock<LogStorage>>,
    processor_chain: Arc<RwLock<LogProcessorChain>>,
    large_file_callback: Option<LargeFileCallback>,
    initialized: bool,
}

impl Logger {
    /// 创建新的日志器实例
    pub fn new() -> Self {
        let processor_chain = create_default_processor_chain()
            .unwrap_or_else(|_| LogProcessorChain::new());

        Self {
            config: Arc::new(RwLock::new(LoggerConfig::default())),
            formatter: Arc::new(RwLock::new(LogFormatter::new())),
            storage: Arc::new(RwLock::new(LogStorage::new())),
            processor_chain: Arc::new(RwLock::new(processor_chain)),
            large_file_callback: None,
            initialized: false,
        }
    }
    
    /// 初始化日志器
    pub fn initialize(&mut self, config: LoggerConfig) -> Result<()> {
        // 验证配置
        config.validate()?;

        // 更新配置
        *self.config.write() = config.clone();

        // 确保rust_storage已初始化（通常由前端初始化，这里是保险措施）
        // StorageManager是单例，多次初始化是安全的

        // 根据业务日志级别和debug模式设置内部日志级别
        let internal_level = if config.is_debug_mode {
            // Debug模式下强制启用Debug级别的内部日志
            crate::internal_log::InternalLogLevel::Debug
        } else {
            match config.log_level {
                LogLevel::Debug => crate::internal_log::InternalLogLevel::Debug,
                LogLevel::Info => crate::internal_log::InternalLogLevel::Info,
                LogLevel::Warn => crate::internal_log::InternalLogLevel::Warn,
                LogLevel::Error => crate::internal_log::InternalLogLevel::Error,
            }
        };
        crate::internal_log::set_level(internal_level);

        // 初始化各个组件
        self.formatter.write().initialize(&config)?;
        self.storage.write().initialize(&config)?;
        initialize_processor_chain(&self.processor_chain.read(), &config)?;

        // 移除上传器初始化，由前端处理上传

        self.initialized = true;
        crate::internal_info!("CORE", "Logger initialized successfully with rust_storage integration");
        Ok(())
    }
    
    /// 检查是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.initialized
    }
    
    /// 写入日志
    pub fn write_log(&self, entry: LogEntry) -> Result<()> {
        if !self.initialized {
            crate::internal_error!("CORE", "Logger not initialized");
            return Err(LoggerError::initialization("Logger not initialized"));
        }

        let config = self.config.read();

        // 检查隐私协议
        if !config.privacy_agreed {
            crate::internal_warn!("CORE", "Privacy not agreed, rejecting log");
            return Err(LoggerError::PrivacyNotAgreed);
        }

        // 检查日志级别
        if !config.should_log(entry.level) {
            return Err(LoggerError::LevelFiltered {
                current: entry.level,
                required: config.log_level,
            });
        }

        // 通过处理链处理日志条目
        let processed_entry = self.processor_chain.read().process(entry, &config)?;

        // 格式化日志
        let mut formatted_log = self.formatter.read().format_entry(&processed_entry, &config)?;

        // 检查日志长度，如果超过限制则截断
        if formatted_log.len() > config.max_log_length {
            // 截断到最大长度，并添加截断标识
            let truncate_suffix = "...[TRUNCATED]";
            let available_length = config.max_log_length.saturating_sub(truncate_suffix.len());

            // 确保在UTF-8字符边界上截断
            let mut truncate_pos = available_length;
            while truncate_pos > 0 && !formatted_log.is_char_boundary(truncate_pos) {
                truncate_pos -= 1;
            }

            formatted_log.truncate(truncate_pos);
            formatted_log.push_str(truncate_suffix);
        }

        // 控制台输出（对应Android的enableConsoleLog）
        if config.enable_console_output {
            self.output_to_console(&formatted_log, processed_entry.level);
        }

        // 文件输出（默认行为，Android没有单独的开关）
        // 使用protobuf格式存储，确保与Android版本兼容

        // 格式化最终消息（替换占位符）
        let final_message = self.format_message_with_args(&processed_entry.message, &processed_entry.args);

        self.storage.write().write_log_protobuf(
            &processed_entry.level.to_string(),
            &processed_entry.tag,
            &final_message,
            &config
        )?;

        // 检查文件大小，触发自动上传通知
        if config.is_auto_upload_enabled() {
            self.check_auto_upload(&config)?;
        }

        Ok(())
    }
    
    /// 格式化消息，替换占位符
    fn format_message_with_args(&self, message: &str, args: &[String]) -> String {
        let mut result = message.to_string();
        for arg in args {
            if let Some(pos) = result.find("{}") {
                result.replace_range(pos..pos+2, arg);
            }
        }
        result
    }

    /// 输出到控制台
    fn output_to_console(&self, message: &str, level: LogLevel) {
        let internal_level = match level {
            LogLevel::Debug => crate::internal_log::InternalLogLevel::Debug,
            LogLevel::Info => crate::internal_log::InternalLogLevel::Info,
            LogLevel::Warn => crate::internal_log::InternalLogLevel::Warn,
            LogLevel::Error => crate::internal_log::InternalLogLevel::Error,
        };
        // 直接输出消息，不添加[CONSOLE]前缀，与Android格式保持一致
        crate::internal_log::log_direct(internal_level, message);
    }

    /// 检查是否需要自动上传
    /// 当文件超过max_file_size时通知前端
    fn check_auto_upload(&self, config: &LoggerConfig) -> Result<()> {
        // 检查当前文件大小
        let current_size = self.storage.read().get_current_file_size()?;

        if current_size >= config.max_file_size {
            // 获取当前文件路径
            let current_file_path = self.storage.read().get_current_file_path()?;

            internal_info!("CORE", "File size {} exceeds limit {}, notifying frontend for upload: {}",
                      current_size, config.max_file_size, current_file_path);

            // 调用回调通知前端
            if let Some(ref callback) = self.large_file_callback {
                callback(current_file_path);
            } else {
                internal_warn!("CORE", "Large file callback not set, cannot notify frontend");
            }
        }

        Ok(())
    }
    
    /// 更新用户ID
    pub fn update_user_id(&self, user_id: String) -> Result<()> {
        let mut config = self.config.write();
        config.set_user_id(user_id);
        Ok(())
    }
    
    /// 设置隐私协议状态
    pub fn set_privacy_agreed(&self, agreed: bool) -> Result<()> {
        let mut config = self.config.write();
        config.set_privacy_agreed(agreed);
        Ok(())
    }
    
    /// 设置全量日志状态
    pub fn set_full_log(&self, enable: bool) -> Result<()> {
        let mut config = self.config.write();
        config.set_full_log(enable);
        Ok(())
    }

    /// 设置大文件通知回调
    /// 当文件超过max_file_size时会调用此回调
    pub fn set_large_file_callback<F>(&mut self, callback: F)
    where
        F: Fn(String) + Send + Sync + 'static
    {
        self.large_file_callback = Some(Box::new(callback));
    }
    
    /// 设置日志级别
    pub fn set_log_level(&self, level: LogLevel) -> Result<()> {
        let mut config = self.config.write();
        config.set_log_level(level);
        Ok(())
    }
    
    /// 获取当前日志级别
    /// 从rust_storage读取持久化状态
    pub fn get_log_level(&self) -> LogLevel {
        if self.initialized {
            let level_int = self.storage.read().get_log_level_from_storage();
            match level_int {
                0 => LogLevel::Debug,  // 0对应Debug级别
                1 => LogLevel::Info,   // 1对应Info级别
                2 => LogLevel::Warn,   // 2对应Warn级别
                3 => LogLevel::Error,  // 3对应Error级别
                _ => LogLevel::Warn,   // 默认值
            }
        } else {
            LogLevel::Warn // 未初始化时的默认值
        }
    }
    
    /// 获取当前配置的副本
    pub fn get_config(&self) -> LoggerConfig {
        self.config.read().clone()
    }

    /// 压缩日志文件供前端上传
    /// 返回压缩文件路径，由前端处理上传
    pub fn compress_logs_for_upload(&self, days: u32) -> Result<String> {
        let config = self.config.read();
        let (zip_path, _files) = LogCompressor::compress_recent_logs(&config, days)?;
        Ok(zip_path)
    }

    /// 设置版本名称
    pub fn set_version_name(&self, version_name: Option<String>) -> Result<()> {
        let mut config = self.config.write();
        config.set_version_name(version_name);

        // 重新初始化存储以使用新的版本号目录
        self.storage.write().initialize(&config)?;

        Ok(())
    }

    /// 压缩崩溃日志供前端上传
    pub fn compress_crash_logs(&self) -> Result<String> {
        use std::fs::{self, File};
        use std::io::{Write, Read};
        use std::path::Path;

        let config = self.config.read();
        let crash_dir = config.get_crash_log_directory();
        let crash_dir_path = Path::new(&crash_dir);

        // 检查崩溃日志目录是否存在
        if !crash_dir_path.exists() {
            return Err(LoggerError::storage("Crash log directory does not exist"));
        }

        // 生成ZIP文件路径
        let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
        let zip_filename = format!("crash_logs_{}.zip", timestamp);
        let zip_path = crash_dir_path.join(&zip_filename);

        // 创建ZIP文件
        let zip_file = File::create(&zip_path)?;
        let mut zip = zip::ZipWriter::new(zip_file);

        // 遍历崩溃日志目录，添加所有.log文件到ZIP
        let entries = fs::read_dir(crash_dir_path)?;
        let mut file_count = 0;

        for entry in entries {
            let entry = entry?;
            let path = entry.path();

            // 只处理.log文件
            if path.is_file() && path.extension().map_or(false, |ext| ext == "log") {
                let file_name = path.file_name()
                    .ok_or_else(|| LoggerError::storage("Invalid file name"))?
                    .to_string_lossy();

                // 添加文件到ZIP
                let options: zip::write::FileOptions<()> = zip::write::FileOptions::default();
                zip.start_file(file_name.as_ref(), options)
                    .map_err(|e| LoggerError::storage(&format!("Failed to start zip entry: {}", e)))?;

                let mut file = File::open(&path)?;
                let mut buffer = Vec::new();
                file.read_to_end(&mut buffer)?;
                zip.write_all(&buffer)
                    .map_err(|e| LoggerError::storage(&format!("Failed to write to zip: {}", e)))?;

                file_count += 1;
                internal_debug!("CORE", "Added crash log to ZIP: {}", file_name);
            }
        }

        zip.finish()
            .map_err(|e| LoggerError::storage(&format!("Failed to finish zip: {}", e)))?;

        if file_count == 0 {
            // 如果没有崩溃日志文件，删除空的ZIP文件
            let _ = fs::remove_file(&zip_path);
            return Err(LoggerError::storage("No crash log files found to compress"));
        }

        let zip_path_str = zip_path.to_string_lossy().to_string();
        internal_info!("CORE", "Compressed {} crash log files to: {}", file_count, zip_path_str);
        Ok(zip_path_str)
    }

    /// 上传成功后清理文件
    pub fn cleanup_after_upload_success(&self, zip_path: String) -> Result<()> {
        use std::fs;
        use std::path::Path;

        let zip_path_obj = Path::new(&zip_path);

        // 检查ZIP文件是否存在
        if !zip_path_obj.exists() {
            internal_warn!("CORE", "ZIP file not found for cleanup: {}", zip_path);
            return Ok(());
        }

        // 获取ZIP文件中包含的原始日志文件列表
        let original_files = self.get_original_files_from_zip(&zip_path)?;

        // 删除ZIP文件
        fs::remove_file(&zip_path_obj)
            .map_err(|e| LoggerError::storage(&format!("Failed to remove ZIP file: {}", e)))?;

        // 删除原始日志文件（上传成功后不再需要）
        let mut deleted_count = 0;
        let config = self.config.read();
        let log_dir = Path::new(&config.log_directory);

        for file_name in original_files {
            let file_path = log_dir.join(&file_name);
            if file_path.exists() {
                match fs::remove_file(&file_path) {
                    Ok(()) => {
                        deleted_count += 1;
                        internal_debug!("CORE", "Deleted original log file: {}", file_name);
                    }
                    Err(e) => {
                        internal_warn!("CORE", "Failed to delete original log file {}: {}", file_name, e);
                    }
                }
            }
        }

        internal_info!("CORE", "Successfully cleaned up ZIP file and {} original log files after upload: {}",
                   deleted_count, zip_path);

        Ok(())
    }

    /// 从ZIP文件中获取原始文件名列表
    fn get_original_files_from_zip(&self, zip_path: &str) -> Result<Vec<String>> {
        use std::fs::File;
        use zip::ZipArchive;

        let file = File::open(zip_path)
            .map_err(|e| LoggerError::storage(&format!("Failed to open ZIP file: {}", e)))?;

        let mut archive = ZipArchive::new(file)
            .map_err(|e| LoggerError::storage(&format!("Failed to read ZIP archive: {}", e)))?;

        let mut file_names = Vec::new();
        for i in 0..archive.len() {
            if let Ok(file) = archive.by_index(i) {
                // 只包含.log文件，排除目录和其他文件
                let name = file.name();
                if name.ends_with(".log") && !name.contains('/') {
                    file_names.push(name.to_string());
                }
            }
        }

        Ok(file_names)
    }

    /// 上传失败后清理文件
    pub fn cleanup_after_upload_failure(&self, zip_path: String) -> Result<()> {
        use std::fs;
        use std::path::Path;

        let zip_path_obj = Path::new(&zip_path);

        // 检查ZIP文件是否存在
        if !zip_path_obj.exists() {
            internal_warn!("CORE", "ZIP file not found for cleanup: {}", zip_path);
            return Ok(());
        }

        // 只删除ZIP文件，保留原始日志文件以便后续重试
        fs::remove_file(&zip_path_obj)
            .map_err(|e| LoggerError::storage(&format!("Failed to remove ZIP file after upload failure: {}", e)))?;

        internal_info!("CORE", "Cleaned up ZIP file after upload failure, original logs preserved: {}", zip_path);

        // 原始日志文件被保留，这样：
        // 1. 可以稍后重试上传
        // 2. 不会丢失重要的日志数据
        // 3. 可以进行本地调试分析

        Ok(())
    }

    /// 设置日志目录（仅在初始化时使用）
    pub fn set_log_directory(&self, log_directory: String) -> Result<()> {
        let mut config = self.config.write();
        config.set_log_directory(log_directory);
        Ok(())
    }

    /// 写入崩溃日志
    /// App崩溃时调用此接口保存崩溃信息
    pub fn write_crash_log(&self, crash_info: String) -> Result<()> {
        let config = self.config.read();

        // 检查隐私协议
        if !config.privacy_agreed {
            return Ok(()); // 静默忽略，不记录崩溃日志
        }

        // 创建崩溃日志条目
        let crash_entry = LogEntry::new(
            LogLevel::Error,
            "CRASH".to_string(),
            format!("Application crashed: {}", crash_info),
            vec![],
            vec![],
        );

        // 通过处理链处理日志条目
        let processed_entry = self.processor_chain.read().process(crash_entry, &config)?;

        // 格式化日志
        let formatted_log = self.formatter.read().format_entry(&processed_entry, &config)?;

        // 写入崩溃日志文件
        self.write_crash_log_to_file(&formatted_log, &config)?;

        internal_info!("CORE", "Crash log written successfully");
        Ok(())
    }

    /// 将崩溃日志写入专门的崩溃日志文件
    fn write_crash_log_to_file(&self, formatted_log: &str, config: &LoggerConfig) -> Result<()> {
        use std::fs::{create_dir_all, OpenOptions};
        use std::io::Write;

        // 确保崩溃日志目录存在
        let crash_dir = config.get_crash_log_directory();
        create_dir_all(&crash_dir)?;

        // 生成崩溃日志文件名（对齐Android格式：yyyy-MM-dd-HH:mm:ss.SSSSSSZ）
        let now = chrono::Utc::now();
        let crash_file_name = format!("{}.log", now.format("%Y-%m-%d-%H:%M:%S%.6f%z"));
        let crash_file_path = format!("{}/{}", crash_dir, crash_file_name);

        // 写入崩溃日志
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&crash_file_path)?;

        writeln!(file, "{}", formatted_log)?;
        file.flush()?;

        internal_debug!("CORE", "Crash log written to: {}", crash_file_path);
        Ok(())
    }

    /// 启用/禁用控制台日志输出
    /// 对应Android的enableConsoleLog()
    pub fn enable_console_log(&self, enable: bool) -> Result<()> {
        if !self.initialized {
            return Err(LoggerError::initialization("Logger not initialized"));
        }

        // 更新配置中的控制台输出设置
        let mut config = self.config.write();
        config.enable_console_output = enable;

        // 同时更新到rust_storage
        self.storage.read().update_console_enabled_to_storage(enable)?;

        internal_info!("CORE", "Console log output {}", if enable { "enabled" } else { "disabled" });
        Ok(())
    }

    /// 启用/禁用完整日志模式
    /// 对应Android的enableFullLogs()
    /// 当启用时，设置日志级别为DEBUG并输出所有日志到文件
    pub fn enable_full_logs(&self, enable: bool) -> Result<()> {
        if !self.initialized {
            return Err(LoggerError::initialization("Logger not initialized"));
        }

        let mut config = self.config.write();
        if enable {
            // 启用完整日志：设置为DEBUG级别，启用文件输出
            config.log_level = LogLevel::Debug;
            config.enable_full_log = true;
            config.enable_console_output = true; // 通常也启用控制台输出
            internal_info!("CORE", "Full logs mode enabled - set to DEBUG level with file output");
        } else {
            // 禁用完整日志：恢复到INFO级别
            config.log_level = LogLevel::Info;
            config.enable_full_log = false;
            internal_info!("CORE", "Full logs mode disabled - restored to INFO level");
        }

        // 同时更新到rust_storage
        self.storage.read().update_full_logs_enabled_to_storage(config.enable_full_log)?;

        Ok(())
    }

    /// 获取控制台日志输出状态
    /// 对应Android的isEnableConsole()
    /// 从rust_storage读取持久化状态
    pub fn is_console_log_enabled(&self) -> bool {
        if self.initialized {
            self.storage.read().get_console_enabled_from_storage()
        } else {
            false
        }
    }

    /// 获取完整日志模式状态
    /// 对应Android的getFullLogsStatus()
    /// 从rust_storage读取持久化状态
    pub fn is_full_logs_enabled(&self) -> bool {
        if self.initialized {
            self.storage.read().get_full_logs_enabled_from_storage()
        } else {
            false
        }
    }
}
