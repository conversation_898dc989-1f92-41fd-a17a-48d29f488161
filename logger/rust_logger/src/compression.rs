use std::fs::File;
use std::io::{Read, Write};
use std::path::{Path, PathBuf};
use zip::{ZipWriter, write::FileOptions, CompressionMethod};
use chrono::Utc;
use crate::{Result, LoggerError, LoggerConfig};

/// 压缩器
pub struct LogCompressor;

impl LogCompressor {
    /// 压缩7天内的日志文件
    pub fn compress_recent_logs(
        config: &LoggerConfig,
        days: u32,
    ) -> Result<(String, Vec<String>)> {
        let log_dir = &config.log_directory;
        let files = Self::get_recent_log_files(log_dir, days)?;
        
        if files.is_empty() {
            return Err(LoggerError::upload_error("No log files found to compress"));
        }
        
        let zip_name = format!("logs_{}_{}.zip", 
            Utc::now().format("%Y%m%d_%H%M%S"),
            uuid::Uuid::new_v4().to_string()[..8].to_string()
        );
        let zip_path = format!("{}/{}", log_dir, zip_name);
        
        Self::create_zip_file(&zip_path, &files, config.max_file_size)?;
        
        let file_names: Vec<String> = files.iter()
            .map(|p| p.to_string_lossy().to_string())
            .collect();
        
        crate::internal_info!("COMPRESSION", "Compressed {} files into {}", files.len(), zip_path);
        Ok((zip_path, file_names))
    }
    
    /// 压缩异常日志文件
    pub fn compress_exception_logs(
        exception_files: Vec<PathBuf>,
        log_dir: &str,
        max_zip_size: u64,
    ) -> Result<(String, Vec<String>)> {
        if exception_files.is_empty() {
            return Err(LoggerError::upload_error("No exception files to compress"));
        }
        
        let zip_name = format!("exception_{}_{}.zip", 
            Utc::now().format("%Y%m%d_%H%M%S"),
            uuid::Uuid::new_v4().to_string()[..8].to_string()
        );
        let zip_path = format!("{}/{}", log_dir, zip_name);
        
        Self::create_zip_file(&zip_path, &exception_files, max_zip_size)?;
        
        let file_names: Vec<String> = exception_files.iter()
            .map(|p| p.to_string_lossy().to_string())
            .collect();
        
        crate::internal_info!("COMPRESSION", "Compressed {} exception files into {}", exception_files.len(), zip_path);
        Ok((zip_path, file_names))
    }
    
    /// 压缩单个大文件
    pub fn compress_single_file(
        file_path: &str,
        log_dir: &str,
        max_zip_size: u64,
    ) -> Result<(String, Vec<String>)> {
        let file_path_buf = PathBuf::from(file_path);
        if !file_path_buf.exists() {
            return Err(LoggerError::upload_error(format!("File not found: {}", file_path)));
        }
        
        let zip_name = format!("single_{}_{}.zip", 
            Utc::now().format("%Y%m%d_%H%M%S"),
            uuid::Uuid::new_v4().to_string()[..8].to_string()
        );
        let zip_path = format!("{}/{}", log_dir, zip_name);
        
        Self::create_zip_file(&zip_path, &[file_path_buf], max_zip_size)?;
        
        crate::internal_info!("COMPRESSION", "Compressed single file {} into {}", file_path, zip_path);
        Ok((zip_path, vec![file_path.to_string()]))
    }
    
    /// 创建ZIP文件
    fn create_zip_file(
        zip_path: &str,
        files: &[PathBuf],
        max_zip_size: u64,
    ) -> Result<()> {
        let zip_file = File::create(zip_path)
            .map_err(|e| LoggerError::compression_failed(format!("Failed to create zip file: {}", e)))?;
        
        let mut zip = ZipWriter::new(zip_file);
        let options: FileOptions<()> = FileOptions::default()
            .compression_method(CompressionMethod::Deflated)
            .compression_level(Some(6)) // 使用默认压缩级别6，与Android Deflater.DEFAULT_COMPRESSION一致
            .unix_permissions(0o644);
        
        let mut total_size = 0u64;
        
        for file_path in files {
            if !file_path.exists() {
                crate::internal_warn!("COMPRESSION", "Skipping non-existent file: {:?}", file_path);
                continue;
            }
            
            let file_size = file_path.metadata()
                .map_err(|e| LoggerError::compression_failed(format!("Failed to get file metadata: {}", e)))?
                .len();
            
            // 检查压缩后的大小限制
            if total_size + file_size > max_zip_size {
                crate::internal_warn!("COMPRESSION", "Zip file would exceed size limit, stopping at {} files", files.len());
                break;
            }
            
            let file_name = file_path.file_name()
                .ok_or_else(|| LoggerError::compression_failed("Invalid file name"))?
                .to_string_lossy();
            
            zip.start_file(file_name.as_ref(), options)
                .map_err(|e| LoggerError::compression_failed(format!("Failed to start zip entry: {}", e)))?;
            
            let mut file = File::open(file_path)
                .map_err(|e| LoggerError::compression_failed(format!("Failed to open file: {}", e)))?;
            
            let mut buffer = vec![0; 8192]; // 8KB buffer
            loop {
                let bytes_read = file.read(&mut buffer)
                    .map_err(|e| LoggerError::compression_failed(format!("Failed to read file: {}", e)))?;
                
                if bytes_read == 0 {
                    break;
                }
                
                zip.write_all(&buffer[..bytes_read])
                    .map_err(|e| LoggerError::compression_failed(format!("Failed to write to zip: {}", e)))?;
            }
            
            total_size += file_size;
        }
        
        zip.finish()
            .map_err(|e| LoggerError::compression_failed(format!("Failed to finish zip: {}", e)))?;
        
        // 验证生成的ZIP文件大小
        let zip_size = std::fs::metadata(zip_path)
            .map_err(|e| LoggerError::compression_failed(format!("Failed to get zip metadata: {}", e)))?
            .len();
        
        if zip_size > max_zip_size {
            std::fs::remove_file(zip_path).ok(); // 清理过大的文件
            return Err(LoggerError::FileSizeExceeded);
        }
        
        log::debug!("Created zip file: {} (size: {} bytes)", zip_path, zip_size);
        Ok(())
    }
    
    /// 获取最近几天的日志文件
    fn get_recent_log_files(log_dir: &str, days: u32) -> Result<Vec<PathBuf>> {
        let log_path = Path::new(log_dir);
        if !log_path.exists() {
            return Ok(Vec::new());
        }
        
        let cutoff_time = std::time::SystemTime::now() - std::time::Duration::from_secs(days as u64 * 24 * 60 * 60);
        let mut files = Vec::new();
        
        for entry in std::fs::read_dir(log_path)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() {
                if let Some(file_name) = path.file_name() {
                    if let Some(name_str) = file_name.to_str() {
                        // 只包含日志文件，排除ZIP文件和状态文件
                        if name_str.ends_with(".log") && 
                           !name_str.starts_with(".") &&
                           !name_str.contains("exception") {
                            
                            // 检查文件修改时间
                            if let Ok(metadata) = path.metadata() {
                                if let Ok(modified) = metadata.modified() {
                                    if modified >= cutoff_time {
                                        files.push(path);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // 按修改时间排序（最新的在前）
        files.sort_by_key(|path| {
            path.metadata()
                .and_then(|m| m.modified())
                .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
        });
        files.reverse();
        
        crate::internal_debug!("COMPRESSION", "Found {} log files in the last {} days", files.len(), days);
        Ok(files)
    }
    
    /// 验证ZIP文件完整性
    pub fn verify_zip_file(zip_path: &str) -> Result<bool> {
        use zip::ZipArchive;
        
        let file = File::open(zip_path)
            .map_err(|e| LoggerError::compression_failed(format!("Failed to open zip for verification: {}", e)))?;
        
        match ZipArchive::new(file) {
            Ok(mut archive) => {
                // 尝试读取所有条目
                for i in 0..archive.len() {
                    if archive.by_index(i).is_err() {
                        return Ok(false);
                    }
                }
                Ok(true)
            }
            Err(_) => Ok(false),
        }
    }
    
    /// 获取ZIP文件信息
    pub fn get_zip_info(zip_path: &str) -> Result<ZipInfo> {
        use zip::ZipArchive;
        
        let file = File::open(zip_path)
            .map_err(|e| LoggerError::compression_failed(format!("Failed to open zip: {}", e)))?;
        
        let mut archive = ZipArchive::new(file)
            .map_err(|e| LoggerError::compression_failed(format!("Failed to read zip: {}", e)))?;
        
        let mut total_uncompressed_size = 0u64;
        let mut file_count = 0usize;
        
        for i in 0..archive.len() {
            if let Ok(file) = archive.by_index(i) {
                total_uncompressed_size += file.size();
                file_count += 1;
            }
        }
        
        let compressed_size = std::fs::metadata(zip_path)?.len();
        
        Ok(ZipInfo {
            file_count,
            compressed_size,
            uncompressed_size: total_uncompressed_size,
        })
    }
}

/// ZIP文件信息
#[derive(Debug)]
pub struct ZipInfo {
    pub file_count: usize,
    pub compressed_size: u64,
    pub uncompressed_size: u64,
}
