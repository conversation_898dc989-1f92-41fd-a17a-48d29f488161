#[cfg(test)]
mod tests {
    use crate::{
        config::LoggerConfig,
        core::{Logger, LogLevel, LogEntry},
    };

    #[test]
    fn test_config_creation() {
        let config = LoggerConfig::new();
        assert_eq!(config.log_level, LogLevel::Warn);
        assert!(!config.enable_console_output); // 默认关闭控制台输出（对应Android默认值）
        assert!(!config.privacy_agreed);
    }

    #[test]
    fn test_large_file_callback() {
        use std::sync::{Arc, Mutex};

        let mut logger = Logger::new();
        let callback_called = Arc::new(Mutex::new(false));
        let callback_called_clone = callback_called.clone();

        // 设置回调
        logger.set_large_file_callback(move |file_path: String| {
            println!("Large file callback called with path: {}", file_path);
            *callback_called_clone.lock().unwrap() = true;
        });

        // 这里只是测试回调设置，实际的文件大小检查需要真实的文件系统
        // 在实际使用中，当文件超过max_file_size时会自动调用回调
        // 注意：large_file_callback是私有字段，我们通过设置回调来验证功能
    }

    #[test]
    fn test_config_validation() {
        let mut config = LoggerConfig::new();
        config.set_log_directory("/tmp/test_logs"); // 设置必需的日志目录
        assert!(config.validate().is_ok());
        
        // 测试无效配置
        config.max_log_length = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_log_level_conversion() {
        assert_eq!(LogLevel::from_u8(0), Some(LogLevel::Debug));
        assert_eq!(LogLevel::from_u8(1), Some(LogLevel::Info));
        assert_eq!(LogLevel::from_u8(2), Some(LogLevel::Warn));
        assert_eq!(LogLevel::from_u8(3), Some(LogLevel::Error));
        assert_eq!(LogLevel::from_u8(4), None);
    }

    #[test]
    fn test_log_entry_creation() {
        let entry = LogEntry::new(
            LogLevel::Info,
            "TestTag".to_string(),
            "Test message: {}".to_string(),
            vec!["arg1".to_string()],
            vec![false],
        );
        
        assert_eq!(entry.level, LogLevel::Info);
        assert_eq!(entry.tag, "TestTag");
        assert_eq!(entry.message, "Test message: {}");
        assert_eq!(entry.args.len(), 1);
        assert_eq!(entry.sensitive_flags.len(), 1);
        assert!(!entry.has_sensitive_data());
    }

    #[test]
    fn test_logger_creation() {
        let logger = Logger::new();
        assert!(!logger.is_initialized());
    }

    #[test]
    fn test_log_formatting() {
        use crate::formatter::LogFormatter;

        let formatter = LogFormatter::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("test_user_123");
        config.set_test_mode(true);

        let entry = LogEntry::new(
            LogLevel::Info,
            "TestModule".to_string(),
            "Test message with arg: {}".to_string(),
            vec!["test_arg".to_string()],
            vec![false],
        );

        let formatted = formatter.format_entry(&entry, &config).unwrap();

        // 验证格式：[时间戳][sessionID][模块信息][LogLevel][TestMode][userId][日志信息]
        assert!(formatted.contains("[TestModule]")); // 模块信息
        assert!(formatted.contains("[INFO]")); // LogLevel
        assert!(formatted.contains("[TEST]")); // TestMode
        assert!(formatted.contains("[test_user_123]")); // userId
        assert!(formatted.contains("[Test message with arg: test_arg]")); // 日志信息

        // 验证包含时间戳和sessionID
        assert!(formatted.starts_with("[")); // 应该以时间戳开始

        println!("Formatted log: {}", formatted);
    }

    #[test]
    fn test_log_formatting_prod_mode() {
        use crate::formatter::LogFormatter;

        let formatter = LogFormatter::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("prod_user_456");
        config.is_debug_mode = false; // 正常模式

        let entry = LogEntry::new(
            LogLevel::Error,
            "ErrorModule".to_string(),
            "Error occurred".to_string(),
            vec![],
            vec![],
        );

        let formatted = formatter.format_entry(&entry, &config).unwrap();

        // 验证生产模式
        assert!(formatted.contains("[正常模式]")); // TestMode应该是正常模式
        assert!(formatted.contains("[ERROR]")); // LogLevel
        assert!(formatted.contains("[prod_user_456]")); // userId
        assert!(formatted.contains("[ErrorModule]")); // 模块信息

        println!("Formatted log (PROD): {}", formatted);
    }

    #[test]
    fn test_max_log_length_setting() {
        let mut config = LoggerConfig::new();

        // 测试设置有效值
        config.set_max_log_length(3000);
        assert_eq!(config.max_log_length, 3000);

        // 测试超过最大值的情况
        config.set_max_log_length(5000);
        assert_eq!(config.max_log_length, 4000); // 应该被限制为4000

        // 测试小于最小值的情况
        config.set_max_log_length(0);
        assert_eq!(config.max_log_length, 1); // 应该被限制为1
    }

    #[test]
    fn test_log_truncation() {
        use crate::formatter::LogFormatter;

        let formatter = LogFormatter::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("test_user");
        config.set_test_mode(true);
        config.set_max_log_length(150); // 设置一个较小的限制来测试截断

        // 创建一个很长的日志消息
        let long_message = "This is a very long log message that should be truncated because it exceeds the maximum length limit set in the configuration and continues with more text".to_string();

        let entry = LogEntry::new(
            LogLevel::Info,
            "TestModule".to_string(),
            long_message,
            vec![],
            vec![],
        );

        let formatted = formatter.format_entry(&entry, &config).unwrap();

        println!("Original formatted log (length: {}): {}", formatted.len(), formatted);
        println!("Max length setting: {}", config.max_log_length);

        // 注意：这里测试的是格式化器本身，不是Logger的截断逻辑
        // 格式化器本身不做截断，截断是在Logger.write_log中进行的

        // 如果我们想测试截断，需要通过Logger来测试
        // 这里先验证格式化器正常工作
        assert!(formatted.len() > config.max_log_length); // 格式化器应该返回完整日志
    }

    #[test]
    fn test_logger_truncation() {
        let mut logger = Logger::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("test_user");
        config.set_test_mode(true);
        config.set_privacy_agreed(true); // 必须同意隐私协议
        config.set_log_level(LogLevel::Debug); // 设置为Debug级别以允许Info日志
        config.set_max_log_length(150); // 设置较小的限制
        config.set_log_directory("/tmp/test_logs"); // 设置日志目录以通过验证

        // 初始化logger
        logger.initialize(config.clone()).unwrap();

        // 创建一个很长的日志消息
        let long_message = "This is a very long log message that should be truncated because it exceeds the maximum length limit set in the configuration and continues with more text to make it even longer".to_string();

        let entry = LogEntry::new(
            LogLevel::Info,
            "TestModule".to_string(),
            long_message,
            vec![],
            vec![],
        );

        // 这应该成功，因为Logger会截断过长的日志
        let result = logger.write_log(entry);
        match result {
            Ok(_) => println!("Logger truncation test passed"),
            Err(e) => {
                println!("Logger truncation test failed with error: {:?}", e);
                panic!("Logger should handle long logs by truncating them, but got error: {:?}", e);
            }
        }
    }

    #[test]
    fn test_truncation_with_different_lengths() {
        use crate::formatter::LogFormatter;

        let formatter = LogFormatter::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("test_user");
        config.set_test_mode(true);

        // 测试不同的最大长度设置
        let test_cases = vec![
            (50, "Short"),
            (100, "Medium length message"),
            (200, "This is a longer message that should be truncated when the limit is set to a smaller value"),
        ];

        for (max_length, message) in test_cases {
            config.set_max_log_length(max_length);

            let entry = LogEntry::new(
                LogLevel::Info,
                "TestModule".to_string(),
                message.to_string(),
                vec![],
                vec![],
            );

            let formatted = formatter.format_entry(&entry, &config).unwrap();
            println!("Max length: {}, Message: '{}', Formatted length: {}",
                    max_length, message, formatted.len());

            // 验证格式化器本身不做截断
            // 截断逻辑在Logger中
        }
    }

    #[test]
    fn test_file_size_configuration() {
        let config = LoggerConfig::new();

        // 验证默认文件大小为20MB
        assert_eq!(config.max_file_size, 20 * 1024 * 1024);

        // 验证默认目录大小为600MB（与Android DEFAULT_TOTAL_LOG_FILE_SIZE一致）
        assert_eq!(config.max_directory_size, 600 * 1024 * 1024);
    }

    #[test]
    fn test_file_size_validation() {
        let mut config = LoggerConfig::new();
        config.set_log_directory("/tmp/test_logs"); // 设置日志目录以通过验证

        // 测试有效的文件大小
        config.max_file_size = 20 * 1024 * 1024; // 20MB
        assert!(config.validate().is_ok());

        // 测试超过最大值的文件大小
        config.max_file_size = 60 * 1024 * 1024; // 60MB，超过50MB限制
        assert!(config.validate().is_err());

        // 测试小于最小值的情况
        config.max_file_size = 3 * 1024 * 1024; // 3MB，小于5MB限制
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_config_validation_max_length() {
        let mut config = LoggerConfig::new();
        config.set_log_directory("/tmp/test_logs"); // 设置日志目录以通过验证

        // 测试有效配置
        config.max_log_length = 2000;
        assert!(config.validate().is_ok());

        // 测试超过最大值
        config.max_log_length = 5000;
        assert!(config.validate().is_err());

        // 测试为0的情况
        config.max_log_length = 0;
        assert!(config.validate().is_err());
    }



    #[test]
    fn test_file_size_setting() {
        let mut config = LoggerConfig::new();

        // 测试设置有效值
        config.set_max_file_size(30 * 1024 * 1024); // 30MB
        assert_eq!(config.max_file_size, 30 * 1024 * 1024);

        // 测试超过最大值的情况
        config.set_max_file_size(100 * 1024 * 1024); // 100MB
        assert_eq!(config.max_file_size, 50 * 1024 * 1024); // 应该被限制为50MB

        // 测试小于最小值的情况
        config.set_max_file_size(1 * 1024 * 1024); // 1MB
        assert_eq!(config.max_file_size, 5 * 1024 * 1024); // 应该被限制为5MB
    }

    #[test]
    fn test_directory_size_validation() {
        let mut config = LoggerConfig::new();
        config.set_log_directory("/tmp/test_logs"); // 设置日志目录以通过验证

        // 测试有效的目录大小
        config.max_directory_size = 200 * 1024 * 1024; // 200MB
        assert!(config.validate().is_ok());

        // 测试超过最大值的目录大小
        config.max_directory_size = 700 * 1024 * 1024; // 700MB，超过600MB限制
        assert!(config.validate().is_err());

        // 测试小于最小值的情况
        config.max_directory_size = 50 * 1024 * 1024; // 50MB，小于100MB限制
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_directory_size_setting() {
        let mut config = LoggerConfig::new();

        // 测试设置有效值
        config.set_max_directory_size(300 * 1024 * 1024); // 300MB
        assert_eq!(config.max_directory_size, 300 * 1024 * 1024);

        // 测试超过最大值的情况
        config.set_max_directory_size(1000 * 1024 * 1024); // 1000MB
        assert_eq!(config.max_directory_size, 600 * 1024 * 1024); // 应该被限制为600MB

        // 测试小于最小值的情况
        config.set_max_directory_size(50 * 1024 * 1024); // 50MB
        assert_eq!(config.max_directory_size, 100 * 1024 * 1024); // 应该被限制为100MB
    }

    #[test]
    fn test_auto_upload_configuration() {
        let mut config = LoggerConfig::new();

        // 验证默认自动上传配置
        assert!(!config.is_auto_upload_enabled()); // 默认关闭

        // 启用全量日志时自动启用上传
        config.set_full_log(true);
        assert!(config.is_auto_upload_enabled());
    }

    #[test]
    fn test_user_id_default_value() {
        let config = LoggerConfig::new();

        // 验证userId默认值为"0"
        assert_eq!(config.user_id, "0".to_string());
    }

    #[test]
    fn test_log_directory_configuration() {
        let mut config = LoggerConfig::new();

        // 测试设置日志目录
        config.set_log_directory("/custom/log/path");
        assert_eq!(config.log_directory, "/custom/log/path");

        // 测试获取崩溃日志目录（遵循Android逻辑: path + "/" + "uhomelog" + "/exception"）
        let crash_dir = config.get_crash_log_directory();
        assert_eq!(crash_dir, "/custom/log/path/uhomelog/exception");
    }

    #[test]
    fn test_crash_log_directory() {
        let config = LoggerConfig::new();

        // 测试默认崩溃日志目录
        let crash_dir = config.get_crash_log_directory();
        assert!(crash_dir.ends_with("/exception"));
    }

    #[test]
    fn test_log_metadata_serialization() {
        use crate::protobuf::LogMetaData;
        use prost::Message;

        // 创建测试数据
        let metadata = LogMetaData {
            time: "2024-01-01T12:00:00Z".to_string(),
            session_id: "test_session".to_string(),
            tag: "TestTag".to_string(),
            level: "INFO".to_string(),
            test_mode: "TEST".to_string(),
            user_id: "test_user".to_string(),
            log_message: "Test message".to_string(),
        };

        // 序列化
        let mut buf = Vec::new();
        metadata.encode(&mut buf).expect("Failed to encode protobuf");

        // 反序列化
        let decoded = LogMetaData::decode(&buf[..]).expect("Failed to decode protobuf");

        // 验证数据
        assert_eq!(decoded.time, "2024-01-01T12:00:00Z");
        assert_eq!(decoded.session_id, "test_session");
        assert_eq!(decoded.tag, "TestTag");
        assert_eq!(decoded.level, "INFO");
        assert_eq!(decoded.test_mode, "TEST");
        assert_eq!(decoded.user_id, "test_user");
        assert_eq!(decoded.log_message, "Test message");

        println!("Protobuf serialization test passed!");
    }

    #[test]
    fn test_formatter_protobuf_integration() {
        use crate::formatter::LogFormatter;
        use crate::protobuf::LogMetaData;
        use prost::Message;

        let formatter = LogFormatter::new();
        let mut config = LoggerConfig::new();
        config.set_user_id("test_user_123");
        config.set_test_mode(true);

        let entry = LogEntry::new(
            LogLevel::Info,
            "TestModule".to_string(),
            "Test message with arg: {}".to_string(),
            vec!["test_arg".to_string()],
            vec![false],
        );

        // 测试protobuf序列化
        let protobuf_data = formatter.to_protobuf(&entry, &config).unwrap();

        // 验证可以反序列化
        let decoded = LogMetaData::decode(&protobuf_data[..]).unwrap();

        assert_eq!(decoded.tag, "TestModule");
        assert_eq!(decoded.level, "INFO");
        assert_eq!(decoded.test_mode, "TEST");
        assert_eq!(decoded.user_id, "test_user_123");
        assert_eq!(decoded.log_message, "Test message with arg: test_arg");
        assert!(!decoded.session_id.is_empty());
        assert!(!decoded.time.is_empty());

        println!("Formatter protobuf integration test passed!");
    }
}
