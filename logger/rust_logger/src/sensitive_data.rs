use regex::Regex;
use std::collections::HashMap;

use crate::{
    config::LoggerConfig,
    core::LogEntry,
    error::{LoggerError, Result},
};

/// 敏感信息处理器
pub struct SensitiveDataProcessor {
    masking_patterns: Vec<Regex>,
    custom_patterns: HashMap<String, Regex>,
}

impl SensitiveDataProcessor {
    /// 创建新的敏感信息处理器
    pub fn new() -> Self {
        Self {
            masking_patterns: Vec::new(),
            custom_patterns: HashMap::new(),
        }
    }
    
    /// 初始化敏感信息处理器
    pub fn initialize(&mut self, _config: &LoggerConfig) -> Result<()> {
        // 初始化默认的脱敏模式
        self.init_default_patterns()?;
        
        crate::internal_debug!("SENSITIVE", "SensitiveDataProcessor initialized with {} patterns", self.masking_patterns.len());
        Ok(())
    }
    
    /// 初始化默认脱敏模式
    fn init_default_patterns(&mut self) -> Result<()> {
        let patterns = vec![
            // 手机号脱敏：保留前3位和后4位
            (r"\b1[3-9]\d{9}\b", "1*******"),
            
            // 身份证号脱敏：保留前6位和后4位
            (r"\b\d{6}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]\b", "******"),
            
            // 邮箱脱敏：保留用户名首字母和域名
            (r"\b([a-zA-Z])[a-zA-Z0-9._-]*@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b", "$1***@$2"),
            
            // 银行卡号脱敏：保留前4位和后4位
            (r"\b\d{4}(\d{8,12})\d{4}\b", "****"),
            
            // IP地址脱敏：保留前两段
            (r"\b(\d{1,3}\.\d{1,3})\.\d{1,3}\.\d{1,3}\b", "$1.*.*"),
            
            // 密码相关关键词后的内容脱敏
            (r"(?i)(password|pwd|pass|secret|token|key)\s*[:=]\s*\S+", "$1:***"),
            
            // URL中的敏感参数脱敏
            (r"(?i)([?&])(password|pwd|token|key|secret)=([^&\s]+)", "$1$2=***"),
        ];
        
        for (pattern, _replacement) in patterns {
            match Regex::new(pattern) {
                Ok(regex) => self.masking_patterns.push(regex),
                Err(e) => {
                    crate::internal_warn!("SENSITIVE", "Failed to compile regex pattern '{}': {}", pattern, e);
                }
            }
        }
        
        Ok(())
    }
    
    /// 处理日志条目
    pub fn process_entry(&self, mut entry: LogEntry, config: &LoggerConfig) -> Result<LogEntry> {
        // 如果隐私协议未同意，不处理敏感信息（应该在上层拦截）
        if !config.privacy_agreed {
            return Err(LoggerError::PrivacyNotAgreed);
        }
        
        // 处理参数中的敏感信息
        for (i, arg) in entry.args.iter_mut().enumerate() {
            if i < entry.sensitive_flags.len() && entry.sensitive_flags[i] {
                *arg = self.mask_sensitive_data(arg);
            } else {
                // 即使标记为非敏感，也要检查是否包含敏感模式
                *arg = self.auto_mask_sensitive_patterns(arg);
            }
        }
        
        // 处理消息内容
        entry.message = self.auto_mask_sensitive_patterns(&entry.message);
        
        Ok(entry)
    }
    
    /// 脱敏敏感数据
    fn mask_sensitive_data(&self, data: &str) -> String {
        if data.is_empty() {
            return data.to_string();
        }
        
        // 对于明确标记为敏感的数据，进行更严格的脱敏
        let len = data.chars().count();
        match len {
            0 => data.to_string(),
            1..=2 => "*".repeat(len),
            3..=6 => {
                let chars: Vec<char> = data.chars().collect();
                format!("{}***", chars[0])
            }
            _ => {
                let chars: Vec<char> = data.chars().collect();
                format!("{}***{}", chars[0], chars[len - 1])
            }
        }
    }
    
    /// 自动检测并脱敏敏感模式
    fn auto_mask_sensitive_patterns(&self, text: &str) -> String {
        let mut result = text.to_string();
        
        // 手机号脱敏
        if let Some(regex) = self.masking_patterns.get(0) {
            result = regex.replace_all(&result, |caps: &regex::Captures| {
                let full_match = caps.get(0).unwrap().as_str();
                if full_match.len() == 11 {
                    format!("{}****{}", &full_match[0..3], &full_match[7..11])
                } else {
                    "***".to_string()
                }
            }).to_string();
        }
        
        // 身份证号脱敏
        if let Some(regex) = self.masking_patterns.get(1) {
            result = regex.replace_all(&result, |caps: &regex::Captures| {
                let full_match = caps.get(0).unwrap().as_str();
                if full_match.len() == 18 {
                    format!("{}********{}", &full_match[0..6], &full_match[14..18])
                } else {
                    "***".to_string()
                }
            }).to_string();
        }
        
        // 邮箱脱敏
        if let Some(regex) = self.masking_patterns.get(2) {
            result = regex.replace_all(&result, "$1***@$2").to_string();
        }
        
        // 银行卡号脱敏
        if let Some(regex) = self.masking_patterns.get(3) {
            result = regex.replace_all(&result, |caps: &regex::Captures| {
                let full_match = caps.get(0).unwrap().as_str();
                if full_match.len() >= 8 {
                    let len = full_match.len();
                    format!("{}****{}", &full_match[0..4], &full_match[len-4..len])
                } else {
                    "****".to_string()
                }
            }).to_string();
        }
        
        // IP地址脱敏
        if let Some(regex) = self.masking_patterns.get(4) {
            result = regex.replace_all(&result, "$1.*.*").to_string();
        }
        
        // 密码相关脱敏
        if let Some(regex) = self.masking_patterns.get(5) {
            result = regex.replace_all(&result, "$1:***").to_string();
        }
        
        // URL参数脱敏
        if let Some(regex) = self.masking_patterns.get(6) {
            result = regex.replace_all(&result, "$1$2=***").to_string();
        }
        
        result
    }
    
    /// 添加自定义脱敏模式
    pub fn add_custom_pattern(&mut self, name: String, pattern: &str) -> Result<()> {
        let regex = Regex::new(pattern)?;
        self.custom_patterns.insert(name, regex);
        Ok(())
    }
    
    /// 移除自定义脱敏模式
    pub fn remove_custom_pattern(&mut self, name: &str) {
        self.custom_patterns.remove(name);
    }
    
    /// 检查文本是否包含敏感信息
    pub fn contains_sensitive_data(&self, text: &str) -> bool {
        for pattern in &self.masking_patterns {
            if pattern.is_match(text) {
                return true;
            }
        }
        
        for pattern in self.custom_patterns.values() {
            if pattern.is_match(text) {
                return true;
            }
        }
        
        false
    }
    
    /// 获取敏感信息统计
    pub fn get_sensitive_stats(&self, text: &str) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        
        let pattern_names = vec![
            "phone", "id_card", "email", "bank_card", 
            "ip_address", "password", "url_param"
        ];
        
        for (i, pattern) in self.masking_patterns.iter().enumerate() {
            let count = pattern.find_iter(text).count();
            if count > 0 {
                let name = pattern_names.get(i).unwrap_or(&"unknown").to_string();
                stats.insert(name, count);
            }
        }
        
        stats
    }
}
