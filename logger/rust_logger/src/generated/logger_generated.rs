// automatically generated by the FlatBuffers compiler, do not modify


// @generated

use core::mem;
use core::cmp::Ordering;

extern crate flatbuffers;
use self::flatbuffers::{EndianScalar, Follow};

#[allow(unused_imports, dead_code)]
pub mod com {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod haier {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uhome {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod uplus {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod rust {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod logger {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};
#[allow(unused_imports, dead_code)]
pub mod fbs {

  use core::mem;
  use core::cmp::Ordering;

  extern crate flatbuffers;
  use self::flatbuffers::{EndianScalar, Follow};

#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MIN_LOG_LEVEL: i8 = 0;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MAX_LOG_LEVEL: i8 = 3;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
#[allow(non_camel_case_types)]
pub const ENUM_VALUES_LOG_LEVEL: [LogLevel; 4] = [
  LogLevel::DEBUG,
  LogLevel::INFO,
  LogLevel::WARN,
  LogLevel::ERROR,
];

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
#[repr(transparent)]
pub struct LogLevel(pub i8);
#[allow(non_upper_case_globals)]
impl LogLevel {
  pub const DEBUG: Self = Self(0);
  pub const INFO: Self = Self(1);
  pub const WARN: Self = Self(2);
  pub const ERROR: Self = Self(3);

  pub const ENUM_MIN: i8 = 0;
  pub const ENUM_MAX: i8 = 3;
  pub const ENUM_VALUES: &'static [Self] = &[
    Self::DEBUG,
    Self::INFO,
    Self::WARN,
    Self::ERROR,
  ];
  /// Returns the variant's name or "" if unknown.
  pub fn variant_name(self) -> Option<&'static str> {
    match self {
      Self::DEBUG => Some("DEBUG"),
      Self::INFO => Some("INFO"),
      Self::WARN => Some("WARN"),
      Self::ERROR => Some("ERROR"),
      _ => None,
    }
  }
}
impl core::fmt::Debug for LogLevel {
  fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
    if let Some(name) = self.variant_name() {
      f.write_str(name)
    } else {
      f.write_fmt(format_args!("<UNKNOWN {:?}>", self.0))
    }
  }
}
impl<'a> flatbuffers::Follow<'a> for LogLevel {
  type Inner = Self;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    let b = flatbuffers::read_scalar_at::<i8>(buf, loc);
    Self(b)
  }
}

impl flatbuffers::Push for LogLevel {
    type Output = LogLevel;
    #[inline]
    unsafe fn push(&self, dst: &mut [u8], _written_len: usize) {
        flatbuffers::emplace_scalar::<i8>(dst, self.0);
    }
}

impl flatbuffers::EndianScalar for LogLevel {
  type Scalar = i8;
  #[inline]
  fn to_little_endian(self) -> i8 {
    self.0.to_le()
  }
  #[inline]
  #[allow(clippy::wrong_self_convention)]
  fn from_little_endian(v: i8) -> Self {
    let b = i8::from_le(v);
    Self(b)
  }
}

impl<'a> flatbuffers::Verifiable for LogLevel {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    i8::run_verifier(v, pos)
  }
}

impl flatbuffers::SimpleToVerifyInSlice for LogLevel {}
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MIN_LOGGER_CONTAINER: u8 = 0;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
pub const ENUM_MAX_LOGGER_CONTAINER: u8 = 9;
#[deprecated(since = "2.0.0", note = "Use associated constants instead. This will no longer be generated in 2021.")]
#[allow(non_camel_case_types)]
pub const ENUM_VALUES_LOGGER_CONTAINER: [LoggerContainer; 10] = [
  LoggerContainer::NONE,
  LoggerContainer::BoolWrapper,
  LoggerContainer::StrWrapper,
  LoggerContainer::Int32Wrapper,
  LoggerContainer::NoneWrapper,
  LoggerContainer::LogEntry,
  LoggerContainer::LoggerConfig,
  LoggerContainer::UploadProgress,
  LoggerContainer::LogStats,
  LoggerContainer::LargeFileEvent,
];

#[derive(Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
#[repr(transparent)]
pub struct LoggerContainer(pub u8);
#[allow(non_upper_case_globals)]
impl LoggerContainer {
  pub const NONE: Self = Self(0);
  pub const BoolWrapper: Self = Self(1);
  pub const StrWrapper: Self = Self(2);
  pub const Int32Wrapper: Self = Self(3);
  pub const NoneWrapper: Self = Self(4);
  pub const LogEntry: Self = Self(5);
  pub const LoggerConfig: Self = Self(6);
  pub const UploadProgress: Self = Self(7);
  pub const LogStats: Self = Self(8);
  pub const LargeFileEvent: Self = Self(9);

  pub const ENUM_MIN: u8 = 0;
  pub const ENUM_MAX: u8 = 9;
  pub const ENUM_VALUES: &'static [Self] = &[
    Self::NONE,
    Self::BoolWrapper,
    Self::StrWrapper,
    Self::Int32Wrapper,
    Self::NoneWrapper,
    Self::LogEntry,
    Self::LoggerConfig,
    Self::UploadProgress,
    Self::LogStats,
    Self::LargeFileEvent,
  ];
  /// Returns the variant's name or "" if unknown.
  pub fn variant_name(self) -> Option<&'static str> {
    match self {
      Self::NONE => Some("NONE"),
      Self::BoolWrapper => Some("BoolWrapper"),
      Self::StrWrapper => Some("StrWrapper"),
      Self::Int32Wrapper => Some("Int32Wrapper"),
      Self::NoneWrapper => Some("NoneWrapper"),
      Self::LogEntry => Some("LogEntry"),
      Self::LoggerConfig => Some("LoggerConfig"),
      Self::UploadProgress => Some("UploadProgress"),
      Self::LogStats => Some("LogStats"),
      Self::LargeFileEvent => Some("LargeFileEvent"),
      _ => None,
    }
  }
}
impl core::fmt::Debug for LoggerContainer {
  fn fmt(&self, f: &mut core::fmt::Formatter) -> core::fmt::Result {
    if let Some(name) = self.variant_name() {
      f.write_str(name)
    } else {
      f.write_fmt(format_args!("<UNKNOWN {:?}>", self.0))
    }
  }
}
impl<'a> flatbuffers::Follow<'a> for LoggerContainer {
  type Inner = Self;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    let b = flatbuffers::read_scalar_at::<u8>(buf, loc);
    Self(b)
  }
}

impl flatbuffers::Push for LoggerContainer {
    type Output = LoggerContainer;
    #[inline]
    unsafe fn push(&self, dst: &mut [u8], _written_len: usize) {
        flatbuffers::emplace_scalar::<u8>(dst, self.0);
    }
}

impl flatbuffers::EndianScalar for LoggerContainer {
  type Scalar = u8;
  #[inline]
  fn to_little_endian(self) -> u8 {
    self.0.to_le()
  }
  #[inline]
  #[allow(clippy::wrong_self_convention)]
  fn from_little_endian(v: u8) -> Self {
    let b = u8::from_le(v);
    Self(b)
  }
}

impl<'a> flatbuffers::Verifiable for LoggerContainer {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    u8::run_verifier(v, pos)
  }
}

impl flatbuffers::SimpleToVerifyInSlice for LoggerContainer {}
pub struct LoggerContainerUnionTableOffset {}

pub enum BoolWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct BoolWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for BoolWrapper<'a> {
  type Inner = BoolWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> BoolWrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    BoolWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args BoolWrapperArgs
  ) -> flatbuffers::WIPOffset<BoolWrapper<'bldr>> {
    let mut builder = BoolWrapperBuilder::new(_fbb);
    builder.add_value(args.value);
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(BoolWrapper::VT_VALUE, Some(false)).unwrap()}
  }
}

impl flatbuffers::Verifiable for BoolWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<bool>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct BoolWrapperArgs {
    pub value: bool,
}
impl<'a> Default for BoolWrapperArgs {
  #[inline]
  fn default() -> Self {
    BoolWrapperArgs {
      value: false,
    }
  }
}

pub struct BoolWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> BoolWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: bool) {
    self.fbb_.push_slot::<bool>(BoolWrapper::VT_VALUE, value, false);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> BoolWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    BoolWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<BoolWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for BoolWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("BoolWrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum StrWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct StrWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for StrWrapper<'a> {
  type Inner = StrWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> StrWrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    StrWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args StrWrapperArgs<'args>
  ) -> flatbuffers::WIPOffset<StrWrapper<'bldr>> {
    let mut builder = StrWrapperBuilder::new(_fbb);
    if let Some(x) = args.value { builder.add_value(x); }
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(StrWrapper::VT_VALUE, None)}
  }
}

impl flatbuffers::Verifiable for StrWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct StrWrapperArgs<'a> {
    pub value: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for StrWrapperArgs<'a> {
  #[inline]
  fn default() -> Self {
    StrWrapperArgs {
      value: None,
    }
  }
}

pub struct StrWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> StrWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(StrWrapper::VT_VALUE, value);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> StrWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    StrWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<StrWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for StrWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("StrWrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum Int32WrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct Int32Wrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for Int32Wrapper<'a> {
  type Inner = Int32Wrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> Int32Wrapper<'a> {
  pub const VT_VALUE: flatbuffers::VOffsetT = 4;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    Int32Wrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args Int32WrapperArgs
  ) -> flatbuffers::WIPOffset<Int32Wrapper<'bldr>> {
    let mut builder = Int32WrapperBuilder::new(_fbb);
    builder.add_value(args.value);
    builder.finish()
  }


  #[inline]
  pub fn value(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(Int32Wrapper::VT_VALUE, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for Int32Wrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<i32>("value", Self::VT_VALUE, false)?
     .finish();
    Ok(())
  }
}
pub struct Int32WrapperArgs {
    pub value: i32,
}
impl<'a> Default for Int32WrapperArgs {
  #[inline]
  fn default() -> Self {
    Int32WrapperArgs {
      value: 0,
    }
  }
}

pub struct Int32WrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> Int32WrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_value(&mut self, value: i32) {
    self.fbb_.push_slot::<i32>(Int32Wrapper::VT_VALUE, value, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> Int32WrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    Int32WrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<Int32Wrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for Int32Wrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("Int32Wrapper");
      ds.field("value", &self.value());
      ds.finish()
  }
}
pub enum NoneWrapperOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct NoneWrapper<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for NoneWrapper<'a> {
  type Inner = NoneWrapper<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> NoneWrapper<'a> {

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    NoneWrapper { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    _args: &'args NoneWrapperArgs
  ) -> flatbuffers::WIPOffset<NoneWrapper<'bldr>> {
    let mut builder = NoneWrapperBuilder::new(_fbb);
    builder.finish()
  }

}

impl flatbuffers::Verifiable for NoneWrapper<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .finish();
    Ok(())
  }
}
pub struct NoneWrapperArgs {
}
impl<'a> Default for NoneWrapperArgs {
  #[inline]
  fn default() -> Self {
    NoneWrapperArgs {
    }
  }
}

pub struct NoneWrapperBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> NoneWrapperBuilder<'a, 'b, A> {
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> NoneWrapperBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    NoneWrapperBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<NoneWrapper<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for NoneWrapper<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("NoneWrapper");
      ds.finish()
  }
}
pub enum LogEntryOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LogEntry<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LogEntry<'a> {
  type Inner = LogEntry<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LogEntry<'a> {
  pub const VT_LEVEL: flatbuffers::VOffsetT = 4;
  pub const VT_TAG: flatbuffers::VOffsetT = 6;
  pub const VT_FORMAT: flatbuffers::VOffsetT = 8;
  pub const VT_ARGS: flatbuffers::VOffsetT = 10;
  pub const VT_SENSITIVE: flatbuffers::VOffsetT = 12;
  pub const VT_TIMESTAMP: flatbuffers::VOffsetT = 14;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LogEntry { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LogEntryArgs<'args>
  ) -> flatbuffers::WIPOffset<LogEntry<'bldr>> {
    let mut builder = LogEntryBuilder::new(_fbb);
    builder.add_timestamp(args.timestamp);
    if let Some(x) = args.sensitive { builder.add_sensitive(x); }
    if let Some(x) = args.args { builder.add_args(x); }
    if let Some(x) = args.format { builder.add_format(x); }
    if let Some(x) = args.tag { builder.add_tag(x); }
    builder.add_level(args.level);
    builder.finish()
  }


  #[inline]
  pub fn level(&self) -> LogLevel {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<LogLevel>(LogEntry::VT_LEVEL, Some(LogLevel::DEBUG)).unwrap()}
  }
  #[inline]
  pub fn tag(&self) -> &'a str {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LogEntry::VT_TAG, None).unwrap()}
  }
  #[inline]
  pub fn format(&self) -> &'a str {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LogEntry::VT_FORMAT, None).unwrap()}
  }
  #[inline]
  pub fn args(&self) -> Option<flatbuffers::Vector<'a, flatbuffers::ForwardsUOffset<&'a str>>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Vector<'a, flatbuffers::ForwardsUOffset<&'a str>>>>(LogEntry::VT_ARGS, None)}
  }
  #[inline]
  pub fn sensitive(&self) -> Option<flatbuffers::Vector<'a, bool>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Vector<'a, bool>>>(LogEntry::VT_SENSITIVE, None)}
  }
  #[inline]
  pub fn timestamp(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LogEntry::VT_TIMESTAMP, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for LogEntry<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<LogLevel>("level", Self::VT_LEVEL, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("tag", Self::VT_TAG, true)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("format", Self::VT_FORMAT, true)?
     .visit_field::<flatbuffers::ForwardsUOffset<flatbuffers::Vector<'_, flatbuffers::ForwardsUOffset<&'_ str>>>>("args", Self::VT_ARGS, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<flatbuffers::Vector<'_, bool>>>("sensitive", Self::VT_SENSITIVE, false)?
     .visit_field::<i64>("timestamp", Self::VT_TIMESTAMP, false)?
     .finish();
    Ok(())
  }
}
pub struct LogEntryArgs<'a> {
    pub level: LogLevel,
    pub tag: Option<flatbuffers::WIPOffset<&'a str>>,
    pub format: Option<flatbuffers::WIPOffset<&'a str>>,
    pub args: Option<flatbuffers::WIPOffset<flatbuffers::Vector<'a, flatbuffers::ForwardsUOffset<&'a str>>>>,
    pub sensitive: Option<flatbuffers::WIPOffset<flatbuffers::Vector<'a, bool>>>,
    pub timestamp: i64,
}
impl<'a> Default for LogEntryArgs<'a> {
  #[inline]
  fn default() -> Self {
    LogEntryArgs {
      level: LogLevel::DEBUG,
      tag: None, // required field
      format: None, // required field
      args: None,
      sensitive: None,
      timestamp: 0,
    }
  }
}

pub struct LogEntryBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LogEntryBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_level(&mut self, level: LogLevel) {
    self.fbb_.push_slot::<LogLevel>(LogEntry::VT_LEVEL, level, LogLevel::DEBUG);
  }
  #[inline]
  pub fn add_tag(&mut self, tag: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LogEntry::VT_TAG, tag);
  }
  #[inline]
  pub fn add_format(&mut self, format: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LogEntry::VT_FORMAT, format);
  }
  #[inline]
  pub fn add_args(&mut self, args: flatbuffers::WIPOffset<flatbuffers::Vector<'b , flatbuffers::ForwardsUOffset<&'b  str>>>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LogEntry::VT_ARGS, args);
  }
  #[inline]
  pub fn add_sensitive(&mut self, sensitive: flatbuffers::WIPOffset<flatbuffers::Vector<'b , bool>>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LogEntry::VT_SENSITIVE, sensitive);
  }
  #[inline]
  pub fn add_timestamp(&mut self, timestamp: i64) {
    self.fbb_.push_slot::<i64>(LogEntry::VT_TIMESTAMP, timestamp, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LogEntryBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LogEntryBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LogEntry<'a>> {
    let o = self.fbb_.end_table(self.start_);
    self.fbb_.required(o, LogEntry::VT_TAG,"tag");
    self.fbb_.required(o, LogEntry::VT_FORMAT,"format");
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LogEntry<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LogEntry");
      ds.field("level", &self.level());
      ds.field("tag", &self.tag());
      ds.field("format", &self.format());
      ds.field("args", &self.args());
      ds.field("sensitive", &self.sensitive());
      ds.field("timestamp", &self.timestamp());
      ds.finish()
  }
}
pub enum LoggerConfigOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LoggerConfig<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LoggerConfig<'a> {
  type Inner = LoggerConfig<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LoggerConfig<'a> {
  pub const VT_LOG_LEVEL: flatbuffers::VOffsetT = 4;
  pub const VT_ENABLE_CONSOLE_OUTPUT: flatbuffers::VOffsetT = 6;
  pub const VT_ENABLE_FILE_OUTPUT: flatbuffers::VOffsetT = 8;
  pub const VT_ENABLE_FULL_LOG: flatbuffers::VOffsetT = 10;
  pub const VT_TEST_MODE: flatbuffers::VOffsetT = 12;
  pub const VT_LOG_ENV: flatbuffers::VOffsetT = 14;
  pub const VT_DISABLE_SENSITIVE_WORDS: flatbuffers::VOffsetT = 16;
  pub const VT_USER_ID: flatbuffers::VOffsetT = 18;
  pub const VT_DEVICE_ID: flatbuffers::VOffsetT = 20;
  pub const VT_SESSION_ID: flatbuffers::VOffsetT = 22;
  pub const VT_APP_VERSION: flatbuffers::VOffsetT = 24;
  pub const VT_PRIVACY_AGREED: flatbuffers::VOffsetT = 26;
  pub const VT_IS_DEBUG_MODE: flatbuffers::VOffsetT = 28;
  pub const VT_MAX_FILE_SIZE: flatbuffers::VOffsetT = 30;
  pub const VT_MAX_DIRECTORY_SIZE: flatbuffers::VOffsetT = 32;
  pub const VT_LOG_FILE_PREFIX: flatbuffers::VOffsetT = 34;
  pub const VT_LOG_DIRECTORY: flatbuffers::VOffsetT = 36;
  pub const VT_UPLOAD_URL: flatbuffers::VOffsetT = 38;
  pub const VT_MAX_RETRY_COUNT: flatbuffers::VOffsetT = 40;
  pub const VT_UPLOAD_TIMEOUT_SECONDS: flatbuffers::VOffsetT = 42;
  pub const VT_MAX_ZIP_FILE_SIZE: flatbuffers::VOffsetT = 44;
  pub const VT_CUSTOM_PREFIX: flatbuffers::VOffsetT = 46;
  pub const VT_MAX_LOG_LENGTH: flatbuffers::VOffsetT = 48;
  pub const VT_MAX_LOGS_PER_SECOND: flatbuffers::VOffsetT = 50;
  pub const VT_BUFFER_SIZE: flatbuffers::VOffsetT = 52;
  pub const VT_VERSION_NAME: flatbuffers::VOffsetT = 54;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LoggerConfig { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LoggerConfigArgs<'args>
  ) -> flatbuffers::WIPOffset<LoggerConfig<'bldr>> {
    let mut builder = LoggerConfigBuilder::new(_fbb);
    builder.add_max_zip_file_size(args.max_zip_file_size);
    builder.add_upload_timeout_seconds(args.upload_timeout_seconds);
    builder.add_max_directory_size(args.max_directory_size);
    builder.add_max_file_size(args.max_file_size);
    if let Some(x) = args.version_name { builder.add_version_name(x); }
    builder.add_buffer_size(args.buffer_size);
    builder.add_max_logs_per_second(args.max_logs_per_second);
    builder.add_max_log_length(args.max_log_length);
    if let Some(x) = args.custom_prefix { builder.add_custom_prefix(x); }
    builder.add_max_retry_count(args.max_retry_count);
    if let Some(x) = args.upload_url { builder.add_upload_url(x); }
    if let Some(x) = args.log_directory { builder.add_log_directory(x); }
    if let Some(x) = args.log_file_prefix { builder.add_log_file_prefix(x); }
    if let Some(x) = args.app_version { builder.add_app_version(x); }
    if let Some(x) = args.session_id { builder.add_session_id(x); }
    if let Some(x) = args.device_id { builder.add_device_id(x); }
    if let Some(x) = args.user_id { builder.add_user_id(x); }
    if let Some(x) = args.log_env { builder.add_log_env(x); }
    builder.add_is_debug_mode(args.is_debug_mode);
    builder.add_privacy_agreed(args.privacy_agreed);
    builder.add_disable_sensitive_words(args.disable_sensitive_words);
    builder.add_test_mode(args.test_mode);
    builder.add_enable_full_log(args.enable_full_log);
    builder.add_enable_file_output(args.enable_file_output);
    builder.add_enable_console_output(args.enable_console_output);
    builder.add_log_level(args.log_level);
    builder.finish()
  }


  #[inline]
  pub fn log_level(&self) -> LogLevel {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<LogLevel>(LoggerConfig::VT_LOG_LEVEL, Some(LogLevel::DEBUG)).unwrap()}
  }
  #[inline]
  pub fn enable_console_output(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_ENABLE_CONSOLE_OUTPUT, Some(false)).unwrap()}
  }
  #[inline]
  pub fn enable_file_output(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_ENABLE_FILE_OUTPUT, Some(false)).unwrap()}
  }
  #[inline]
  pub fn enable_full_log(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_ENABLE_FULL_LOG, Some(false)).unwrap()}
  }
  #[inline]
  pub fn test_mode(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_TEST_MODE, Some(false)).unwrap()}
  }
  #[inline]
  pub fn log_env(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_LOG_ENV, None)}
  }
  #[inline]
  pub fn disable_sensitive_words(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_DISABLE_SENSITIVE_WORDS, Some(false)).unwrap()}
  }
  #[inline]
  pub fn user_id(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_USER_ID, None)}
  }
  #[inline]
  pub fn device_id(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_DEVICE_ID, None)}
  }
  #[inline]
  pub fn session_id(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_SESSION_ID, None)}
  }
  #[inline]
  pub fn app_version(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_APP_VERSION, None)}
  }
  #[inline]
  pub fn privacy_agreed(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_PRIVACY_AGREED, Some(false)).unwrap()}
  }
  #[inline]
  pub fn is_debug_mode(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerConfig::VT_IS_DEBUG_MODE, Some(false)).unwrap()}
  }
  #[inline]
  pub fn max_file_size(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LoggerConfig::VT_MAX_FILE_SIZE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn max_directory_size(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LoggerConfig::VT_MAX_DIRECTORY_SIZE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn log_file_prefix(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_LOG_FILE_PREFIX, None)}
  }
  #[inline]
  pub fn log_directory(&self) -> &'a str {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_LOG_DIRECTORY, None).unwrap()}
  }
  #[inline]
  pub fn upload_url(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_UPLOAD_URL, None)}
  }
  #[inline]
  pub fn max_retry_count(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerConfig::VT_MAX_RETRY_COUNT, Some(0)).unwrap()}
  }
  #[inline]
  pub fn upload_timeout_seconds(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LoggerConfig::VT_UPLOAD_TIMEOUT_SECONDS, Some(0)).unwrap()}
  }
  #[inline]
  pub fn max_zip_file_size(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LoggerConfig::VT_MAX_ZIP_FILE_SIZE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn custom_prefix(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_CUSTOM_PREFIX, None)}
  }
  #[inline]
  pub fn max_log_length(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerConfig::VT_MAX_LOG_LENGTH, Some(0)).unwrap()}
  }
  #[inline]
  pub fn max_logs_per_second(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerConfig::VT_MAX_LOGS_PER_SECOND, Some(0)).unwrap()}
  }
  #[inline]
  pub fn buffer_size(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerConfig::VT_BUFFER_SIZE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn version_name(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerConfig::VT_VERSION_NAME, None)}
  }
}

impl flatbuffers::Verifiable for LoggerConfig<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<LogLevel>("log_level", Self::VT_LOG_LEVEL, false)?
     .visit_field::<bool>("enable_console_output", Self::VT_ENABLE_CONSOLE_OUTPUT, false)?
     .visit_field::<bool>("enable_file_output", Self::VT_ENABLE_FILE_OUTPUT, false)?
     .visit_field::<bool>("enable_full_log", Self::VT_ENABLE_FULL_LOG, false)?
     .visit_field::<bool>("test_mode", Self::VT_TEST_MODE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("log_env", Self::VT_LOG_ENV, false)?
     .visit_field::<bool>("disable_sensitive_words", Self::VT_DISABLE_SENSITIVE_WORDS, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("user_id", Self::VT_USER_ID, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("device_id", Self::VT_DEVICE_ID, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("session_id", Self::VT_SESSION_ID, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("app_version", Self::VT_APP_VERSION, false)?
     .visit_field::<bool>("privacy_agreed", Self::VT_PRIVACY_AGREED, false)?
     .visit_field::<bool>("is_debug_mode", Self::VT_IS_DEBUG_MODE, false)?
     .visit_field::<i64>("max_file_size", Self::VT_MAX_FILE_SIZE, false)?
     .visit_field::<i64>("max_directory_size", Self::VT_MAX_DIRECTORY_SIZE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("log_file_prefix", Self::VT_LOG_FILE_PREFIX, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("log_directory", Self::VT_LOG_DIRECTORY, true)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("upload_url", Self::VT_UPLOAD_URL, false)?
     .visit_field::<i32>("max_retry_count", Self::VT_MAX_RETRY_COUNT, false)?
     .visit_field::<i64>("upload_timeout_seconds", Self::VT_UPLOAD_TIMEOUT_SECONDS, false)?
     .visit_field::<i64>("max_zip_file_size", Self::VT_MAX_ZIP_FILE_SIZE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("custom_prefix", Self::VT_CUSTOM_PREFIX, false)?
     .visit_field::<i32>("max_log_length", Self::VT_MAX_LOG_LENGTH, false)?
     .visit_field::<i32>("max_logs_per_second", Self::VT_MAX_LOGS_PER_SECOND, false)?
     .visit_field::<i32>("buffer_size", Self::VT_BUFFER_SIZE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("version_name", Self::VT_VERSION_NAME, false)?
     .finish();
    Ok(())
  }
}
pub struct LoggerConfigArgs<'a> {
    pub log_level: LogLevel,
    pub enable_console_output: bool,
    pub enable_file_output: bool,
    pub enable_full_log: bool,
    pub test_mode: bool,
    pub log_env: Option<flatbuffers::WIPOffset<&'a str>>,
    pub disable_sensitive_words: bool,
    pub user_id: Option<flatbuffers::WIPOffset<&'a str>>,
    pub device_id: Option<flatbuffers::WIPOffset<&'a str>>,
    pub session_id: Option<flatbuffers::WIPOffset<&'a str>>,
    pub app_version: Option<flatbuffers::WIPOffset<&'a str>>,
    pub privacy_agreed: bool,
    pub is_debug_mode: bool,
    pub max_file_size: i64,
    pub max_directory_size: i64,
    pub log_file_prefix: Option<flatbuffers::WIPOffset<&'a str>>,
    pub log_directory: Option<flatbuffers::WIPOffset<&'a str>>,
    pub upload_url: Option<flatbuffers::WIPOffset<&'a str>>,
    pub max_retry_count: i32,
    pub upload_timeout_seconds: i64,
    pub max_zip_file_size: i64,
    pub custom_prefix: Option<flatbuffers::WIPOffset<&'a str>>,
    pub max_log_length: i32,
    pub max_logs_per_second: i32,
    pub buffer_size: i32,
    pub version_name: Option<flatbuffers::WIPOffset<&'a str>>,
}
impl<'a> Default for LoggerConfigArgs<'a> {
  #[inline]
  fn default() -> Self {
    LoggerConfigArgs {
      log_level: LogLevel::DEBUG,
      enable_console_output: false,
      enable_file_output: false,
      enable_full_log: false,
      test_mode: false,
      log_env: None,
      disable_sensitive_words: false,
      user_id: None,
      device_id: None,
      session_id: None,
      app_version: None,
      privacy_agreed: false,
      is_debug_mode: false,
      max_file_size: 0,
      max_directory_size: 0,
      log_file_prefix: None,
      log_directory: None, // required field
      upload_url: None,
      max_retry_count: 0,
      upload_timeout_seconds: 0,
      max_zip_file_size: 0,
      custom_prefix: None,
      max_log_length: 0,
      max_logs_per_second: 0,
      buffer_size: 0,
      version_name: None,
    }
  }
}

pub struct LoggerConfigBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LoggerConfigBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_log_level(&mut self, log_level: LogLevel) {
    self.fbb_.push_slot::<LogLevel>(LoggerConfig::VT_LOG_LEVEL, log_level, LogLevel::DEBUG);
  }
  #[inline]
  pub fn add_enable_console_output(&mut self, enable_console_output: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_ENABLE_CONSOLE_OUTPUT, enable_console_output, false);
  }
  #[inline]
  pub fn add_enable_file_output(&mut self, enable_file_output: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_ENABLE_FILE_OUTPUT, enable_file_output, false);
  }
  #[inline]
  pub fn add_enable_full_log(&mut self, enable_full_log: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_ENABLE_FULL_LOG, enable_full_log, false);
  }
  #[inline]
  pub fn add_test_mode(&mut self, test_mode: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_TEST_MODE, test_mode, false);
  }
  #[inline]
  pub fn add_log_env(&mut self, log_env: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_LOG_ENV, log_env);
  }
  #[inline]
  pub fn add_disable_sensitive_words(&mut self, disable_sensitive_words: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_DISABLE_SENSITIVE_WORDS, disable_sensitive_words, false);
  }
  #[inline]
  pub fn add_user_id(&mut self, user_id: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_USER_ID, user_id);
  }
  #[inline]
  pub fn add_device_id(&mut self, device_id: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_DEVICE_ID, device_id);
  }
  #[inline]
  pub fn add_session_id(&mut self, session_id: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_SESSION_ID, session_id);
  }
  #[inline]
  pub fn add_app_version(&mut self, app_version: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_APP_VERSION, app_version);
  }
  #[inline]
  pub fn add_privacy_agreed(&mut self, privacy_agreed: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_PRIVACY_AGREED, privacy_agreed, false);
  }
  #[inline]
  pub fn add_is_debug_mode(&mut self, is_debug_mode: bool) {
    self.fbb_.push_slot::<bool>(LoggerConfig::VT_IS_DEBUG_MODE, is_debug_mode, false);
  }
  #[inline]
  pub fn add_max_file_size(&mut self, max_file_size: i64) {
    self.fbb_.push_slot::<i64>(LoggerConfig::VT_MAX_FILE_SIZE, max_file_size, 0);
  }
  #[inline]
  pub fn add_max_directory_size(&mut self, max_directory_size: i64) {
    self.fbb_.push_slot::<i64>(LoggerConfig::VT_MAX_DIRECTORY_SIZE, max_directory_size, 0);
  }
  #[inline]
  pub fn add_log_file_prefix(&mut self, log_file_prefix: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_LOG_FILE_PREFIX, log_file_prefix);
  }
  #[inline]
  pub fn add_log_directory(&mut self, log_directory: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_LOG_DIRECTORY, log_directory);
  }
  #[inline]
  pub fn add_upload_url(&mut self, upload_url: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_UPLOAD_URL, upload_url);
  }
  #[inline]
  pub fn add_max_retry_count(&mut self, max_retry_count: i32) {
    self.fbb_.push_slot::<i32>(LoggerConfig::VT_MAX_RETRY_COUNT, max_retry_count, 0);
  }
  #[inline]
  pub fn add_upload_timeout_seconds(&mut self, upload_timeout_seconds: i64) {
    self.fbb_.push_slot::<i64>(LoggerConfig::VT_UPLOAD_TIMEOUT_SECONDS, upload_timeout_seconds, 0);
  }
  #[inline]
  pub fn add_max_zip_file_size(&mut self, max_zip_file_size: i64) {
    self.fbb_.push_slot::<i64>(LoggerConfig::VT_MAX_ZIP_FILE_SIZE, max_zip_file_size, 0);
  }
  #[inline]
  pub fn add_custom_prefix(&mut self, custom_prefix: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_CUSTOM_PREFIX, custom_prefix);
  }
  #[inline]
  pub fn add_max_log_length(&mut self, max_log_length: i32) {
    self.fbb_.push_slot::<i32>(LoggerConfig::VT_MAX_LOG_LENGTH, max_log_length, 0);
  }
  #[inline]
  pub fn add_max_logs_per_second(&mut self, max_logs_per_second: i32) {
    self.fbb_.push_slot::<i32>(LoggerConfig::VT_MAX_LOGS_PER_SECOND, max_logs_per_second, 0);
  }
  #[inline]
  pub fn add_buffer_size(&mut self, buffer_size: i32) {
    self.fbb_.push_slot::<i32>(LoggerConfig::VT_BUFFER_SIZE, buffer_size, 0);
  }
  #[inline]
  pub fn add_version_name(&mut self, version_name: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerConfig::VT_VERSION_NAME, version_name);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LoggerConfigBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LoggerConfigBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LoggerConfig<'a>> {
    let o = self.fbb_.end_table(self.start_);
    self.fbb_.required(o, LoggerConfig::VT_LOG_DIRECTORY,"log_directory");
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LoggerConfig<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LoggerConfig");
      ds.field("log_level", &self.log_level());
      ds.field("enable_console_output", &self.enable_console_output());
      ds.field("enable_file_output", &self.enable_file_output());
      ds.field("enable_full_log", &self.enable_full_log());
      ds.field("test_mode", &self.test_mode());
      ds.field("log_env", &self.log_env());
      ds.field("disable_sensitive_words", &self.disable_sensitive_words());
      ds.field("user_id", &self.user_id());
      ds.field("device_id", &self.device_id());
      ds.field("session_id", &self.session_id());
      ds.field("app_version", &self.app_version());
      ds.field("privacy_agreed", &self.privacy_agreed());
      ds.field("is_debug_mode", &self.is_debug_mode());
      ds.field("max_file_size", &self.max_file_size());
      ds.field("max_directory_size", &self.max_directory_size());
      ds.field("log_file_prefix", &self.log_file_prefix());
      ds.field("log_directory", &self.log_directory());
      ds.field("upload_url", &self.upload_url());
      ds.field("max_retry_count", &self.max_retry_count());
      ds.field("upload_timeout_seconds", &self.upload_timeout_seconds());
      ds.field("max_zip_file_size", &self.max_zip_file_size());
      ds.field("custom_prefix", &self.custom_prefix());
      ds.field("max_log_length", &self.max_log_length());
      ds.field("max_logs_per_second", &self.max_logs_per_second());
      ds.field("buffer_size", &self.buffer_size());
      ds.field("version_name", &self.version_name());
      ds.finish()
  }
}
pub enum UploadProgressOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct UploadProgress<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for UploadProgress<'a> {
  type Inner = UploadProgress<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> UploadProgress<'a> {
  pub const VT_TOTAL_FILES: flatbuffers::VOffsetT = 4;
  pub const VT_UPLOADED_FILES: flatbuffers::VOffsetT = 6;
  pub const VT_CURRENT_FILE: flatbuffers::VOffsetT = 8;
  pub const VT_PROGRESS_PERCENT: flatbuffers::VOffsetT = 10;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    UploadProgress { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args UploadProgressArgs<'args>
  ) -> flatbuffers::WIPOffset<UploadProgress<'bldr>> {
    let mut builder = UploadProgressBuilder::new(_fbb);
    builder.add_progress_percent(args.progress_percent);
    if let Some(x) = args.current_file { builder.add_current_file(x); }
    builder.add_uploaded_files(args.uploaded_files);
    builder.add_total_files(args.total_files);
    builder.finish()
  }


  #[inline]
  pub fn total_files(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(UploadProgress::VT_TOTAL_FILES, Some(0)).unwrap()}
  }
  #[inline]
  pub fn uploaded_files(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(UploadProgress::VT_UPLOADED_FILES, Some(0)).unwrap()}
  }
  #[inline]
  pub fn current_file(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(UploadProgress::VT_CURRENT_FILE, None)}
  }
  #[inline]
  pub fn progress_percent(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(UploadProgress::VT_PROGRESS_PERCENT, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for UploadProgress<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<i32>("total_files", Self::VT_TOTAL_FILES, false)?
     .visit_field::<i32>("uploaded_files", Self::VT_UPLOADED_FILES, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("current_file", Self::VT_CURRENT_FILE, false)?
     .visit_field::<i32>("progress_percent", Self::VT_PROGRESS_PERCENT, false)?
     .finish();
    Ok(())
  }
}
pub struct UploadProgressArgs<'a> {
    pub total_files: i32,
    pub uploaded_files: i32,
    pub current_file: Option<flatbuffers::WIPOffset<&'a str>>,
    pub progress_percent: i32,
}
impl<'a> Default for UploadProgressArgs<'a> {
  #[inline]
  fn default() -> Self {
    UploadProgressArgs {
      total_files: 0,
      uploaded_files: 0,
      current_file: None,
      progress_percent: 0,
    }
  }
}

pub struct UploadProgressBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> UploadProgressBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_total_files(&mut self, total_files: i32) {
    self.fbb_.push_slot::<i32>(UploadProgress::VT_TOTAL_FILES, total_files, 0);
  }
  #[inline]
  pub fn add_uploaded_files(&mut self, uploaded_files: i32) {
    self.fbb_.push_slot::<i32>(UploadProgress::VT_UPLOADED_FILES, uploaded_files, 0);
  }
  #[inline]
  pub fn add_current_file(&mut self, current_file: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(UploadProgress::VT_CURRENT_FILE, current_file);
  }
  #[inline]
  pub fn add_progress_percent(&mut self, progress_percent: i32) {
    self.fbb_.push_slot::<i32>(UploadProgress::VT_PROGRESS_PERCENT, progress_percent, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> UploadProgressBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    UploadProgressBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<UploadProgress<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for UploadProgress<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("UploadProgress");
      ds.field("total_files", &self.total_files());
      ds.field("uploaded_files", &self.uploaded_files());
      ds.field("current_file", &self.current_file());
      ds.field("progress_percent", &self.progress_percent());
      ds.finish()
  }
}
pub enum LogStatsOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LogStats<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LogStats<'a> {
  type Inner = LogStats<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LogStats<'a> {
  pub const VT_TOTAL_LOGS: flatbuffers::VOffsetT = 4;
  pub const VT_ERROR_LOGS: flatbuffers::VOffsetT = 6;
  pub const VT_WARNING_LOGS: flatbuffers::VOffsetT = 8;
  pub const VT_FILE_COUNT: flatbuffers::VOffsetT = 10;
  pub const VT_TOTAL_SIZE: flatbuffers::VOffsetT = 12;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LogStats { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LogStatsArgs
  ) -> flatbuffers::WIPOffset<LogStats<'bldr>> {
    let mut builder = LogStatsBuilder::new(_fbb);
    builder.add_total_size(args.total_size);
    builder.add_warning_logs(args.warning_logs);
    builder.add_error_logs(args.error_logs);
    builder.add_total_logs(args.total_logs);
    builder.add_file_count(args.file_count);
    builder.finish()
  }


  #[inline]
  pub fn total_logs(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LogStats::VT_TOTAL_LOGS, Some(0)).unwrap()}
  }
  #[inline]
  pub fn error_logs(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LogStats::VT_ERROR_LOGS, Some(0)).unwrap()}
  }
  #[inline]
  pub fn warning_logs(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LogStats::VT_WARNING_LOGS, Some(0)).unwrap()}
  }
  #[inline]
  pub fn file_count(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LogStats::VT_FILE_COUNT, Some(0)).unwrap()}
  }
  #[inline]
  pub fn total_size(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LogStats::VT_TOTAL_SIZE, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for LogStats<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<i64>("total_logs", Self::VT_TOTAL_LOGS, false)?
     .visit_field::<i64>("error_logs", Self::VT_ERROR_LOGS, false)?
     .visit_field::<i64>("warning_logs", Self::VT_WARNING_LOGS, false)?
     .visit_field::<i32>("file_count", Self::VT_FILE_COUNT, false)?
     .visit_field::<i64>("total_size", Self::VT_TOTAL_SIZE, false)?
     .finish();
    Ok(())
  }
}
pub struct LogStatsArgs {
    pub total_logs: i64,
    pub error_logs: i64,
    pub warning_logs: i64,
    pub file_count: i32,
    pub total_size: i64,
}
impl<'a> Default for LogStatsArgs {
  #[inline]
  fn default() -> Self {
    LogStatsArgs {
      total_logs: 0,
      error_logs: 0,
      warning_logs: 0,
      file_count: 0,
      total_size: 0,
    }
  }
}

pub struct LogStatsBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LogStatsBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_total_logs(&mut self, total_logs: i64) {
    self.fbb_.push_slot::<i64>(LogStats::VT_TOTAL_LOGS, total_logs, 0);
  }
  #[inline]
  pub fn add_error_logs(&mut self, error_logs: i64) {
    self.fbb_.push_slot::<i64>(LogStats::VT_ERROR_LOGS, error_logs, 0);
  }
  #[inline]
  pub fn add_warning_logs(&mut self, warning_logs: i64) {
    self.fbb_.push_slot::<i64>(LogStats::VT_WARNING_LOGS, warning_logs, 0);
  }
  #[inline]
  pub fn add_file_count(&mut self, file_count: i32) {
    self.fbb_.push_slot::<i32>(LogStats::VT_FILE_COUNT, file_count, 0);
  }
  #[inline]
  pub fn add_total_size(&mut self, total_size: i64) {
    self.fbb_.push_slot::<i64>(LogStats::VT_TOTAL_SIZE, total_size, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LogStatsBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LogStatsBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LogStats<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LogStats<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LogStats");
      ds.field("total_logs", &self.total_logs());
      ds.field("error_logs", &self.error_logs());
      ds.field("warning_logs", &self.warning_logs());
      ds.field("file_count", &self.file_count());
      ds.field("total_size", &self.total_size());
      ds.finish()
  }
}
pub enum LargeFileEventOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LargeFileEvent<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LargeFileEvent<'a> {
  type Inner = LargeFileEvent<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LargeFileEvent<'a> {
  pub const VT_EVENT_TYPE: flatbuffers::VOffsetT = 4;
  pub const VT_FILE_PATH: flatbuffers::VOffsetT = 6;
  pub const VT_FILE_SIZE: flatbuffers::VOffsetT = 8;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LargeFileEvent { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LargeFileEventArgs<'args>
  ) -> flatbuffers::WIPOffset<LargeFileEvent<'bldr>> {
    let mut builder = LargeFileEventBuilder::new(_fbb);
    builder.add_file_size(args.file_size);
    if let Some(x) = args.file_path { builder.add_file_path(x); }
    if let Some(x) = args.event_type { builder.add_event_type(x); }
    builder.finish()
  }


  #[inline]
  pub fn event_type(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LargeFileEvent::VT_EVENT_TYPE, None)}
  }
  #[inline]
  pub fn file_path(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LargeFileEvent::VT_FILE_PATH, None)}
  }
  #[inline]
  pub fn file_size(&self) -> i64 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i64>(LargeFileEvent::VT_FILE_SIZE, Some(0)).unwrap()}
  }
}

impl flatbuffers::Verifiable for LargeFileEvent<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("event_type", Self::VT_EVENT_TYPE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("file_path", Self::VT_FILE_PATH, false)?
     .visit_field::<i64>("file_size", Self::VT_FILE_SIZE, false)?
     .finish();
    Ok(())
  }
}
pub struct LargeFileEventArgs<'a> {
    pub event_type: Option<flatbuffers::WIPOffset<&'a str>>,
    pub file_path: Option<flatbuffers::WIPOffset<&'a str>>,
    pub file_size: i64,
}
impl<'a> Default for LargeFileEventArgs<'a> {
  #[inline]
  fn default() -> Self {
    LargeFileEventArgs {
      event_type: None,
      file_path: None,
      file_size: 0,
    }
  }
}

pub struct LargeFileEventBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LargeFileEventBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_event_type(&mut self, event_type: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LargeFileEvent::VT_EVENT_TYPE, event_type);
  }
  #[inline]
  pub fn add_file_path(&mut self, file_path: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LargeFileEvent::VT_FILE_PATH, file_path);
  }
  #[inline]
  pub fn add_file_size(&mut self, file_size: i64) {
    self.fbb_.push_slot::<i64>(LargeFileEvent::VT_FILE_SIZE, file_size, 0);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LargeFileEventBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LargeFileEventBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LargeFileEvent<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LargeFileEvent<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LargeFileEvent");
      ds.field("event_type", &self.event_type());
      ds.field("file_path", &self.file_path());
      ds.field("file_size", &self.file_size());
      ds.finish()
  }
}
pub enum LoggerMessageOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LoggerMessage<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LoggerMessage<'a> {
  type Inner = LoggerMessage<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LoggerMessage<'a> {
  pub const VT_CODE: flatbuffers::VOffsetT = 4;
  pub const VT_CONTAINER_TYPE: flatbuffers::VOffsetT = 6;
  pub const VT_CONTAINER: flatbuffers::VOffsetT = 8;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LoggerMessage { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LoggerMessageArgs
  ) -> flatbuffers::WIPOffset<LoggerMessage<'bldr>> {
    let mut builder = LoggerMessageBuilder::new(_fbb);
    if let Some(x) = args.container { builder.add_container(x); }
    builder.add_code(args.code);
    builder.add_container_type(args.container_type);
    builder.finish()
  }


  #[inline]
  pub fn code(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerMessage::VT_CODE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn container_type(&self) -> LoggerContainer {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<LoggerContainer>(LoggerMessage::VT_CONTAINER_TYPE, Some(LoggerContainer::NONE)).unwrap()}
  }
  #[inline]
  pub fn container(&self) -> Option<flatbuffers::Table<'a>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Table<'a>>>(LoggerMessage::VT_CONTAINER, None)}
  }
  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_bool_wrapper(&self) -> Option<BoolWrapper<'a>> {
    if self.container_type() == LoggerContainer::BoolWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { BoolWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_str_wrapper(&self) -> Option<StrWrapper<'a>> {
    if self.container_type() == LoggerContainer::StrWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { StrWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_int_32_wrapper(&self) -> Option<Int32Wrapper<'a>> {
    if self.container_type() == LoggerContainer::Int32Wrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { Int32Wrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_none_wrapper(&self) -> Option<NoneWrapper<'a>> {
    if self.container_type() == LoggerContainer::NoneWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { NoneWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_log_entry(&self) -> Option<LogEntry<'a>> {
    if self.container_type() == LoggerContainer::LogEntry {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LogEntry::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_logger_config(&self) -> Option<LoggerConfig<'a>> {
    if self.container_type() == LoggerContainer::LoggerConfig {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LoggerConfig::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_upload_progress(&self) -> Option<UploadProgress<'a>> {
    if self.container_type() == LoggerContainer::UploadProgress {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { UploadProgress::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_log_stats(&self) -> Option<LogStats<'a>> {
    if self.container_type() == LoggerContainer::LogStats {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LogStats::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_large_file_event(&self) -> Option<LargeFileEvent<'a>> {
    if self.container_type() == LoggerContainer::LargeFileEvent {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LargeFileEvent::init_from_table(t) }
     })
    } else {
      None
    }
  }

}

impl flatbuffers::Verifiable for LoggerMessage<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_field::<i32>("code", Self::VT_CODE, false)?
     .visit_union::<LoggerContainer, _>("container_type", Self::VT_CONTAINER_TYPE, "container", Self::VT_CONTAINER, false, |key, v, pos| {
        match key {
          LoggerContainer::BoolWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<BoolWrapper>>("LoggerContainer::BoolWrapper", pos),
          LoggerContainer::StrWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<StrWrapper>>("LoggerContainer::StrWrapper", pos),
          LoggerContainer::Int32Wrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<Int32Wrapper>>("LoggerContainer::Int32Wrapper", pos),
          LoggerContainer::NoneWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<NoneWrapper>>("LoggerContainer::NoneWrapper", pos),
          LoggerContainer::LogEntry => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LogEntry>>("LoggerContainer::LogEntry", pos),
          LoggerContainer::LoggerConfig => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LoggerConfig>>("LoggerContainer::LoggerConfig", pos),
          LoggerContainer::UploadProgress => v.verify_union_variant::<flatbuffers::ForwardsUOffset<UploadProgress>>("LoggerContainer::UploadProgress", pos),
          LoggerContainer::LogStats => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LogStats>>("LoggerContainer::LogStats", pos),
          LoggerContainer::LargeFileEvent => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LargeFileEvent>>("LoggerContainer::LargeFileEvent", pos),
          _ => Ok(()),
        }
     })?
     .finish();
    Ok(())
  }
}
pub struct LoggerMessageArgs {
    pub code: i32,
    pub container_type: LoggerContainer,
    pub container: Option<flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>>,
}
impl<'a> Default for LoggerMessageArgs {
  #[inline]
  fn default() -> Self {
    LoggerMessageArgs {
      code: 0,
      container_type: LoggerContainer::NONE,
      container: None,
    }
  }
}

pub struct LoggerMessageBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LoggerMessageBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_code(&mut self, code: i32) {
    self.fbb_.push_slot::<i32>(LoggerMessage::VT_CODE, code, 0);
  }
  #[inline]
  pub fn add_container_type(&mut self, container_type: LoggerContainer) {
    self.fbb_.push_slot::<LoggerContainer>(LoggerMessage::VT_CONTAINER_TYPE, container_type, LoggerContainer::NONE);
  }
  #[inline]
  pub fn add_container(&mut self, container: flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerMessage::VT_CONTAINER, container);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LoggerMessageBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LoggerMessageBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LoggerMessage<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LoggerMessage<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LoggerMessage");
      ds.field("code", &self.code());
      ds.field("container_type", &self.container_type());
      match self.container_type() {
        LoggerContainer::BoolWrapper => {
          if let Some(x) = self.container_as_bool_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::StrWrapper => {
          if let Some(x) = self.container_as_str_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::Int32Wrapper => {
          if let Some(x) = self.container_as_int_32_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::NoneWrapper => {
          if let Some(x) = self.container_as_none_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LogEntry => {
          if let Some(x) = self.container_as_log_entry() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LoggerConfig => {
          if let Some(x) = self.container_as_logger_config() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::UploadProgress => {
          if let Some(x) = self.container_as_upload_progress() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LogStats => {
          if let Some(x) = self.container_as_log_stats() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LargeFileEvent => {
          if let Some(x) = self.container_as_large_file_event() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        _ => {
          let x: Option<()> = None;
          ds.field("container", &x)
        },
      };
      ds.finish()
  }
}
pub enum LoggerFlatOffset {}
#[derive(Copy, Clone, PartialEq)]

pub struct LoggerFlat<'a> {
  pub _tab: flatbuffers::Table<'a>,
}

impl<'a> flatbuffers::Follow<'a> for LoggerFlat<'a> {
  type Inner = LoggerFlat<'a>;
  #[inline]
  unsafe fn follow(buf: &'a [u8], loc: usize) -> Self::Inner {
    Self { _tab: flatbuffers::Table::new(buf, loc) }
  }
}

impl<'a> LoggerFlat<'a> {
  pub const VT_CONTAINER_TYPE: flatbuffers::VOffsetT = 4;
  pub const VT_CONTAINER: flatbuffers::VOffsetT = 6;
  pub const VT_CODE: flatbuffers::VOffsetT = 8;
  pub const VT_MESSAGE: flatbuffers::VOffsetT = 10;
  pub const VT_SUCCESS: flatbuffers::VOffsetT = 12;

  #[inline]
  pub unsafe fn init_from_table(table: flatbuffers::Table<'a>) -> Self {
    LoggerFlat { _tab: table }
  }
  #[allow(unused_mut)]
  pub fn create<'bldr: 'args, 'args: 'mut_bldr, 'mut_bldr, A: flatbuffers::Allocator + 'bldr>(
    _fbb: &'mut_bldr mut flatbuffers::FlatBufferBuilder<'bldr, A>,
    args: &'args LoggerFlatArgs<'args>
  ) -> flatbuffers::WIPOffset<LoggerFlat<'bldr>> {
    let mut builder = LoggerFlatBuilder::new(_fbb);
    if let Some(x) = args.message { builder.add_message(x); }
    builder.add_code(args.code);
    if let Some(x) = args.container { builder.add_container(x); }
    builder.add_success(args.success);
    builder.add_container_type(args.container_type);
    builder.finish()
  }


  #[inline]
  pub fn container_type(&self) -> LoggerContainer {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<LoggerContainer>(LoggerFlat::VT_CONTAINER_TYPE, Some(LoggerContainer::NONE)).unwrap()}
  }
  #[inline]
  pub fn container(&self) -> Option<flatbuffers::Table<'a>> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<flatbuffers::Table<'a>>>(LoggerFlat::VT_CONTAINER, None)}
  }
  #[inline]
  pub fn code(&self) -> i32 {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<i32>(LoggerFlat::VT_CODE, Some(0)).unwrap()}
  }
  #[inline]
  pub fn message(&self) -> Option<&'a str> {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<flatbuffers::ForwardsUOffset<&str>>(LoggerFlat::VT_MESSAGE, None)}
  }
  #[inline]
  pub fn success(&self) -> bool {
    // Safety:
    // Created from valid Table for this object
    // which contains a valid value in this slot
    unsafe { self._tab.get::<bool>(LoggerFlat::VT_SUCCESS, Some(false)).unwrap()}
  }
  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_bool_wrapper(&self) -> Option<BoolWrapper<'a>> {
    if self.container_type() == LoggerContainer::BoolWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { BoolWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_str_wrapper(&self) -> Option<StrWrapper<'a>> {
    if self.container_type() == LoggerContainer::StrWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { StrWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_int_32_wrapper(&self) -> Option<Int32Wrapper<'a>> {
    if self.container_type() == LoggerContainer::Int32Wrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { Int32Wrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_none_wrapper(&self) -> Option<NoneWrapper<'a>> {
    if self.container_type() == LoggerContainer::NoneWrapper {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { NoneWrapper::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_log_entry(&self) -> Option<LogEntry<'a>> {
    if self.container_type() == LoggerContainer::LogEntry {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LogEntry::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_logger_config(&self) -> Option<LoggerConfig<'a>> {
    if self.container_type() == LoggerContainer::LoggerConfig {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LoggerConfig::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_upload_progress(&self) -> Option<UploadProgress<'a>> {
    if self.container_type() == LoggerContainer::UploadProgress {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { UploadProgress::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_log_stats(&self) -> Option<LogStats<'a>> {
    if self.container_type() == LoggerContainer::LogStats {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LogStats::init_from_table(t) }
     })
    } else {
      None
    }
  }

  #[inline]
  #[allow(non_snake_case)]
  pub fn container_as_large_file_event(&self) -> Option<LargeFileEvent<'a>> {
    if self.container_type() == LoggerContainer::LargeFileEvent {
      self.container().map(|t| {
       // Safety:
       // Created from a valid Table for this object
       // Which contains a valid union in this slot
       unsafe { LargeFileEvent::init_from_table(t) }
     })
    } else {
      None
    }
  }

}

impl flatbuffers::Verifiable for LoggerFlat<'_> {
  #[inline]
  fn run_verifier(
    v: &mut flatbuffers::Verifier, pos: usize
  ) -> Result<(), flatbuffers::InvalidFlatbuffer> {
    use self::flatbuffers::Verifiable;
    v.visit_table(pos)?
     .visit_union::<LoggerContainer, _>("container_type", Self::VT_CONTAINER_TYPE, "container", Self::VT_CONTAINER, false, |key, v, pos| {
        match key {
          LoggerContainer::BoolWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<BoolWrapper>>("LoggerContainer::BoolWrapper", pos),
          LoggerContainer::StrWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<StrWrapper>>("LoggerContainer::StrWrapper", pos),
          LoggerContainer::Int32Wrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<Int32Wrapper>>("LoggerContainer::Int32Wrapper", pos),
          LoggerContainer::NoneWrapper => v.verify_union_variant::<flatbuffers::ForwardsUOffset<NoneWrapper>>("LoggerContainer::NoneWrapper", pos),
          LoggerContainer::LogEntry => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LogEntry>>("LoggerContainer::LogEntry", pos),
          LoggerContainer::LoggerConfig => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LoggerConfig>>("LoggerContainer::LoggerConfig", pos),
          LoggerContainer::UploadProgress => v.verify_union_variant::<flatbuffers::ForwardsUOffset<UploadProgress>>("LoggerContainer::UploadProgress", pos),
          LoggerContainer::LogStats => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LogStats>>("LoggerContainer::LogStats", pos),
          LoggerContainer::LargeFileEvent => v.verify_union_variant::<flatbuffers::ForwardsUOffset<LargeFileEvent>>("LoggerContainer::LargeFileEvent", pos),
          _ => Ok(()),
        }
     })?
     .visit_field::<i32>("code", Self::VT_CODE, false)?
     .visit_field::<flatbuffers::ForwardsUOffset<&str>>("message", Self::VT_MESSAGE, false)?
     .visit_field::<bool>("success", Self::VT_SUCCESS, false)?
     .finish();
    Ok(())
  }
}
pub struct LoggerFlatArgs<'a> {
    pub container_type: LoggerContainer,
    pub container: Option<flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>>,
    pub code: i32,
    pub message: Option<flatbuffers::WIPOffset<&'a str>>,
    pub success: bool,
}
impl<'a> Default for LoggerFlatArgs<'a> {
  #[inline]
  fn default() -> Self {
    LoggerFlatArgs {
      container_type: LoggerContainer::NONE,
      container: None,
      code: 0,
      message: None,
      success: false,
    }
  }
}

pub struct LoggerFlatBuilder<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> {
  fbb_: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
  start_: flatbuffers::WIPOffset<flatbuffers::TableUnfinishedWIPOffset>,
}
impl<'a: 'b, 'b, A: flatbuffers::Allocator + 'a> LoggerFlatBuilder<'a, 'b, A> {
  #[inline]
  pub fn add_container_type(&mut self, container_type: LoggerContainer) {
    self.fbb_.push_slot::<LoggerContainer>(LoggerFlat::VT_CONTAINER_TYPE, container_type, LoggerContainer::NONE);
  }
  #[inline]
  pub fn add_container(&mut self, container: flatbuffers::WIPOffset<flatbuffers::UnionWIPOffset>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerFlat::VT_CONTAINER, container);
  }
  #[inline]
  pub fn add_code(&mut self, code: i32) {
    self.fbb_.push_slot::<i32>(LoggerFlat::VT_CODE, code, 0);
  }
  #[inline]
  pub fn add_message(&mut self, message: flatbuffers::WIPOffset<&'b  str>) {
    self.fbb_.push_slot_always::<flatbuffers::WIPOffset<_>>(LoggerFlat::VT_MESSAGE, message);
  }
  #[inline]
  pub fn add_success(&mut self, success: bool) {
    self.fbb_.push_slot::<bool>(LoggerFlat::VT_SUCCESS, success, false);
  }
  #[inline]
  pub fn new(_fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>) -> LoggerFlatBuilder<'a, 'b, A> {
    let start = _fbb.start_table();
    LoggerFlatBuilder {
      fbb_: _fbb,
      start_: start,
    }
  }
  #[inline]
  pub fn finish(self) -> flatbuffers::WIPOffset<LoggerFlat<'a>> {
    let o = self.fbb_.end_table(self.start_);
    flatbuffers::WIPOffset::new(o.value())
  }
}

impl core::fmt::Debug for LoggerFlat<'_> {
  fn fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result {
    let mut ds = f.debug_struct("LoggerFlat");
      ds.field("container_type", &self.container_type());
      match self.container_type() {
        LoggerContainer::BoolWrapper => {
          if let Some(x) = self.container_as_bool_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::StrWrapper => {
          if let Some(x) = self.container_as_str_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::Int32Wrapper => {
          if let Some(x) = self.container_as_int_32_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::NoneWrapper => {
          if let Some(x) = self.container_as_none_wrapper() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LogEntry => {
          if let Some(x) = self.container_as_log_entry() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LoggerConfig => {
          if let Some(x) = self.container_as_logger_config() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::UploadProgress => {
          if let Some(x) = self.container_as_upload_progress() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LogStats => {
          if let Some(x) = self.container_as_log_stats() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        LoggerContainer::LargeFileEvent => {
          if let Some(x) = self.container_as_large_file_event() {
            ds.field("container", &x)
          } else {
            ds.field("container", &"InvalidFlatbuffer: Union discriminant does not match value.")
          }
        },
        _ => {
          let x: Option<()> = None;
          ds.field("container", &x)
        },
      };
      ds.field("code", &self.code());
      ds.field("message", &self.message());
      ds.field("success", &self.success());
      ds.finish()
  }
}
#[inline]
/// Verifies that a buffer of bytes contains a `LoggerFlat`
/// and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_logger_flat_unchecked`.
pub fn root_as_logger_flat(buf: &[u8]) -> Result<LoggerFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root::<LoggerFlat>(buf)
}
#[inline]
/// Verifies that a buffer of bytes contains a size prefixed
/// `LoggerFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `size_prefixed_root_as_logger_flat_unchecked`.
pub fn size_prefixed_root_as_logger_flat(buf: &[u8]) -> Result<LoggerFlat, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root::<LoggerFlat>(buf)
}
#[inline]
/// Verifies, with the given options, that a buffer of bytes
/// contains a `LoggerFlat` and returns it.
/// Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_logger_flat_unchecked`.
pub fn root_as_logger_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<LoggerFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::root_with_opts::<LoggerFlat<'b>>(opts, buf)
}
#[inline]
/// Verifies, with the given verifier options, that a buffer of
/// bytes contains a size prefixed `LoggerFlat` and returns
/// it. Note that verification is still experimental and may not
/// catch every error, or be maximally performant. For the
/// previous, unchecked, behavior use
/// `root_as_logger_flat_unchecked`.
pub fn size_prefixed_root_as_logger_flat_with_opts<'b, 'o>(
  opts: &'o flatbuffers::VerifierOptions,
  buf: &'b [u8],
) -> Result<LoggerFlat<'b>, flatbuffers::InvalidFlatbuffer> {
  flatbuffers::size_prefixed_root_with_opts::<LoggerFlat<'b>>(opts, buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a LoggerFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid `LoggerFlat`.
pub unsafe fn root_as_logger_flat_unchecked(buf: &[u8]) -> LoggerFlat {
  flatbuffers::root_unchecked::<LoggerFlat>(buf)
}
#[inline]
/// Assumes, without verification, that a buffer of bytes contains a size prefixed LoggerFlat and returns it.
/// # Safety
/// Callers must trust the given bytes do indeed contain a valid size prefixed `LoggerFlat`.
pub unsafe fn size_prefixed_root_as_logger_flat_unchecked(buf: &[u8]) -> LoggerFlat {
  flatbuffers::size_prefixed_root_unchecked::<LoggerFlat>(buf)
}
#[inline]
pub fn finish_logger_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(
    fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>,
    root: flatbuffers::WIPOffset<LoggerFlat<'a>>) {
  fbb.finish(root, None);
}

#[inline]
pub fn finish_size_prefixed_logger_flat_buffer<'a, 'b, A: flatbuffers::Allocator + 'a>(fbb: &'b mut flatbuffers::FlatBufferBuilder<'a, A>, root: flatbuffers::WIPOffset<LoggerFlat<'a>>) {
  fbb.finish_size_prefixed(root, None);
}
}  // pub mod fbs
}  // pub mod logger
}  // pub mod rust
}  // pub mod uplus
}  // pub mod uhome
}  // pub mod haier
}  // pub mod com

