// This file is @generated by prost-build.
/// 日志元数据消息
/// 与Android版本的LogMetaData.proto完全一致
#[allow(clippy::derive_partial_eq_without_eq)]
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct LogMetaData {
    /// 时间戳
    #[prost(string, tag = "1")]
    pub time: ::prost::alloc::string::String,
    /// 会话ID（与Android字段名一致）
    #[prost(string, tag = "2")]
    pub session_id: ::prost::alloc::string::String,
    /// 日志标签
    #[prost(string, tag = "3")]
    pub tag: ::prost::alloc::string::String,
    /// 日志级别
    #[prost(string, tag = "4")]
    pub level: ::prost::alloc::string::String,
    /// 测试模式（与Android字段名一致）
    #[prost(string, tag = "5")]
    pub test_mode: ::prost::alloc::string::String,
    /// 用户ID（与Android字段名一致）
    #[prost(string, tag = "6")]
    pub user_id: ::prost::alloc::string::String,
    /// 日志消息（与Android字段名一致）
    #[prost(string, tag = "7")]
    pub log_message: ::prost::alloc::string::String,
}
