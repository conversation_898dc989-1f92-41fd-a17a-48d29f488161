// 纯Rust mmap日志存储实现
// 借鉴mars xlog的设计：150KB缓冲区，50KB触发flush

use memmap2::MmapMut;
use std::fs::{File, OpenOptions};
use std::io::Write;
use std::path::{Path, PathBuf};
use chrono::{DateTime, Local};
use parking_lot::Mutex;
use std::sync::Arc;

/// mmap存储引擎的错误类型
#[derive(Debug, thiserror::Error)]
pub enum MmapStorageError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Storage is closed")]
    Closed,
    #[error("Buffer overflow: tried to write {size} bytes, but only {available} available")]
    BufferOverflow { size: usize, available: usize },
    #[error("Invalid path: {0}")]
    InvalidPath(String),
}

pub type Result<T> = std::result::Result<T, MmapStorageError>;

/// mmap日志存储引擎
/// 借鉴xlog设计：150KB mmap缓冲区，50KB触发flush
pub struct MmapLogStorage {
    /// mmap缓冲区（150KB）- varint前缀 + protobuf，与Android格式完全一致
    mmap_buffer: MmapMut,
    /// 当前mmap缓冲区使用大小
    mmap_used: usize,
    /// mmap文件句柄（必须保持生命周期，确保mmap映射有效）
    #[allow(dead_code)]
    mmap_file: File,

    /// 配置参数
    config: StorageConfig,

    /// 状态管理
    is_closed: bool,

    /// 当前日志文件
    current_log_file: Option<File>,
    current_log_path: Option<PathBuf>,
}

/// 存储配置
#[derive(Debug, Clone)]
pub struct StorageConfig {
    /// 日志目录
    pub log_dir: PathBuf,
    /// 缓存目录（存放mmap文件）
    pub cache_dir: PathBuf,
    /// 文件名前缀
    pub name_prefix: String,
    /// 最大缓冲区大小（150KB）
    pub max_buffer_size: usize,
    /// flush阈值（50KB = 150KB * 1/3）
    pub flush_threshold: usize,
    /// 单个日志文件最大大小
    pub max_file_size: u64,
}

impl StorageConfig {
    /// 创建默认配置
    pub fn new(log_dir: impl AsRef<Path>, name_prefix: &str) -> Result<Self> {
        let log_dir = log_dir.as_ref().to_path_buf();
        let cache_dir = log_dir.join("cache");
        
        // 创建目录
        std::fs::create_dir_all(&log_dir)?;
        std::fs::create_dir_all(&cache_dir)?;
        
        Ok(Self {
            log_dir,
            cache_dir,
            name_prefix: name_prefix.to_string(),
            max_buffer_size: Self::BUFFER_BLOCK_LENGTH,
            flush_threshold: Self::FLUSH_THRESHOLD,
            max_file_size: 10 * 1024 * 1024, // 10MB默认
        })
    }
    
    /// 借鉴xlog的常量定义
    const BUFFER_BLOCK_LENGTH: usize = 150 * 1024;  // 150KB
    const FLUSH_THRESHOLD: usize = Self::BUFFER_BLOCK_LENGTH / 3;  // 50KB
}

impl MmapLogStorage {
    /// 创建新的mmap存储实例
    pub fn new(config: StorageConfig) -> Result<Self> {
        // 创建mmap文件路径：{cache_dir}/{name_prefix}.mmap3
        let mmap_path = config.cache_dir.join(format!("{}.mmap3", config.name_prefix));

        // 检查是否是新创建的文件
        let is_new_file = !mmap_path.exists();

        // 创建或打开mmap文件
        let mmap_file = OpenOptions::new()
            .create(true)
            .read(true)
            .write(true)
            .open(&mmap_path)?;

        // 设置文件大小为150KB
        mmap_file.set_len(config.max_buffer_size as u64)?;

        // 创建内存映射
        let mmap_buffer = unsafe { MmapMut::map_mut(&mmap_file)? };

        // 检测已有数据的大小（借鉴xlog的恢复逻辑）
        let buffer_used = if is_new_file {
            0
        } else {
            Self::detect_existing_data_size(&mmap_buffer, config.max_buffer_size)
        };

        crate::internal_info!("MMAP", "Created mmap storage: buffer_size={}KB, flush_threshold={}KB, mmap_file={:?}, existing_data={}bytes, is_new_file={}",
                             config.max_buffer_size / 1024,
                             config.flush_threshold / 1024,
                             mmap_path,
                             buffer_used,
                             is_new_file);

        let mut storage = Self {
            mmap_buffer,
            mmap_used: buffer_used,
            mmap_file,
            config,
            is_closed: false,
            current_log_file: None,
            current_log_path: None,
        };

        // 如果有未flush的数据，立即flush到log文件（借鉴xlog启动恢复逻辑）
        if buffer_used > 0 {
            crate::internal_info!("MMAP", "Found {} bytes of unflushed data in mmap, flushing to log file", buffer_used);
            storage.flush_to_file()?;
        }

        Ok(storage)
    }

    /// 检测mmap文件中已有数据的大小
    /// 使用varint长度前缀来准确定位数据边界（与Android兼容）
    fn detect_existing_data_size(mmap_buffer: &[u8], max_size: usize) -> usize {
        let mut pos = 0;

        // 逐个读取数据块，每个数据块前面是varint编码的长度
        while pos < max_size {
            // 解码varint长度前缀
            let (data_length, varint_len) = match Self::decode_varint(&mmap_buffer[pos..max_size]) {
                Some((len, varint_bytes)) => (len as usize, varint_bytes),
                None => {
                    // 无法解码varint，可能到达数据末尾或数据损坏
                    break;
                }
            };

            // 检查长度是否合理
            if data_length == 0 {
                // 遇到长度为0，说明到达数据末尾
                break;
            }

            if data_length > max_size || pos + varint_len + data_length > max_size {
                // 长度不合理，可能是损坏的数据，从这里截断
                crate::internal_warn!("MMAP", "Detected corrupted data at position {}, varint_len={}, data_length={}", pos, varint_len, data_length);
                break;
            }

            // 跳过varint长度前缀和数据内容
            pos += varint_len + data_length;
        }

        crate::internal_info!("MMAP", "Detected existing data size: {} bytes", pos);
        pos
    }

    /// 解码varint（与protobuf兼容）
    fn decode_varint(data: &[u8]) -> Option<(u64, usize)> {
        let mut result = 0u64;
        let mut shift = 0;
        let mut pos = 0;

        while pos < data.len() && pos < 10 { // varint最多10字节
            let byte = data[pos];
            result |= ((byte & 0x7F) as u64) << shift;
            pos += 1;

            if byte & 0x80 == 0 {
                // 最后一个字节
                return Some((result, pos));
            }

            shift += 7;
        }

        None // 解码失败
    }

    /// 高性能写入protobuf数据（单缓冲区设计，与Android格式完全一致）
    /// mmap缓冲区：varint长度前缀 + protobuf数据，可直接flush到文件
    pub fn write_protobuf(&mut self, data: &[u8]) -> Result<()> {
        if self.is_closed {
            return Err(MmapStorageError::Closed);
        }

        // 计算需要的空间：varint长度前缀 + protobuf数据
        let mut len_buf = Vec::new();
        prost::encoding::encode_varint(data.len() as u64, &mut len_buf);
        let total_size = len_buf.len() + data.len();

        // 检查单条日志是否过大
        if total_size > self.config.max_buffer_size {
            return Err(MmapStorageError::BufferOverflow {
                size: total_size,
                available: self.config.max_buffer_size,
            });
        }

        // 检查缓冲区空间
        if self.mmap_used + total_size > self.config.max_buffer_size {
            // 缓冲区满了，先flush
            self.flush_to_file()?;
        }

        // 写入mmap缓冲区（varint前缀 + protobuf，与Android格式完全一致）
        let start = self.mmap_used;

        // 写入varint长度前缀
        self.mmap_buffer[start..start + len_buf.len()].copy_from_slice(&len_buf);

        // 写入protobuf数据
        let data_start = start + len_buf.len();
        self.mmap_buffer[data_start..data_start + data.len()].copy_from_slice(data);

        self.mmap_used = data_start + data.len();

        crate::internal_debug!("MMAP", "Written {} bytes (varint:{} + data:{}) to mmap buffer, total used: {}/{} bytes",
                              total_size, len_buf.len(), data.len(), self.mmap_used, self.config.max_buffer_size);

        Ok(())
    }
    
    /// 检查是否需要flush
    /// 借鉴xlog的flush条件：缓冲区达到1/3时触发
    pub fn should_flush(&self) -> bool {
        self.mmap_used >= self.config.flush_threshold
    }

    /// 高性能flush到文件（直接从mmap缓冲区写入，与Android格式完全一致）
    pub fn flush_to_file(&mut self) -> Result<()> {
        if self.mmap_used == 0 {
            crate::internal_debug!("MMAP", "No data to flush");
            return Ok(());
        }

        // 确保有当前日志文件
        self.ensure_current_log_file()?;

        // 直接写入mmap缓冲区数据（varint前缀 + protobuf，与Android格式完全一致）
        if let Some(ref mut file) = self.current_log_file {
            file.write_all(&self.mmap_buffer[..self.mmap_used])?;
            file.sync_all()?;

            crate::internal_info!("MMAP", "Flushed {} bytes Android-compatible data to log file: {:?}",
                                 self.mmap_used, self.current_log_path);
        }

        // 清空缓冲区
        self.clear_buffers();

        // 检查文件大小，必要时轮转
        self.rotate_if_needed()?;

        Ok(())
    }

    /// 清空缓冲区
    fn clear_buffers(&mut self) {
        // 清空mmap缓冲区：将已使用的区域清零，确保下次启动时能正确检测数据边界
        for i in 0..self.mmap_used {
            self.mmap_buffer[i] = 0;
        }
        self.mmap_used = 0;
    }
    
    /// 确保有当前的日志文件（遵循Android逻辑）
    /// Android逻辑：重启后继续写入现有文件，直到达到大小限制
    fn ensure_current_log_file(&mut self) -> Result<()> {
        if self.current_log_file.is_some() {
            return Ok(()); // 已经有文件了
        }

        // 查找现有文件（遵循Android逻辑）
        let target_file_path = self.find_target_log_file()?;

        // 打开文件（追加模式）
        let file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&target_file_path)?;

        crate::internal_info!("MMAP", "Using log file: {:?}", target_file_path);

        self.current_log_file = Some(file);
        self.current_log_path = Some(target_file_path);

        Ok(())
    }

    /// 查找目标日志文件（遵循Android逻辑）
    /// 1. 如果目录下没有文件 → 创建新文件
    /// 2. 如果只有一个文件 → 继续使用这个文件
    /// 3. 如果有多个文件 → 找到修改时间最新的文件继续使用
    fn find_target_log_file(&self) -> Result<PathBuf> {
        use std::fs;

        // 确保日志目录存在
        if !self.config.log_dir.exists() {
            fs::create_dir_all(&self.config.log_dir)?;
        }

        // 获取目录下所有.log文件
        let mut log_files = Vec::new();
        if let Ok(entries) = fs::read_dir(&self.config.log_dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.is_file() && path.extension().map_or(false, |ext| ext == "log") {
                    log_files.push(path);
                }
            }
        }

        match log_files.len() {
            0 => {
                // 没有文件，创建新文件
                let now = Local::now();
                let new_path = self.generate_log_file_path(&now);
                crate::internal_info!("MMAP", "No existing log files, creating new: {:?}", new_path);
                Ok(new_path)
            }
            1 => {
                // 只有一个文件，继续使用（检查大小限制）
                let file_path = &log_files[0];
                if let Ok(metadata) = fs::metadata(file_path) {
                    if metadata.len() >= self.config.max_file_size {
                        // 文件已满，创建新文件
                        let now = Local::now();
                        let new_path = self.generate_log_file_path(&now);
                        crate::internal_info!("MMAP", "Existing file full ({} bytes), creating new: {:?}",
                                             metadata.len(), new_path);
                        Ok(new_path)
                    } else {
                        // 继续使用现有文件
                        crate::internal_info!("MMAP", "Continuing with existing file: {:?} ({} bytes)",
                                             file_path, metadata.len());
                        Ok(file_path.clone())
                    }
                } else {
                    // 无法获取文件信息，继续使用
                    Ok(file_path.clone())
                }
            }
            _ => {
                // 多个文件，找到修改时间最新的文件
                let mut files_with_time = Vec::new();
                for file_path in log_files {
                    if let Ok(metadata) = fs::metadata(&file_path) {
                        if let Ok(modified) = metadata.modified() {
                            files_with_time.push((file_path, modified, metadata.len()));
                        }
                    }
                }

                if files_with_time.is_empty() {
                    // 无法获取文件信息，创建新文件
                    let now = Local::now();
                    Ok(self.generate_log_file_path(&now))
                } else {
                    // 按修改时间排序，选择最新的
                    files_with_time.sort_by(|a, b| b.1.cmp(&a.1)); // 降序排列
                    let (latest_file, _, file_size) = &files_with_time[0];

                    if *file_size >= self.config.max_file_size {
                        // 最新文件已满，创建新文件
                        let now = Local::now();
                        let new_path = self.generate_log_file_path(&now);
                        crate::internal_info!("MMAP", "Latest file full ({} bytes), creating new: {:?}",
                                             file_size, new_path);
                        Ok(new_path)
                    } else {
                        // 继续使用最新文件
                        crate::internal_info!("MMAP", "Continuing with latest file: {:?} ({} bytes)",
                                             latest_file, file_size);
                        Ok(latest_file.clone())
                    }
                }
            }
        }
    }
    
    /// 生成日志文件路径（与Android格式一致）
    /// 格式：{log_dir}/YYYY-MM-DD-HH_MM_SS.microseconds+timezone.log
    fn generate_log_file_path(&self, datetime: &DateTime<Local>) -> PathBuf {
        // 与Android格式完全一致：2025-06-23-16_26_04.435000+0800.log
        let filename = format!("{}.log",
            datetime.format("%Y-%m-%d-%H_%M_%S%.6f%z")
        );
        self.config.log_dir.join(filename)
    }
    
    /// 检查文件大小，必要时轮转
    fn rotate_if_needed(&mut self) -> Result<()> {
        if let Some(ref path) = self.current_log_path {
            if let Ok(metadata) = std::fs::metadata(path) {
                if metadata.len() > self.config.max_file_size {
                    crate::internal_info!("MMAP", "Log file size ({} bytes) exceeds limit ({} bytes), rotating",
                                         metadata.len(), self.config.max_file_size);

                    // 关闭当前文件
                    if let Some(file) = self.current_log_file.take() {
                        drop(file);
                    }

                    // 生成新的文件名（与Android格式一致）
                    let now = Local::now();
                    let new_filename = format!("{}.log",
                        now.format("%Y-%m-%d-%H_%M_%S%.6f%z")
                    );
                    let new_path = self.config.log_dir.join(new_filename);

                    // 创建新文件
                    let file = OpenOptions::new()
                        .create(true)
                        .append(true)
                        .open(&new_path)?;

                    crate::internal_info!("MMAP", "Rotated to new log file: {:?}", new_path);

                    self.current_log_file = Some(file);
                    self.current_log_path = Some(new_path);
                }
            }
        }
        Ok(())
    }
    
    /// 关闭存储
    pub fn close(&mut self) -> Result<()> {
        if !self.is_closed {
            // flush剩余数据
            self.flush_to_file()?;
            
            // 关闭文件
            if let Some(file) = self.current_log_file.take() {
                drop(file);
            }
            
            self.is_closed = true;
            crate::internal_info!("MMAP", "Mmap storage closed");
        }
        Ok(())
    }
    
    /// 获取当前缓冲区使用情况
    pub fn buffer_usage(&self) -> (usize, usize) {
        (self.mmap_used, self.config.max_buffer_size)
    }
}

/// 崩溃时自动flush
impl Drop for MmapLogStorage {
    fn drop(&mut self) {
        if !self.is_closed && self.mmap_used > 0 {
            crate::internal_warn!("MMAP", "Emergency flush on drop: {} bytes", self.mmap_used);
            let _ = self.flush_to_file();
        }
    }
}

/// 线程安全的mmap存储包装器
pub struct ThreadSafeMmapStorage {
    inner: Arc<Mutex<MmapLogStorage>>,
}

impl ThreadSafeMmapStorage {
    pub fn new(config: StorageConfig) -> Result<Self> {
        let storage = MmapLogStorage::new(config)?;
        Ok(Self {
            inner: Arc::new(Mutex::new(storage)),
        })
    }
    
    pub fn write_protobuf(&self, data: &[u8]) -> Result<()> {
        let mut storage = self.inner.lock();
        storage.write_protobuf(data)?;

        // 检查是否需要自动flush（借鉴xlog的50KB阈值）
        if storage.should_flush() {
            storage.flush_to_file()?;
        }

        Ok(())
    }
    
    pub fn should_flush(&self) -> bool {
        let storage = self.inner.lock();
        storage.should_flush()
    }
    
    pub fn flush_to_file(&self) -> Result<()> {
        let mut storage = self.inner.lock();
        storage.flush_to_file()
    }
    
    pub fn close(&self) -> Result<()> {
        let mut storage = self.inner.lock();
        storage.close()
    }
    
    pub fn buffer_usage(&self) -> (usize, usize) {
        let storage = self.inner.lock();
        storage.buffer_usage()
    }
}

impl Clone for ThreadSafeMmapStorage {
    fn clone(&self) -> Self {
        Self {
            inner: Arc::clone(&self.inner),
        }
    }
}

/// 应用退出时自动flush
impl Drop for ThreadSafeMmapStorage {
    fn drop(&mut self) {
        // 尝试获取锁并flush数据
        if let Some(mut storage) = self.inner.try_lock() {
            if !storage.is_closed && storage.mmap_used > 0 {
                crate::internal_warn!("MMAP", "ThreadSafeMmapStorage drop: flushing {} bytes", storage.mmap_used);
                let _ = storage.flush_to_file();
            }
            let _ = storage.close();
        } else {
            crate::internal_warn!("MMAP", "ThreadSafeMmapStorage drop: could not acquire lock for final flush");
        }
    }
}
