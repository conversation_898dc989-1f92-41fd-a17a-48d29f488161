// 测试mmap存储功能的集成测试

use rust_logger::{
    config::LoggerConfig,
    storage::LogStorage,
    mmap_storage::{StorageConfig, ThreadSafeMmapStorage, MmapLogStorage},
};
use std::path::PathBuf;

fn create_temp_dir() -> PathBuf {
    let temp_dir = std::env::temp_dir().join(format!("rust_logger_test_{}",
        std::process::id()));
    std::fs::create_dir_all(&temp_dir).unwrap();
    temp_dir
}

fn create_test_config() -> LoggerConfig {
    let mut config = LoggerConfig::default();
    config.log_directory = "/tmp/test_logs".to_string();
    config.log_file_prefix = "test_log".to_string();
    config.max_file_size = 1024 * 1024; // 1MB
    config.max_directory_size = 10 * 1024 * 1024; // 10MB
    config.enable_console_output = true;
    config.enable_full_log = true;
    config.log_level = rust_logger::core::LogLevel::Debug;
    config.app_version = Some("1.0.0".to_string());
    config.device_id = Some("test_device".to_string());
    config.user_id = "test_user".to_string();
    config
}

#[test]
fn test_mmap_storage_creation() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 检查缓冲区使用情况
    let (used, total) = storage.buffer_usage();
    assert_eq!(used, 0);
    assert_eq!(total, 150 * 1024); // 150KB
}

#[test]
fn test_mmap_storage_write() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 写入一些测试数据
    let test_data = b"Hello, mmap storage!";
    storage.write_protobuf(test_data).unwrap();
    
    // 检查缓冲区使用情况
    let (used, _) = storage.buffer_usage();
    assert_eq!(used, test_data.len());
}

#[test]
fn test_mmap_storage_flush() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 写入测试数据
    let test_data = b"Test flush functionality";
    storage.write_protobuf(test_data).unwrap();

    // 手动flush
    storage.flush_to_file().unwrap();

    // 检查缓冲区已清空
    let (used, _) = storage.buffer_usage();
    assert_eq!(used, 0);

    // 检查文件是否创建
    let now = chrono::Local::now();
    let expected_filename = format!("test_log_{}.log", now.format("%Y%m%d"));
    let expected_path = log_dir.join(expected_filename);
    assert!(expected_path.exists());

    // 检查文件内容（protobuf二进制数据）
    let file_content = std::fs::read(&expected_path).unwrap();
    assert!(!file_content.is_empty());
    assert!(file_content.len() >= test_data.len());
}

#[test]
fn test_mmap_storage_auto_flush() {
    let log_dir = create_temp_dir();
    
    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();
    
    // 写入大量数据触发自动flush（超过50KB阈值）
    let large_data = vec![b'A'; 60 * 1024]; // 60KB
    storage.write_protobuf(&large_data).unwrap();
    
    // 应该已经自动flush了
    let (used, _) = storage.buffer_usage();
    assert_eq!(used, 0);
    
    // 检查文件是否创建
    let now = chrono::Local::now();
    let expected_filename = format!("test_log_{}.log", now.format("%Y%m%d"));
    let expected_path = log_dir.join(expected_filename);
    assert!(expected_path.exists());
}

#[test]
fn test_log_storage_integration() {
    let log_dir = create_temp_dir();
    let mut config = create_test_config();
    config.log_directory = log_dir.to_str().unwrap().to_string();
    
    let mut storage = LogStorage::new();
    storage.initialize(&config).unwrap();
    
    // 写入protobuf日志
    storage.write_log_protobuf("INFO", "TestTag", "Test message", &config).unwrap();
    
    // 手动flush
    storage.flush().unwrap();
    
    // 检查日志文件是否创建
    let log_files = storage.get_all_log_files().unwrap();
    assert!(!log_files.is_empty());
    
    // 检查文件扩展名是.log
    let log_file = &log_files[0];
    assert!(log_file.extension().unwrap() == "log");
}

#[test]
fn test_should_flush_threshold() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 写入接近阈值的数据（49KB）
    let data_49kb = vec![b'B'; 49 * 1024];
    storage.write_protobuf(&data_49kb).unwrap();

    // 应该还不需要flush
    assert!(!storage.should_flush());

    // 检查缓冲区使用情况
    let (used, _total) = storage.buffer_usage();
    assert_eq!(used, 49 * 1024);

    // 检查如果再加2KB是否会超过阈值
    let would_exceed = used + 2 * 1024 > 50 * 1024;
    assert!(would_exceed);
}

#[test]
fn test_multiple_writes_and_flushes() {
    let log_dir = create_temp_dir();

    let config = StorageConfig::new(&log_dir, "test_log").unwrap();
    let storage = ThreadSafeMmapStorage::new(config).unwrap();

    // 多次写入和flush
    let mut expected_content = Vec::new();
    for i in 0..5 {
        let test_data = format!("Test message {}", i);
        expected_content.extend_from_slice(test_data.as_bytes());
        storage.write_protobuf(test_data.as_bytes()).unwrap();
        storage.flush_to_file().unwrap();
    }

    // 检查文件内容
    let now = chrono::Local::now();
    let expected_filename = format!("test_log_{}.log", now.format("%Y%m%d"));
    let expected_path = log_dir.join(expected_filename);
    assert!(expected_path.exists());

    // 检查文件内容（二进制数据）
    let file_content = std::fs::read(&expected_path).unwrap();
    assert!(!file_content.is_empty());
    assert!(file_content.len() >= expected_content.len());
    // 简单检查：文件包含了我们写入的数据
    assert!(file_content.len() > 0);
}

#[test]
fn test_app_restart_recovery() {
    let log_dir = create_temp_dir();

    // 第一次运行：写入数据但不flush
    {
        let config = StorageConfig::new(&log_dir, "test_recovery").unwrap();
        let mut storage = MmapLogStorage::new(config).unwrap();

        // 写入一些数据但不flush（模拟应用异常退出）
        let data1 = b"First log entry";
        let data2 = b"Second log entry";
        storage.write_protobuf(data1).unwrap();
        storage.write_protobuf(data2).unwrap();

        // 验证数据在缓冲区中
        let (used, _) = storage.buffer_usage();
        assert!(used > 0);

        // 不调用flush，直接drop（模拟应用退出时的Drop行为）
    }

    // 第二次运行：应该自动恢复mmap中的数据
    {
        let config = StorageConfig::new(&log_dir, "test_recovery").unwrap();
        let storage = MmapLogStorage::new(config).unwrap();

        // 新创建的storage应该已经flush了之前的数据
        let (used, _) = storage.buffer_usage();
        assert_eq!(used, 0, "Buffer should be empty after recovery flush");

        // 检查log文件是否被创建
        let now = chrono::Local::now();
        let expected_filename = format!("test_recovery_{}.log", now.format("%Y%m%d"));
        let expected_path = log_dir.join(expected_filename);
        assert!(expected_path.exists(), "Log file should exist after recovery");

        // 检查文件内容
        let file_content = std::fs::read(&expected_path).unwrap();
        assert!(!file_content.is_empty(), "Log file should contain recovered data");
    }
}

#[test]
fn test_threadsafe_storage_drop() {
    let log_dir = create_temp_dir();

    // 创建ThreadSafeMmapStorage并写入数据
    {
        let config = StorageConfig::new(&log_dir, "test_drop").unwrap();
        let storage = ThreadSafeMmapStorage::new(config).unwrap();

        // 写入数据但不手动flush
        let data = b"Data that should be flushed on drop";
        storage.write_protobuf(data).unwrap();

        // 验证数据在缓冲区中
        let (used, _) = storage.buffer_usage();
        assert!(used > 0);

        // 当storage被drop时，应该自动flush
    }

    // 检查文件是否被创建
    let now = chrono::Local::now();
    let expected_filename = format!("test_drop_{}.log", now.format("%Y%m%d"));
    let expected_path = log_dir.join(expected_filename);
    assert!(expected_path.exists(), "Log file should exist after ThreadSafeMmapStorage drop");

    // 检查文件内容
    let file_content = std::fs::read(&expected_path).unwrap();
    assert!(!file_content.is_empty(), "Log file should contain data flushed on drop");
}
