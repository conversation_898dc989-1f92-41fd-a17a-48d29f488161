// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { LogLevel } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/log-level.js';


export class LoggerConfig {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LoggerConfig {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLoggerConfig(bb:flatbuffers.ByteBuffer, obj?:LoggerConfig):LoggerConfig {
  return (obj || new LoggerConfig()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLoggerConfig(bb:flatbuffers.ByteBuffer, obj?:LoggerConfig):LoggerConfig {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LoggerConfig()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

logLevel():LogLevel {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readInt8(this.bb_pos + offset) : LogLevel.DEBUG;
}

enableConsoleOutput():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

enableFileOutput():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

enableFullLog():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

testMode():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

logEnv():string|null
logEnv(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
logEnv(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 14);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

disableSensitiveWords():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 16);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

userId():string|null
userId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
userId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 18);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

deviceId():string|null
deviceId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
deviceId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 20);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

sessionId():string|null
sessionId(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
sessionId(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 22);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

appVersion():string|null
appVersion(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
appVersion(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 24);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

privacyAgreed():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 26);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

isDebugMode():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 28);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

maxFileSize():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 30);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

maxDirectorySize():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 32);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

logFilePrefix():string|null
logFilePrefix(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
logFilePrefix(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 34);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

logDirectory():string|null
logDirectory(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
logDirectory(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 36);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

uploadUrl():string|null
uploadUrl(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
uploadUrl(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 38);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

maxRetryCount():number {
  const offset = this.bb!.__offset(this.bb_pos, 40);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

uploadTimeoutSeconds():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 42);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

maxZipFileSize():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 44);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

maxLogLength():number {
  const offset = this.bb!.__offset(this.bb_pos, 46);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

maxLogsPerSecond():number {
  const offset = this.bb!.__offset(this.bb_pos, 48);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

versionName():string|null
versionName(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
versionName(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 50);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

static startLoggerConfig(builder:flatbuffers.Builder) {
  builder.startObject(24);
}

static addLogLevel(builder:flatbuffers.Builder, logLevel:LogLevel) {
  builder.addFieldInt8(0, logLevel, LogLevel.DEBUG);
}

static addEnableConsoleOutput(builder:flatbuffers.Builder, enableConsoleOutput:boolean) {
  builder.addFieldInt8(1, +enableConsoleOutput, +false);
}

static addEnableFileOutput(builder:flatbuffers.Builder, enableFileOutput:boolean) {
  builder.addFieldInt8(2, +enableFileOutput, +false);
}

static addEnableFullLog(builder:flatbuffers.Builder, enableFullLog:boolean) {
  builder.addFieldInt8(3, +enableFullLog, +false);
}

static addTestMode(builder:flatbuffers.Builder, testMode:boolean) {
  builder.addFieldInt8(4, +testMode, +false);
}

static addLogEnv(builder:flatbuffers.Builder, logEnvOffset:flatbuffers.Offset) {
  builder.addFieldOffset(5, logEnvOffset, 0);
}

static addDisableSensitiveWords(builder:flatbuffers.Builder, disableSensitiveWords:boolean) {
  builder.addFieldInt8(6, +disableSensitiveWords, +false);
}

static addUserId(builder:flatbuffers.Builder, userIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(7, userIdOffset, 0);
}

static addDeviceId(builder:flatbuffers.Builder, deviceIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(8, deviceIdOffset, 0);
}

static addSessionId(builder:flatbuffers.Builder, sessionIdOffset:flatbuffers.Offset) {
  builder.addFieldOffset(9, sessionIdOffset, 0);
}

static addAppVersion(builder:flatbuffers.Builder, appVersionOffset:flatbuffers.Offset) {
  builder.addFieldOffset(10, appVersionOffset, 0);
}

static addPrivacyAgreed(builder:flatbuffers.Builder, privacyAgreed:boolean) {
  builder.addFieldInt8(11, +privacyAgreed, +false);
}

static addIsDebugMode(builder:flatbuffers.Builder, isDebugMode:boolean) {
  builder.addFieldInt8(12, +isDebugMode, +false);
}

static addMaxFileSize(builder:flatbuffers.Builder, maxFileSize:bigint) {
  builder.addFieldInt64(13, maxFileSize, BigInt('0'));
}

static addMaxDirectorySize(builder:flatbuffers.Builder, maxDirectorySize:bigint) {
  builder.addFieldInt64(14, maxDirectorySize, BigInt('0'));
}

static addLogFilePrefix(builder:flatbuffers.Builder, logFilePrefixOffset:flatbuffers.Offset) {
  builder.addFieldOffset(15, logFilePrefixOffset, 0);
}

static addLogDirectory(builder:flatbuffers.Builder, logDirectoryOffset:flatbuffers.Offset) {
  builder.addFieldOffset(16, logDirectoryOffset, 0);
}

static addUploadUrl(builder:flatbuffers.Builder, uploadUrlOffset:flatbuffers.Offset) {
  builder.addFieldOffset(17, uploadUrlOffset, 0);
}

static addMaxRetryCount(builder:flatbuffers.Builder, maxRetryCount:number) {
  builder.addFieldInt32(18, maxRetryCount, 0);
}

static addUploadTimeoutSeconds(builder:flatbuffers.Builder, uploadTimeoutSeconds:bigint) {
  builder.addFieldInt64(19, uploadTimeoutSeconds, BigInt('0'));
}

static addMaxZipFileSize(builder:flatbuffers.Builder, maxZipFileSize:bigint) {
  builder.addFieldInt64(20, maxZipFileSize, BigInt('0'));
}

static addMaxLogLength(builder:flatbuffers.Builder, maxLogLength:number) {
  builder.addFieldInt32(21, maxLogLength, 0);
}

static addMaxLogsPerSecond(builder:flatbuffers.Builder, maxLogsPerSecond:number) {
  builder.addFieldInt32(22, maxLogsPerSecond, 0);
}

static addVersionName(builder:flatbuffers.Builder, versionNameOffset:flatbuffers.Offset) {
  builder.addFieldOffset(23, versionNameOffset, 0);
}

static endLoggerConfig(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  builder.requiredField(offset, 36) // log_directory
  return offset;
}

static createLoggerConfig(builder:flatbuffers.Builder, logLevel:LogLevel, enableConsoleOutput:boolean, enableFileOutput:boolean, enableFullLog:boolean, testMode:boolean, logEnvOffset:flatbuffers.Offset, disableSensitiveWords:boolean, userIdOffset:flatbuffers.Offset, deviceIdOffset:flatbuffers.Offset, sessionIdOffset:flatbuffers.Offset, appVersionOffset:flatbuffers.Offset, privacyAgreed:boolean, isDebugMode:boolean, maxFileSize:bigint, maxDirectorySize:bigint, logFilePrefixOffset:flatbuffers.Offset, logDirectoryOffset:flatbuffers.Offset, uploadUrlOffset:flatbuffers.Offset, maxRetryCount:number, uploadTimeoutSeconds:bigint, maxZipFileSize:bigint, maxLogLength:number, maxLogsPerSecond:number, versionNameOffset:flatbuffers.Offset):flatbuffers.Offset {
  LoggerConfig.startLoggerConfig(builder);
  LoggerConfig.addLogLevel(builder, logLevel);
  LoggerConfig.addEnableConsoleOutput(builder, enableConsoleOutput);
  LoggerConfig.addEnableFileOutput(builder, enableFileOutput);
  LoggerConfig.addEnableFullLog(builder, enableFullLog);
  LoggerConfig.addTestMode(builder, testMode);
  LoggerConfig.addLogEnv(builder, logEnvOffset);
  LoggerConfig.addDisableSensitiveWords(builder, disableSensitiveWords);
  LoggerConfig.addUserId(builder, userIdOffset);
  LoggerConfig.addDeviceId(builder, deviceIdOffset);
  LoggerConfig.addSessionId(builder, sessionIdOffset);
  LoggerConfig.addAppVersion(builder, appVersionOffset);
  LoggerConfig.addPrivacyAgreed(builder, privacyAgreed);
  LoggerConfig.addIsDebugMode(builder, isDebugMode);
  LoggerConfig.addMaxFileSize(builder, maxFileSize);
  LoggerConfig.addMaxDirectorySize(builder, maxDirectorySize);
  LoggerConfig.addLogFilePrefix(builder, logFilePrefixOffset);
  LoggerConfig.addLogDirectory(builder, logDirectoryOffset);
  LoggerConfig.addUploadUrl(builder, uploadUrlOffset);
  LoggerConfig.addMaxRetryCount(builder, maxRetryCount);
  LoggerConfig.addUploadTimeoutSeconds(builder, uploadTimeoutSeconds);
  LoggerConfig.addMaxZipFileSize(builder, maxZipFileSize);
  LoggerConfig.addMaxLogLength(builder, maxLogLength);
  LoggerConfig.addMaxLogsPerSecond(builder, maxLogsPerSecond);
  LoggerConfig.addVersionName(builder, versionNameOffset);
  return LoggerConfig.endLoggerConfig(builder);
}
}
