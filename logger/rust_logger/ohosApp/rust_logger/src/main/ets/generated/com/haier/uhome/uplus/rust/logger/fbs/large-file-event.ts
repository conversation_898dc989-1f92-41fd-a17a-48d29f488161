// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

export class LargeFileEvent {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LargeFileEvent {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLargeFileEvent(bb:flatbuffers.ByteBuffer, obj?:LargeFileEvent):LargeFileEvent {
  return (obj || new LargeFileEvent()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLargeFileEvent(bb:flatbuffers.ByteBuffer, obj?:LargeFileEvent):LargeFileEvent {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LargeFileEvent()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

eventType():string|null
eventType(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
eventType(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

filePath():string|null
filePath(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
filePath(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

fileSize():bigint {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.readInt64(this.bb_pos + offset) : BigInt('0');
}

static startLargeFileEvent(builder:flatbuffers.Builder) {
  builder.startObject(3);
}

static addEventType(builder:flatbuffers.Builder, eventTypeOffset:flatbuffers.Offset) {
  builder.addFieldOffset(0, eventTypeOffset, 0);
}

static addFilePath(builder:flatbuffers.Builder, filePathOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, filePathOffset, 0);
}

static addFileSize(builder:flatbuffers.Builder, fileSize:bigint) {
  builder.addFieldInt64(2, fileSize, BigInt('0'));
}

static endLargeFileEvent(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static createLargeFileEvent(builder:flatbuffers.Builder, eventTypeOffset:flatbuffers.Offset, filePathOffset:flatbuffers.Offset, fileSize:bigint):flatbuffers.Offset {
  LargeFileEvent.startLargeFileEvent(builder);
  LargeFileEvent.addEventType(builder, eventTypeOffset);
  LargeFileEvent.addFilePath(builder, filePathOffset);
  LargeFileEvent.addFileSize(builder, fileSize);
  return LargeFileEvent.endLargeFileEvent(builder);
}
}
