// automatically generated by the FlatBuffers compiler, do not modify

/* eslint-disable @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any, @typescript-eslint/no-non-null-assertion */

import * as flatbuffers from 'flatbuffers';

import { LoggerContainer, unionToLoggerContainer, unionListToLoggerContainer } from '../../../../../../../com/haier/uhome/uplus/rust/logger/fbs/logger-container.js';


export class LoggerFlat {
  bb: flatbuffers.ByteBuffer|null = null;
  bb_pos = 0;
  __init(i:number, bb:flatbuffers.ByteBuffer):LoggerFlat {
  this.bb_pos = i;
  this.bb = bb;
  return this;
}

static getRootAsLoggerFlat(bb:flatbuffers.ByteBuffer, obj?:LoggerFlat):LoggerFlat {
  return (obj || new LoggerFlat()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

static getSizePrefixedRootAsLoggerFlat(bb:flatbuffers.ByteBuffer, obj?:LoggerFlat):LoggerFlat {
  bb.setPosition(bb.position() + flatbuffers.SIZE_PREFIX_LENGTH);
  return (obj || new LoggerFlat()).__init(bb.readInt32(bb.position()) + bb.position(), bb);
}

containerType():LoggerContainer {
  const offset = this.bb!.__offset(this.bb_pos, 4);
  return offset ? this.bb!.readUint8(this.bb_pos + offset) : LoggerContainer.NONE;
}

container<T extends flatbuffers.Table>(obj:any):any|null {
  const offset = this.bb!.__offset(this.bb_pos, 6);
  return offset ? this.bb!.__union(obj, this.bb_pos + offset) : null;
}

code():number {
  const offset = this.bb!.__offset(this.bb_pos, 8);
  return offset ? this.bb!.readInt32(this.bb_pos + offset) : 0;
}

message():string|null
message(optionalEncoding:flatbuffers.Encoding):string|Uint8Array|null
message(optionalEncoding?:any):string|Uint8Array|null {
  const offset = this.bb!.__offset(this.bb_pos, 10);
  return offset ? this.bb!.__string(this.bb_pos + offset, optionalEncoding) : null;
}

success():boolean {
  const offset = this.bb!.__offset(this.bb_pos, 12);
  return offset ? !!this.bb!.readInt8(this.bb_pos + offset) : false;
}

static startLoggerFlat(builder:flatbuffers.Builder) {
  builder.startObject(5);
}

static addContainerType(builder:flatbuffers.Builder, containerType:LoggerContainer) {
  builder.addFieldInt8(0, containerType, LoggerContainer.NONE);
}

static addContainer(builder:flatbuffers.Builder, containerOffset:flatbuffers.Offset) {
  builder.addFieldOffset(1, containerOffset, 0);
}

static addCode(builder:flatbuffers.Builder, code:number) {
  builder.addFieldInt32(2, code, 0);
}

static addMessage(builder:flatbuffers.Builder, messageOffset:flatbuffers.Offset) {
  builder.addFieldOffset(3, messageOffset, 0);
}

static addSuccess(builder:flatbuffers.Builder, success:boolean) {
  builder.addFieldInt8(4, +success, +false);
}

static endLoggerFlat(builder:flatbuffers.Builder):flatbuffers.Offset {
  const offset = builder.endObject();
  return offset;
}

static finishLoggerFlatBuffer(builder:flatbuffers.Builder, offset:flatbuffers.Offset) {
  builder.finish(offset);
}

static finishSizePrefixedLoggerFlatBuffer(builder:flatbuffers.Builder, offset:flatbuffers.Offset) {
  builder.finish(offset, undefined, true);
}

static createLoggerFlat(builder:flatbuffers.Builder, containerType:LoggerContainer, containerOffset:flatbuffers.Offset, code:number, messageOffset:flatbuffers.Offset, success:boolean):flatbuffers.Offset {
  LoggerFlat.startLoggerFlat(builder);
  LoggerFlat.addContainerType(builder, containerType);
  LoggerFlat.addContainer(builder, containerOffset);
  LoggerFlat.addCode(builder, code);
  LoggerFlat.addMessage(builder, messageOffset);
  LoggerFlat.addSuccess(builder, success);
  return LoggerFlat.endLoggerFlat(builder);
}
}
