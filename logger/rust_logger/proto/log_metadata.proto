syntax = "proto3";

package logger;

// 日志元数据消息
// 与Android版本的LogMetaData.proto完全一致
message LogMetaData {
  string time = 1;        // 时间戳
  string sessionId = 2;   // 会话ID（与Android字段名一致）
  string tag = 3;         // 日志标签
  string level = 4;       // 日志级别
  string testMode = 5;    // 测试模式（与Android字段名一致）
  string userId = 6;      // 用户ID（与Android字段名一致）
  string logMessage = 7;  // 日志消息（与Android字段名一致）
}
