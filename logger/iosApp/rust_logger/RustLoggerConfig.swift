//
//  RustLoggerConfig.swift
//  iosApp
//
//  Created by Logger Team on 2024/12/10.
//

import Foundation

@objc public enum RustLogLevel: Int32 {
    case debug = 0
    case info = 1
    case warn = 2
    case error = 3
}

@objcMembers public class RustLoggerConfig: NSObject {
    
    // 基础配置
    public var logLevel: RustLogLevel = .warn
    public var enableConsoleOutput: Bool = false  // 默认关闭控制台输出（对应Android默认值）
    public var enableFullLog: Bool = false        // 全量日志开关（对应Android的enableFullLogs）
    public var testMode: Bool = false
    public var logEnv: String = "SC" // SC: 生产环境, YS: 验收环境
    public var disableSensitiveWords: Bool = false // 是否禁用脱敏
    
    // 用户信息
    public var userId: String = "0"
    public var deviceId: String = ""
    public var sessionId: String = ""
    public var appVersion: String = ""
    
    // 隐私配置
    public var privacyAgreed: Bool = false
    public var isDebugMode: Bool = false
    
    // 文件配置
    public var maxFileSize: Int64 = 20 * 1024 * 1024 // 20MB
    public var maxDirectorySize: Int64 = 600 * 1024 * 1024 // 600MB
    public var logFilePrefix: String = "uplog"
    public var logDirectory: String = "" // 必须设置

    // 格式化配置
    public var customPrefix: String = ""
    
    // 性能配置
    public var maxLogLength: Int32 = 4000
    public var maxLogsPerSecond: Int32 = 2000
    public var bufferSize: Int32 = 8192
    
    // iOS对齐的关键配置
    public var versionName: String = "" // 版本名称，用作一级目录
    
    public override init() {
        super.init()
    }
    
    /// 转换为JSON字符串
    public func toJsonString() -> String {
        let configDict: [String: Any] = [
            // 基础配置
            "log_level": logLevel.rawValue,
            "enable_console_output": enableConsoleOutput,
            "enable_full_log": enableFullLog,
            "test_mode": testMode,
            "log_env": logEnv,
            "disable_sensitive_words": disableSensitiveWords,
            
            // 用户信息
            "user_id": userId,
            "device_id": deviceId,
            "session_id": sessionId,
            "app_version": appVersion,
            
            // 隐私配置
            "privacy_agreed": privacyAgreed,
            "is_debug_mode": isDebugMode,
            
            // 文件配置
            "max_file_size": maxFileSize,
            "max_directory_size": maxDirectorySize,
            "log_file_prefix": logFilePrefix,
            "log_directory": logDirectory,

            // 格式化配置
            "custom_prefix": customPrefix,
            
            // 性能配置
            "max_log_length": maxLogLength,
            "max_logs_per_second": maxLogsPerSecond,
            "buffer_size": bufferSize,
            
            // iOS对齐的关键配置
            "version_name": versionName
        ]
        
        guard let jsonData = try? JSONSerialization.data(withJSONObject: configDict, options: []),
              let jsonString = String(data: jsonData, encoding: .utf8) else {
            return "{}"
        }
        
        return jsonString
    }
}
