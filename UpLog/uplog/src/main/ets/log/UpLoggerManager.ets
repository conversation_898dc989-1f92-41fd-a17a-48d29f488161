import { Logger } from './Logger';
import { LogLevel, LogLevelHelper } from './LogLevel';
import { preferences } from '@kit.ArkData';
import { BusinessError } from '@kit.BasicServicesKit';
import { common } from '@kit.AbilityKit';
import hilog from '@ohos.hilog';
import { UpLogConfig } from '../config/UpLogConfig';
// 导入rust_logger桥接层
import { RustLogger, LoggerResult } from '@uplus/rust_logger';

export class UpLoggerManager {
  private context?: common.Context;
  private static instance: UpLoggerManager;

  static getInstance(): UpLoggerManager {
    if (!UpLoggerManager.instance) {
      UpLoggerManager.instance = new UpLoggerManager();
    }
    return UpLoggerManager.instance;
  }

  private constructor() {
    // 纯粹的单例，无逻辑
  }

  initialize(context: common.Context, config?: UpLogConfig) {
    this.context = context.getApplicationContext();

    if (config) {
      // 调用rust_logger桥接层初始化
      RustLogger.initialize(context, config.toLoggerConfig());
    } else {
      // 使用默认配置初始化rust_logger
      const defaultConfig = UpLogConfig.builder().build();
      RustLogger.initialize(context, defaultConfig.toLoggerConfig());
    }
  }

  createLogger(tag: string): Logger {
    return new Logger(tag);
  }

  setLogLevel(level: LogLevel): void {
    // 注意：RustLogger没有单独的setLogLevel接口，日志级别通过初始化配置设置
  }

  getLogLevelSync(): LogLevel {
    // 注意：RustLogger没有getLogLevel接口，返回默认值
    return LogLevel.WARN;
  }

  getContext() {
    return this.context;
  }

  /**
   * 更新用户ID
   */
  updateUserId(userId: string): void {
    RustLogger.updateUserId(userId);
  }

  /**
   * 设置隐私协议状态
   */
  setPrivacyAgreed(agreed: boolean): void {
    // 注意：RustLogger没有单独的setPrivacyAgreed接口，隐私状态通过初始化配置设置
  }

  /**
   * 主动上传日志
   */
  uploadLogsManually(progressCallback?: (progress: number, message: string) => void): void {
    RustLogger.compressLogsForUpload(7, (result: LoggerResult<string>) => {
      if (result.success && result.data) {
        progressCallback?.(50, "压缩完成，开始上传...");
        // 这里应该调用前端的网络上传逻辑
        // 上传成功后调用 RustLogger.cleanupAfterUploadSuccess(result.data)
        // 上传失败后调用 RustLogger.cleanupAfterUploadFailure(result.data)
        progressCallback?.(100, "上传完成");
      } else {
        progressCallback?.(0, "压缩失败");
      }
    });
  }

  /**
   * 启动时检查并上传日志
   */
  checkAndUploadOnStartup(): boolean {
    // 注意：这个功能需要前端实现，Rust层只提供压缩接口
    return true;
  }

  /**
   * 写入崩溃日志
   */
  writeCrashLog(crashInfo: string): boolean {
    RustLogger.writeCrashLog(crashInfo);
    return true;
  }

  /**
   * 检查异常日志并上传
   */
  checkExceptionForUpload(): boolean {
    RustLogger.compressCrashLogs((result: LoggerResult<string>) => {
      if (result.success && result.data) {
        // 这里应该调用前端的网络上传逻辑
      }
    });
    return true;
  }

  /**
   * 启用控制台日志输出（异步执行）
   * 对应Android的enableConsoleLog()
   */
  enableConsoleLog(enable: boolean): void {
    RustLogger.enableConsoleLog(enable);
  }

  /**
   * 启用完整日志模式（异步执行）
   * 对应Android的enableFullLogs()
   */
  enableFullLogs(enable: boolean): void {
    RustLogger.enableFullLogs(enable);
  }

  /**
   * 获取控制台日志输出状态（异步执行）
   * 对应Android的isEnableConsole()
   */
  isConsoleLogEnabled(callback: (enabled: boolean) => void): void {
    RustLogger.isConsoleLogEnabled((result: LoggerResult<boolean>) => {
      if (result.success) {
        callback(result.data || false);
      } else {
        callback(false); // 默认值
      }
    });
  }

  /**
   * 获取完整日志模式状态（异步执行）
   * 对应Android的getFullLogsStatus()
   */
  isFullLogsEnabled(callback: (enabled: boolean) => void): void {
    RustLogger.isFullLogsEnabled((result: LoggerResult<boolean>) => {
      if (result.success) {
        callback(result.data || false);
      } else {
        callback(false); // 默认值
      }
    });
  }

  /**
   * 获取当前日志级别（异步执行）
   * 对应Android的getLoggerLevel()
   */
  getLogLevel(callback: (level: LogLevel) => void): void {
    RustLogger.getLogLevel((result: LoggerResult<LogLevel>) => {
      if (result.success) {
        callback(result.data || LogLevel.WARN);
      } else {
        callback(LogLevel.WARN); // 默认值
      }
    });
  }
}