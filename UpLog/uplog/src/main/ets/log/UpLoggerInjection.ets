import { UpLoggerManager } from './UpLoggerManager';
import { common } from '@kit.AbilityKit';
import { UpLogConfig } from '../config/UpLogConfig';
import { LogLevel } from '@uplus/rust_logger';
import { LogUploadUtils } from '../upload/LogUploadUtils';
import { LargeFileHandler } from '../upload/callback/LargeFileHandler';
import hilog from '@ohos.hilog';

export class UpLoggerInjection {
  /**
   * 初始化日志系统
   * @param context 应用上下文
   * @param config 可选的配置对象
   */
  static initialize(context: common.Context, config?: UpLogConfig) {
    UpLoggerManager.getInstance().initialize(context, config);

    // 设置大文件回调
    LargeFileHandler.setupLargeFileCallback();

    // 启动时检查未上传文件
    LogUploadUtils.checkPendingUploadsOnStartup();
  }

  /**
   * 获取日志管理器
   */
  static provideManager(): UpLoggerManager {
    return UpLoggerManager.getInstance();
  }

  /**
   * 快速配置日志系统
   * @param context 应用上下文
   * @param config 配置对象
   */
  static initializeWithConfig(context: common.Context, config: UpLogConfig) {
    UpLoggerManager.getInstance().initialize(context, config);
  }

  /**
   * 更新用户ID - 统一入口
   */
  static updateUserId(userId: string): void {
    // 调用rust_logger桥接层更新用户ID
    UpLoggerManager.getInstance().updateUserId(userId);
    hilog.info(0xFF, "UpLog", "Update user ID: %{public}s", userId);
  }

  /**
   * 设置隐私协议状态 - 统一入口
   */
  static setPrivacyAgreed(agreed: boolean): void {
    // 调用rust_logger桥接层设置隐私状态
    UpLoggerManager.getInstance().setPrivacyAgreed(agreed);
    hilog.info(0xFF, "UpLog", "Set privacy agreed: %{public}s", agreed.toString());
  }

  /**
   * 显示主动上传对话框 - 用户点击按钮触发
   * 压缩7天内的日志文件为ZIP包并上传
   * @param context 上下文
   * @param days 上传天数，默认7天
   */
  static showUploadDialog(context: common.Context, days: number = 7): void {
    hilog.info(0xFF, "UpLog", "Show upload dialog for %{public}d days", days);
    LogUploadUtils.showUploadDialog(context, days);
  }

  /**
   * 静默上传日志 - 无UI版本
   * @param days 上传天数，默认7天
   */
  static async uploadLogsManually(days: number = 7): Promise<boolean> {
    hilog.info(0xFF, "UpLog", "Manual log upload initiated for %{public}d days", days);

    try {
      await LogUploadUtils.uploadSilently(days);
      return true;
    } catch (error) {
      hilog.error(0xFF, "UpLog", "Manual upload failed: %{public}s", error.message);
      return false;
    }
  }

  /**
   * 启动时检查并上传日志
   * 检查崩溃日志和正常日志是否需要上传
   */
  static async checkAndUploadOnStartup(): Promise<boolean> {
    hilog.info(0xFF, "UpLog", "Startup log check initiated");

    try {
      // 检查未上传文件
      await LogUploadUtils.checkPendingUploadsOnStartup();

      // 检查并上传崩溃日志
      await LogUploadUtils.uploadCrashLogs();

      hilog.info(0xFF, "UpLog", "Startup log check completed successfully");
      return true;
    } catch (error) {
      hilog.error(0xFF, "UpLog", "Startup log check failed: %{public}s", error.message);
      return false;
    }
  }

  /**
   * 写入崩溃日志
   * App崩溃时调用此接口保存崩溃信息
   */
  static writeCrashLog(crashInfo: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 调用rust_logger桥接层写入崩溃日志
      const result = UpLoggerManager.getInstance().writeCrashLog(crashInfo);
      hilog.info(0xFF, "UpLog", "Crash log write result: %{public}s", result.toString());
      resolve(result);
    });
  }

  /**
   * 检查异常日志并上传
   * 对应Android的checkExceptionForUpload()
   */
  static async checkExceptionForUpload(): Promise<boolean> {
    hilog.info(0xFF, "UpLog", "Exception check initiated");

    try {
      await LogUploadUtils.uploadCrashLogs();
      hilog.info(0xFF, "UpLog", "Exception check completed successfully");
      return true;
    } catch (error) {
      hilog.error(0xFF, "UpLog", "Exception check failed: %{public}s", error.message);
      return false;
    }
  }

  /**
   * 启用控制台日志输出（异步执行）
   * 对应Android的enableConsoleLog()
   */
  static enableConsoleLog(enable: boolean): void {
    // 调用rust_logger桥接层设置控制台日志
    UpLoggerManager.getInstance().enableConsoleLog(enable);
    hilog.info(0xFF, "UpLog", "Console log enabled: %{public}s", enable.toString());
  }

  /**
   * 启用完整日志模式（异步执行）
   * 对应Android的enableFullLogs()
   * 当启用时，设置日志级别为DEBUG并输出所有日志到文件
   */
  static enableFullLogs(enable: boolean): void {
    // 调用rust_logger桥接层设置完整日志模式
    UpLoggerManager.getInstance().enableFullLogs(enable);
    hilog.info(0xFF, "UpLog", "Full logs enabled: %{public}s", enable.toString());
  }

  /**
   * 获取控制台日志输出状态（异步执行）
   * 对应Android的isEnableConsole()
   */
  static isEnableConsole(): Promise<boolean> {
    return new Promise((resolve) => {
      UpLoggerManager.getInstance().isConsoleLogEnabled((enabled) => {
        hilog.info(0xFF, "UpLog", "Console log enabled status: %{public}s", enabled.toString());
        resolve(enabled);
      });
    });
  }

  /**
   * 获取完整日志模式状态（异步执行）
   * 对应Android的getFullLogsStatus()
   */
  static getFullLogsStatus(): Promise<boolean> {
    return new Promise((resolve) => {
      UpLoggerManager.getInstance().isFullLogsEnabled((enabled) => {
        hilog.info(0xFF, "UpLog", "Full logs enabled status: %{public}s", enabled.toString());
        resolve(enabled);
      });
    });
  }

  /**
   * 获取当前日志级别（异步执行）
   * 对应Android的getLoggerLevel()
   */
  static getLoggerLevel(): Promise<LogLevel> {
    return new Promise((resolve) => {
      UpLoggerManager.getInstance().getLogLevel((level) => {
        hilog.info(0xFF, "UpLog", "Current log level: %{public}d", level);
        resolve(level);
      });
    });
  }
}