import { UpLoggerInjection } from '../log/UpLoggerInjection';
import { LogUploadUtils } from '../upload/LogUploadUtils';
import { LargeFileHandler } from '../upload/callback/LargeFileHandler';
import { common } from '@kit.AbilityKit';

/**
 * 日志上传功能使用示例
 * 展示如何在应用中集成和使用日志上传功能
 */
export class UploadExample {

  /**
   * 应用启动时的初始化示例
   * @param context 应用上下文
   */
  public static async initializeOnAppStart(context: common.Context): Promise<void> {
    // 1. 初始化日志系统（会自动设置大文件回调和检查未上传文件）
    UpLoggerInjection.initialize(context);
    
    // 2. 设置隐私协议状态
    UpLoggerInjection.setPrivacyAgreed(true);
    
    // 3. 启动时检查并上传日志
    const checkResult = await UpLoggerInjection.checkAndUploadOnStartup();
    console.log(`启动检查结果: ${checkResult}`);
  }

  /**
   * 用户主动上传日志示例（带UI）
   * @param context 上下文
   */
  public static showUploadDialogExample(context: common.Context): void {
    // 显示上传对话框，上传最近7天的日志
    UpLoggerInjection.showUploadDialog(context, 7);
  }

  /**
   * 静默上传日志示例（无UI）
   */
  public static async silentUploadExample(): Promise<void> {
    try {
      // 静默上传最近3天的日志
      const success = await UpLoggerInjection.uploadLogsManually(3);
      console.log(`静默上传结果: ${success}`);
    } catch (error) {
      console.error(`静默上传失败: ${error.message}`);
    }
  }

  /**
   * 处理大文件通知示例
   */
  public static setupLargeFileHandling(): void {
    // 大文件处理已在初始化时自动设置
    // 当文件达到20MB时，会自动触发LargeFileHandler.handleLargeFile()
    console.log("大文件处理已设置完成");
  }

  /**
   * 崩溃日志处理示例
   */
  public static async handleCrashExample(): Promise<void> {
    try {
      // 1. 写入崩溃日志
      const crashInfo = `
        崩溃时间: ${new Date().toISOString()}
        错误信息: 应用异常退出
        堆栈信息: [详细堆栈信息]
      `;
      
      const writeResult = await UpLoggerInjection.writeCrashLog(crashInfo);
      console.log(`崩溃日志写入结果: ${writeResult}`);
      
      // 2. 上传崩溃日志
      await LogUploadUtils.uploadCrashLogs();
      console.log("崩溃日志上传完成");
      
    } catch (error) {
      console.error(`崩溃日志处理失败: ${error.message}`);
    }
  }

  /**
   * 自定义上传回调示例
   */
  public static async customUploadCallbackExample(): Promise<void> {
    await LogUploadUtils.uploadSilently(7, {
      progress: (progress: number) => {
        console.log(`上传进度: ${progress}%`);
      },
      
      onSuccess: () => {
        console.log("上传成功！");
        // 可以显示成功通知
      },
      
      onFailed: (error: Error) => {
        console.error(`上传失败: ${error.message}`);
        // 可以显示错误通知
      },
      
      onComplete: () => {
        console.log("上传完成");
        // 清理UI状态
      }
    });
  }

  /**
   * 配置检查示例
   */
  public static async checkConfigurationExample(): Promise<void> {
    // 检查各种配置状态
    const consoleEnabled = await UpLoggerInjection.isEnableConsole();
    const fullLogsEnabled = await UpLoggerInjection.getFullLogsStatus();
    const logLevel = await UpLoggerInjection.getLoggerLevel();
    
    console.log(`控制台日志: ${consoleEnabled}`);
    console.log(`完整日志: ${fullLogsEnabled}`);
    console.log(`日志级别: ${logLevel}`);
    
    // 根据配置决定是否启用自动上传
    if (fullLogsEnabled) {
      console.log("完整日志模式已启用，支持自动上传");
    }
  }

  /**
   * 完整的应用生命周期集成示例
   */
  public static class AppLifecycleIntegration {
    
    /**
     * 应用启动
     */
    public static async onAppStart(context: common.Context): Promise<void> {
      // 初始化日志系统
      await UploadExample.initializeOnAppStart(context);
      
      // 检查配置
      await UploadExample.checkConfigurationExample();
    }
    
    /**
     * 用户登录后
     */
    public static onUserLogin(userId: string): void {
      // 更新用户ID
      UpLoggerInjection.updateUserId(userId);
      
      // 设置隐私协议状态
      UpLoggerInjection.setPrivacyAgreed(true);
    }
    
    /**
     * 应用进入后台
     */
    public static async onAppBackground(): Promise<void> {
      // 可以在后台触发日志上传
      try {
        await UploadExample.silentUploadExample();
      } catch (error) {
        console.error("后台上传失败:", error);
      }
    }
    
    /**
     * 应用崩溃处理
     */
    public static async onAppCrash(crashInfo: string): Promise<void> {
      try {
        // 写入崩溃日志
        await UpLoggerInjection.writeCrashLog(crashInfo);
        
        // 立即尝试上传崩溃日志
        await LogUploadUtils.uploadCrashLogs();
      } catch (error) {
        console.error("崩溃日志处理失败:", error);
      }
    }
    
    /**
     * 用户手动上传
     */
    public static onUserRequestUpload(context: common.Context): void {
      // 显示上传对话框
      UploadExample.showUploadDialogExample(context);
    }
  }
}
