import { AppInfo } from '@uplus/upbase';
import { ServerEnv } from '@uplus/upbase/src/main/ets/ServerEnv';
import deviceInfo from '@ohos.deviceInfo';

/**
 * 应用信息帮助类
 * 基于UpBase提供应用信息，替代Android的AppInfoProvider
 */
export class AppInfoHelper {
  /**
   * 获取应用ID
   */
  public static getAppId(): string {
    return AppInfo.getAppId();
  }

  /**
   * 获取客户端ID（同步方式）
   */
  public static getClientId(): string {
    return AppInfo.getClientIdSync();
  }

  /**
   * 获取客户端ID（异步方式）
   */
  public static async getClientIdAsync(): Promise<string> {
    return await AppInfo.getClientId();
  }

  /**
   * 获取版本名称
   */
  public static getVersionName(): string {
    return AppInfo.getVersionName();
  }

  /**
   * 获取版本号
   */
  public static getVersionCode(): number {
    return AppInfo.getVersionCode();
  }

  /**
   * 获取UPM应用ID
   * 根据实际业务配置
   */
  public static getUpmAppId(): string {
    // 可以从配置中读取，这里使用默认值
    return "smarthome";
  }

  /**
   * 获取日志环境
   */
  public static getLogEnv(): ServerEnv {
    return AppInfo.getServerEnv();
  }

  /**
   * 获取系统信息
   */
  public static getSystemInfo(): string {
    return `HarmonyOS ${deviceInfo.osFullName}`;
  }

  /**
   * 获取设备型号信息
   */
  public static getModelInfo(): string {
    return `${deviceInfo.brand} ${deviceInfo.productModel}`;
  }

  /**
   * 构建上传请求头参数
   * 对应Android的LogUploader.buildHeaderParam()
   */
  public static buildHeaderParams(fileName: string): Record<string, string> {
    const params: Record<string, string> = {};
    
    // 文件信息
    params["filename"] = fileName;
    
    // 应用信息
    params["appId"] = AppInfoHelper.getAppId();
    params["clientId"] = AppInfoHelper.getClientId();
    params["appVersion"] = AppInfoHelper.getVersionName();
    params["buildId"] = AppInfoHelper.getVersionCode().toString();
    params["upmAppId"] = AppInfoHelper.getUpmAppId();
    
    // 系统信息
    params["timestamp"] = Date.now().toString();
    params["systemInfo"] = AppInfoHelper.getSystemInfo();
    params["modelInfo"] = AppInfoHelper.getModelInfo();
    
    return params;
  }
}
