import { LogUploadUtils } from '../LogUploadUtils';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 大文件处理器
 * 处理Rust日志库的大文件回调通知
 */
export class LargeFileHandler {
  private static readonly TAG = "LargeFileHandler";

  /**
   * 处理大文件通知
   * 当日志文件达到20MB时，Rust库会调用此方法
   * @param filePath 达到大小限制的文件路径
   */
  public static handleLargeFile(filePath: string): void {
    hilog.info(0x0000, LargeFileHandler.TAG, `收到大文件通知: ${filePath}`);
    
    try {
      // 检查隐私协议和日志级别
      if (!LargeFileHandler.shouldAutoUpload()) {
        hilog.info(0x0000, LargeFileHandler.TAG, "不满足自动上传条件，跳过上传");
        return;
      }
      
      // 异步处理上传，避免阻塞
      setTimeout(() => {
        LogUploadUtils.handleLargeFileUpload(filePath);
      }, 100);
      
    } catch (error) {
      hilog.error(0x0000, LargeFileHandler.TAG, `处理大文件通知失败: ${error.message}`);
    }
  }

  /**
   * 检查是否应该自动上传
   * 对应Android的自动上传条件检查
   */
  private static shouldAutoUpload(): boolean {
    try {
      // TODO: 从配置中读取实际的隐私协议状态和日志级别
      // 这里暂时返回true，实际应该检查：
      // 1. 隐私协议已同意
      // 2. 日志级别为DEBUG
      // 3. 网络状态良好
      
      const privacyAgreed = true; // 从配置读取
      const isDebugLevel = true;  // 从配置读取
      const hasNetwork = true;    // 检查网络状态
      
      const shouldUpload = privacyAgreed && isDebugLevel && hasNetwork;
      
      hilog.info(0x0000, LargeFileHandler.TAG, 
        `自动上传条件检查: 隐私协议=${privacyAgreed}, DEBUG级别=${isDebugLevel}, 网络=${hasNetwork}, 结果=${shouldUpload}`);
      
      return shouldUpload;
      
    } catch (error) {
      hilog.error(0x0000, LargeFileHandler.TAG, `检查自动上传条件失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 批量处理多个大文件
   * @param filePaths 文件路径数组
   */
  public static handleMultipleLargeFiles(filePaths: string[]): void {
    hilog.info(0x0000, LargeFileHandler.TAG, `批量处理${filePaths.length}个大文件`);
    
    if (!LargeFileHandler.shouldAutoUpload()) {
      hilog.info(0x0000, LargeFileHandler.TAG, "不满足自动上传条件，跳过批量上传");
      return;
    }
    
    // 逐个处理，避免并发过多
    filePaths.forEach((filePath, index) => {
      setTimeout(() => {
        LargeFileHandler.handleLargeFile(filePath);
      }, index * 1000); // 每个文件间隔1秒处理
    });
  }

  /**
   * 设置大文件回调到Rust库
   * 在应用初始化时调用
   */
  public static setupLargeFileCallback(): void {
    hilog.info(0x0000, LargeFileHandler.TAG, "设置大文件回调到Rust库");
    
    try {
      // TODO: 调用Rust库的设置回调方法
      // RustLogger.setLargeFileCallback(LargeFileHandler.handleLargeFile);
      
      hilog.info(0x0000, LargeFileHandler.TAG, "大文件回调设置成功");
    } catch (error) {
      hilog.error(0x0000, LargeFileHandler.TAG, `设置大文件回调失败: ${error.message}`);
    }
  }
}
