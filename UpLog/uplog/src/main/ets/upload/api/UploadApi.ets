import { AppInfo } from '@uplus/upbase';
import { ServerEnv } from '@uplus/upbase/src/main/ets/ServerEnv';
import { OkHttpClient, Request, RequestBody, UpCloud } from '@uplus/upcloud';
import { UploadResponse } from '../model/UploadResponse';

/**
 * 日志上传API接口
 * 对应Android的UploadApi.java
 */
export class UploadApi {
  /**
   * 生产环境baseUrl
   */
  private static readonly SC_BASE_URL = "https://zj.haier.net/applog/v1/";

  /**
   * 验收环境baseUrl
   */
  private static readonly YS_BASE_URL = "https://zj-yanshou.haier.net/applog/v1/";

  /**
   * 上传接口路径
   */
  private static readonly UPLOAD_PATH = "upload/logfile";

  /**
   * 获取基础服务器URL
   */
  public static getBaseServerUrl(): string {
    switch (AppInfo.getServerEnv()) {
      case ServerEnv.EV_YS:
        return UploadApi.YS_BASE_URL;
      default:
        return UploadApi.SC_BASE_URL;
    }
  }

  /**
   * 获取完整的上传URL
   */
  public static getUploadUrl(): string {
    return UploadApi.getBaseServerUrl() + UploadApi.UPLOAD_PATH;
  }

  /**
   * 上传日志文件
   * @param headerParams 请求头参数
   * @param filePath 文件路径
   * @returns Promise<UploadResponse>
   */
  public static async uploadLogFile(
    headerParams: Record<string, string>, 
    filePath: string
  ): Promise<UploadResponse> {
    const client: OkHttpClient = UpCloud.getInstance().getHttpClient(UploadApi.getBaseServerUrl());
    
    const request = new Request.Builder()
      .url(UploadApi.getUploadUrl())
      .post(RequestBody.createMultipart(filePath, "file"))
      .headers(headerParams)
      .build();

    return client.execRequest(request, UploadResponse);
  }
}
