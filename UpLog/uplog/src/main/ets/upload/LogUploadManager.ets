import { UploadApi } from './api/UploadApi';
import { UpLoadCallback } from './callback/UpLoadCallback';
import { AppInfoHelper } from './helper/AppInfoHelper';
import { CommonResponse } from '@uplus/upcloud';
import { RustLogger } from '../RustLogger';
import fs from '@ohos.file.fs';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 日志上传管理器
 * 对应Android的LogUploader.java
 */
export class LogUploadManager {
  private static readonly TAG = "LogUploadManager";

  /**
   * 主动上传日志（用户触发）
   * 对应Android的uploadLogsManually()
   * @param days 上传最近几天的日志，默认7天
   * @param callback 上传回调
   */
  public static async uploadLogsManually(days: number = 7, callback: UpLoadCallback): Promise<void> {
    hilog.info(0x0000, LogUploadManager.TAG, `开始主动上传最近${days}天的日志`);
    
    try {
      callback.progress(10);
      
      // 1. 调用Rust日志库压缩接口
      hilog.info(0x0000, LogUploadManager.TAG, "调用Rust日志库压缩接口");
      const zipFilePath = await RustLogger.compressLogsForUpload(days);
      
      if (!zipFilePath || zipFilePath.length === 0) {
        throw new Error("压缩日志失败：未生成压缩文件");
      }
      
      callback.progress(30);
      
      // 2. 检查压缩文件是否存在
      if (!fs.accessSync(zipFilePath)) {
        throw new Error(`压缩文件不存在: ${zipFilePath}`);
      }
      
      callback.progress(40);
      
      // 3. 执行上传
      hilog.info(0x0000, LogUploadManager.TAG, `开始上传压缩文件: ${zipFilePath}`);
      await LogUploadManager.uploadZipFile(zipFilePath, callback);
      
      callback.progress(90);
      
      // 4. 上传成功，清理文件
      hilog.info(0x0000, LogUploadManager.TAG, "上传成功，清理文件");
      await RustLogger.cleanupAfterUploadSuccess(zipFilePath);
      
      callback.progress(100);
      callback.onSuccess();
      
    } catch (error) {
      hilog.error(0x0000, LogUploadManager.TAG, `主动上传失败: ${error.message}`);
      callback.onFailed(error as Error);
    } finally {
      callback.onComplete();
    }
  }

  /**
   * 大文件自动上传（20MB文件触发）
   * 对应Android的processWhenFullMaxSize()
   * @param filePath 达到大小限制的文件路径
   * @param callback 上传回调
   */
  public static async uploadLargeFile(filePath: string, callback: UpLoadCallback): Promise<void> {
    hilog.info(0x0000, LogUploadManager.TAG, `大文件自动上传: ${filePath}`);
    
    try {
      callback.progress(10);
      
      // 1. 压缩单个大文件
      const zipFilePath = await RustLogger.compressSingleFile(filePath);
      
      if (!zipFilePath || zipFilePath.length === 0) {
        throw new Error("压缩大文件失败");
      }
      
      callback.progress(30);
      
      // 2. 上传压缩文件
      await LogUploadManager.uploadZipFile(zipFilePath, callback);
      
      callback.progress(90);
      
      // 3. 上传成功，清理文件
      await RustLogger.cleanupAfterUploadSuccess(zipFilePath);
      
      callback.progress(100);
      callback.onSuccess();
      
    } catch (error) {
      hilog.error(0x0000, LogUploadManager.TAG, `大文件上传失败: ${error.message}`);
      
      // 上传失败，只删除ZIP文件，保留原始文件
      try {
        const zipFilePath = await RustLogger.compressSingleFile(filePath);
        if (zipFilePath) {
          await RustLogger.cleanupAfterUploadFailure(zipFilePath);
        }
      } catch (cleanupError) {
        hilog.error(0x0000, LogUploadManager.TAG, `清理失败文件出错: ${cleanupError.message}`);
      }
      
      callback.onFailed(error as Error);
    } finally {
      callback.onComplete();
    }
  }

  /**
   * 崩溃日志上传
   * 对应Android的uploadRelatedLogWhenCrash()
   * @param callback 上传回调
   */
  public static async uploadCrashLogs(callback: UpLoadCallback): Promise<void> {
    hilog.info(0x0000, LogUploadManager.TAG, "开始上传崩溃日志");
    
    try {
      callback.progress(10);
      
      // 1. 压缩崩溃日志
      const zipFilePath = await RustLogger.compressCrashLogs();
      
      if (!zipFilePath || zipFilePath.length === 0) {
        hilog.info(0x0000, LogUploadManager.TAG, "没有崩溃日志需要上传");
        callback.progress(100);
        callback.onSuccess();
        return;
      }
      
      callback.progress(30);
      
      // 2. 上传崩溃日志
      await LogUploadManager.uploadZipFile(zipFilePath, callback);
      
      callback.progress(90);
      
      // 3. 上传成功，清理文件
      await RustLogger.cleanupAfterUploadSuccess(zipFilePath);
      
      callback.progress(100);
      callback.onSuccess();
      
    } catch (error) {
      hilog.error(0x0000, LogUploadManager.TAG, `崩溃日志上传失败: ${error.message}`);
      callback.onFailed(error as Error);
    } finally {
      callback.onComplete();
    }
  }

  /**
   * 上传ZIP文件的核心方法
   * @param zipFilePath ZIP文件路径
   * @param callback 上传回调
   */
  private static async uploadZipFile(zipFilePath: string, callback: UpLoadCallback): Promise<void> {
    // 1. 构建请求头参数
    const fileName = zipFilePath.substring(zipFilePath.lastIndexOf('/') + 1);
    const headerParams = AppInfoHelper.buildHeaderParams(fileName);
    
    hilog.info(0x0000, LogUploadManager.TAG, `上传文件: ${fileName}`);
    hilog.info(0x0000, LogUploadManager.TAG, `请求头参数: ${JSON.stringify(headerParams)}`);
    
    callback.progress(50);
    
    // 2. 调用上传API
    const response: CommonResponse = await UploadApi.uploadLogFile(headerParams, zipFilePath);
    
    callback.progress(80);
    
    // 3. 检查响应结果
    if (response.isSuccess()) {
      hilog.info(0x0000, LogUploadManager.TAG, `上传成功: ${response.toString()}`);
    } else {
      throw new Error(`上传失败: ${response.toString()}`);
    }
  }

  /**
   * 启动时检查未上传的文件
   * 对应Android的checkNeedToUploadLogFiles()
   */
  public static async checkPendingUploads(): Promise<void> {
    hilog.info(0x0000, LogUploadManager.TAG, "检查启动时未上传的文件");
    
    try {
      // 获取未上传的ZIP文件列表
      const pendingFiles = await RustLogger.getPendingUploadFiles();
      
      if (pendingFiles && pendingFiles.length > 0) {
        hilog.info(0x0000, LogUploadManager.TAG, `发现${pendingFiles.length}个未上传文件，开始后台上传`);
        
        // 后台静默上传
        for (const filePath of pendingFiles) {
          try {
            await LogUploadManager.uploadZipFile(filePath, {
              onSuccess: () => {
                hilog.info(0x0000, LogUploadManager.TAG, `后台上传成功: ${filePath}`);
                RustLogger.cleanupAfterUploadSuccess(filePath);
              },
              onFailed: (error: Error) => {
                hilog.error(0x0000, LogUploadManager.TAG, `后台上传失败: ${filePath}, ${error.message}`);
                RustLogger.cleanupAfterUploadFailure(filePath);
              },
              progress: () => {}, // 后台上传不需要进度
              onComplete: () => {}
            });
          } catch (error) {
            hilog.error(0x0000, LogUploadManager.TAG, `后台上传异常: ${filePath}, ${error.message}`);
          }
        }
      } else {
        hilog.info(0x0000, LogUploadManager.TAG, "没有未上传的文件");
      }
    } catch (error) {
      hilog.error(0x0000, LogUploadManager.TAG, `检查未上传文件失败: ${error.message}`);
    }
  }
}
