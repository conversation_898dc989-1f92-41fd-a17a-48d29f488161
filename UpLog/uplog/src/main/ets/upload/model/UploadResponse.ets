import { CommonResponse } from '@uplus/upcloud';

/**
 * 上传响应模型
 * 基于UpCloud的CommonResponse，对应Android的ResponseBean.java
 */
export class UploadResponse extends CommonResponse {
  /**
   * 是否上传成功
   * 重写父类方法，保持与UpCloud一致的成功判断逻辑
   */
  public isSuccess(): boolean {
    return this.getRetCode() === CommonResponse.SUCCESS_CODE; // "00000"
  }

  /**
   * 转换为字符串
   */
  public toString(): string {
    return `UploadResponse{retCode='${this.getRetCode()}', retInfo='${this.getRetInfo()}', isSuccess=${this.isSuccess()}}`;
  }
}
