import { LogUploadManager } from '../LogUploadManager';
import { UpLoadCallback } from '../callback/UpLoadCallback';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 日志上传对话框组件
 * 主动上传时显示进度和结果
 */
@CustomDialog
export struct LogUploadDialog {
  private static readonly TAG = "LogUploadDialog";
  
  // 对话框控制器
  controller: CustomDialogController;
  
  // 上传状态
  @State uploadProgress: number = 0;
  @State uploadStatus: string = "准备上传...";
  @State isUploading: boolean = false;
  @State isCompleted: boolean = false;
  @State isSuccess: boolean = false;
  @State errorMessage: string = "";
  
  // 上传天数，默认7天
  private uploadDays: number = 7;

  build() {
    Column({ space: 20 }) {
      // 标题
      Text("日志上传")
        .fontSize(18)
        .fontWeight(FontWeight.Bold)
        .fontColor($r('app.color.text_primary'))
      
      // 上传状态区域
      Column({ space: 15 }) {
        // 状态文本
        Text(this.uploadStatus)
          .fontSize(14)
          .fontColor($r('app.color.text_secondary'))
          .textAlign(TextAlign.Center)
        
        // 进度条（上传中时显示）
        if (this.isUploading) {
          Column({ space: 8 }) {
            Progress({
              value: this.uploadProgress,
              total: 100,
              type: ProgressType.Linear
            })
              .width('100%')
              .height(6)
              .color($r('app.color.brand_primary'))
              .backgroundColor($r('app.color.background_secondary'))
            
            Text(`${this.uploadProgress}%`)
              .fontSize(12)
              .fontColor($r('app.color.text_tertiary'))
          }
        }
        
        // 错误信息（失败时显示）
        if (this.isCompleted && !this.isSuccess && this.errorMessage) {
          Text(this.errorMessage)
            .fontSize(12)
            .fontColor($r('app.color.error'))
            .textAlign(TextAlign.Center)
            .maxLines(3)
            .textOverflow({ overflow: TextOverflow.Ellipsis })
        }
        
        // 成功图标（成功时显示）
        if (this.isCompleted && this.isSuccess) {
          Image($r('app.media.ic_success'))
            .width(48)
            .height(48)
            .fillColor($r('app.color.success'))
        }
      }
      .width('100%')
      .minHeight(80)
      .justifyContent(FlexAlign.Center)
      
      // 按钮区域
      Row({ space: 12 }) {
        if (!this.isUploading) {
          // 取消按钮
          Button("取消")
            .fontSize(14)
            .fontColor($r('app.color.text_secondary'))
            .backgroundColor($r('app.color.background_secondary'))
            .borderRadius(8)
            .layoutWeight(1)
            .onClick(() => {
              this.controller.close();
            })
        }
        
        if (!this.isCompleted) {
          // 开始上传按钮
          Button(this.isUploading ? "上传中..." : "开始上传")
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isUploading ? $r('app.color.button_disabled') : $r('app.color.brand_primary'))
            .borderRadius(8)
            .layoutWeight(1)
            .enabled(!this.isUploading)
            .onClick(() => {
              this.startUpload();
            })
        } else {
          // 完成按钮
          Button(this.isSuccess ? "完成" : "重试")
            .fontSize(14)
            .fontColor(Color.White)
            .backgroundColor(this.isSuccess ? $r('app.color.success') : $r('app.color.brand_primary'))
            .borderRadius(8)
            .layoutWeight(1)
            .onClick(() => {
              if (this.isSuccess) {
                this.controller.close();
              } else {
                this.resetAndRetry();
              }
            })
        }
      }
      .width('100%')
    }
    .width(280)
    .padding(24)
    .backgroundColor(Color.White)
    .borderRadius(12)
  }

  /**
   * 开始上传
   */
  private startUpload(): void {
    hilog.info(0x0000, LogUploadDialog.TAG, `开始上传最近${this.uploadDays}天的日志`);
    
    this.isUploading = true;
    this.isCompleted = false;
    this.uploadProgress = 0;
    this.uploadStatus = "正在压缩日志文件...";
    this.errorMessage = "";
    
    // 创建上传回调
    const callback: UpLoadCallback = {
      progress: (progress: number) => {
        this.uploadProgress = progress;
        
        if (progress <= 30) {
          this.uploadStatus = "正在压缩日志文件...";
        } else if (progress <= 50) {
          this.uploadStatus = "正在上传文件...";
        } else if (progress <= 90) {
          this.uploadStatus = "上传中，请稍候...";
        } else {
          this.uploadStatus = "正在清理临时文件...";
        }
      },
      
      onSuccess: () => {
        hilog.info(0x0000, LogUploadDialog.TAG, "日志上传成功");
        this.isUploading = false;
        this.isCompleted = true;
        this.isSuccess = true;
        this.uploadProgress = 100;
        this.uploadStatus = "上传成功！";
      },
      
      onFailed: (error: Error) => {
        hilog.error(0x0000, LogUploadDialog.TAG, `日志上传失败: ${error.message}`);
        this.isUploading = false;
        this.isCompleted = true;
        this.isSuccess = false;
        this.uploadStatus = "上传失败";
        this.errorMessage = error.message;
      },
      
      onComplete: () => {
        hilog.info(0x0000, LogUploadDialog.TAG, "日志上传完成");
      }
    };
    
    // 调用上传管理器
    LogUploadManager.uploadLogsManually(this.uploadDays, callback);
  }

  /**
   * 重置状态并重试
   */
  private resetAndRetry(): void {
    this.isUploading = false;
    this.isCompleted = false;
    this.isSuccess = false;
    this.uploadProgress = 0;
    this.uploadStatus = "准备上传...";
    this.errorMessage = "";
    
    // 延迟一下再开始上传，给用户反馈
    setTimeout(() => {
      this.startUpload();
    }, 500);
  }

  /**
   * 设置上传天数
   */
  public setUploadDays(days: number): void {
    this.uploadDays = days;
  }
}
