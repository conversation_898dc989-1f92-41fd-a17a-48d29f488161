import { LogUploadDialog } from './dialog/LogUploadDialog';
import { LogUploadManager } from './LogUploadManager';
import { UpLoadCallback } from './callback/UpLoadCallback';
import { hilog } from '@kit.PerformanceAnalysisKit';

/**
 * 日志上传工具类
 * 提供便捷的上传入口方法
 */
export class LogUploadUtils {
  private static readonly TAG = "LogUploadUtils";

  /**
   * 显示主动上传对话框
   * @param context 上下文
   * @param days 上传天数，默认7天
   */
  public static showUploadDialog(context: Context, days: number = 7): void {
    hilog.info(0x0000, LogUploadUtils.TAG, `显示上传对话框，上传${days}天日志`);
    
    const dialogController = new CustomDialogController({
      builder: LogUploadDialog({
        controller: null as any // 会在下面设置
      }),
      alignment: DialogAlignment.Center,
      autoCancel: false,
      customStyle: true
    });
    
    // 设置控制器引用
    const dialog = dialogController.builder as LogUploadDialog;
    dialog.controller = dialogController;
    dialog.setUploadDays(days);
    
    dialogController.open();
  }

  /**
   * 静默上传日志（无UI）
   * @param days 上传天数，默认7天
   * @param callback 可选的回调
   */
  public static async uploadSilently(days: number = 7, callback?: UpLoadCallback): Promise<void> {
    hilog.info(0x0000, LogUploadUtils.TAG, `静默上传${days}天日志`);
    
    const defaultCallback: UpLoadCallback = {
      progress: (progress: number) => {
        hilog.debug(0x0000, LogUploadUtils.TAG, `上传进度: ${progress}%`);
      },
      onSuccess: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, "静默上传成功");
      },
      onFailed: (error: Error) => {
        hilog.error(0x0000, LogUploadUtils.TAG, `静默上传失败: ${error.message}`);
      },
      onComplete: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, "静默上传完成");
      }
    };
    
    const finalCallback = callback || defaultCallback;
    await LogUploadManager.uploadLogsManually(days, finalCallback);
  }

  /**
   * 处理大文件上传通知
   * 当文件达到20MB时由Rust库回调触发
   * @param filePath 大文件路径
   */
  public static handleLargeFileUpload(filePath: string): void {
    hilog.info(0x0000, LogUploadUtils.TAG, `处理大文件上传: ${filePath}`);
    
    const callback: UpLoadCallback = {
      progress: (progress: number) => {
        hilog.debug(0x0000, LogUploadUtils.TAG, `大文件上传进度: ${progress}%`);
      },
      onSuccess: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, `大文件上传成功: ${filePath}`);
        // 可以发送通知给用户
        LogUploadUtils.showUploadNotification("日志文件上传成功", true);
      },
      onFailed: (error: Error) => {
        hilog.error(0x0000, LogUploadUtils.TAG, `大文件上传失败: ${filePath}, ${error.message}`);
        // 可以发送错误通知给用户
        LogUploadUtils.showUploadNotification("日志文件上传失败", false);
      },
      onComplete: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, `大文件上传完成: ${filePath}`);
      }
    };
    
    LogUploadManager.uploadLargeFile(filePath, callback);
  }

  /**
   * 上传崩溃日志
   */
  public static async uploadCrashLogs(): Promise<void> {
    hilog.info(0x0000, LogUploadUtils.TAG, "上传崩溃日志");
    
    const callback: UpLoadCallback = {
      progress: (progress: number) => {
        hilog.debug(0x0000, LogUploadUtils.TAG, `崩溃日志上传进度: ${progress}%`);
      },
      onSuccess: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, "崩溃日志上传成功");
      },
      onFailed: (error: Error) => {
        hilog.error(0x0000, LogUploadUtils.TAG, `崩溃日志上传失败: ${error.message}`);
      },
      onComplete: () => {
        hilog.info(0x0000, LogUploadUtils.TAG, "崩溃日志上传完成");
      }
    };
    
    await LogUploadManager.uploadCrashLogs(callback);
  }

  /**
   * 应用启动时检查未上传文件
   */
  public static async checkPendingUploadsOnStartup(): Promise<void> {
    hilog.info(0x0000, LogUploadUtils.TAG, "应用启动检查未上传文件");
    
    try {
      await LogUploadManager.checkPendingUploads();
    } catch (error) {
      hilog.error(0x0000, LogUploadUtils.TAG, `启动检查失败: ${error.message}`);
    }
  }

  /**
   * 显示上传通知
   * @param message 通知消息
   * @param isSuccess 是否成功
   */
  private static showUploadNotification(message: string, isSuccess: boolean): void {
    // 这里可以集成系统通知或Toast
    // 暂时使用hilog记录
    if (isSuccess) {
      hilog.info(0x0000, LogUploadUtils.TAG, `通知: ${message}`);
    } else {
      hilog.warn(0x0000, LogUploadUtils.TAG, `通知: ${message}`);
    }
    
    // TODO: 集成实际的通知系统
    // NotificationManager.showToast(message);
  }

  /**
   * 获取上传统计信息
   */
  public static async getUploadStats(): Promise<{
    totalUploaded: number;
    lastUploadTime: number;
    pendingFiles: number;
  }> {
    // TODO: 实现上传统计功能
    return {
      totalUploaded: 0,
      lastUploadTime: 0,
      pendingFiles: 0
    };
  }
}
